.RenewalLinkPopup {
    .MuiDialog-paperWidthSm {
      padding-bottom: 0px;
      height: auto;
    }
    .justifyCenter {
      justify-content: center;
    }
    .mt-3 {
      margin-top: 8px;
    }
  
    .GenerateBtn {
      background: #0065ff;
      border-radius: 8px;
      letter-spacing: 0.17px;
      padding: 8px;
      width: 150px;
      border: none;
      color: #ffffff;
      font: normal normal 600 12px/21px Roboto;
      cursor: pointer;
      outline: none;
      margin-top: 10px;
    }
      
  
    .Note {
      color: #df1f1f;
      font: normal normal 600 12px/15px Roboto;
      margin: 15px 0px;
    }
    .button-txt {
      background: #ffffff;
      border-radius: 8px;
      letter-spacing: 0.17px;
      padding: 8px;
      width: 150px;
      font: normal normal 600 12px/21px Roboto;
      cursor: pointer;
      outline: none;
      margin-top: 10px;
      color: #0065ff;
      border-color: #0065ff;
    }    
  
    span {
      float: right;
      font: normal normal 500 12px/24px Roboto;
      margin-right: 35px;
    }
    .custom-disabled-input input:disabled {
      background: #e5e5e5 !important;
      border-radius: 12px !important;
      border: none !important;
      color: #6b6764 !important;
    }
  }
  
  .RenewalLinkPopupHistory {  
    .mt-3 {
      margin-top: 8px;
    }
    .button-txt {
      background: #ffffff;
      border-radius: 8px;
      letter-spacing: 0.17px;
      padding: 8px;
      width: auto;
      font: normal normal 600 12px/21px Roboto;
      cursor: pointer;
      outline: none;
      margin: 5px;
      color: #0065ff;
      border-color: #0065ff;
    }
      
    .button {
      background: #ffffff;
      border-radius: 8px;
      letter-spacing: 0.17px;
      padding: 8px;
      width: 150px;
      font: normal normal 600 12px/21px Roboto;
      cursor: pointer;
      outline: none;
      color: #0065ff;
      border-color: #0065ff;
    }
      
    .pending-button-txt {
      background: #ffffff;
      border-radius: 8px;
      letter-spacing: 0.17px;
      padding: 8px;
      width: 150px;
      font: normal normal 600 12px/21px Roboto;
      cursor: pointer;
      outline: none;
      margin: 5px;
    }
    .disablebtn{
      cursor: default !important;
    }
    .TablePagination{
      .MuiTablePagination-input{
        margin-right: 8px !important;
      }
     
      .MuiButtonBase-root{
        position: static;
        background-color: transparent !important;
      }
    }
    
  }
  