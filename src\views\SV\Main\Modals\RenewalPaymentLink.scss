.RenewalLinkPopup {
  .MuiDialog-paperWidthSm {
    padding-bottom: 0px;
    height: auto;
    width: 600px;
    box-shadow: 0px 0px 16px #00000014;
    border-radius: 16px;
    height: 83%;

    .MuiDialogContent-root {
      padding: 0px 8px !important;
      height: auto;


      .MuiTabs-root {
        padding:0px 25px;
      }

      .MuiTableCell-root {
        font: normal normal normal 12px/16px Roboto;
        padding: 10px 5px;
        color: #808080;
        border-bottom: none;
        text-align: center;
      }

      .MuiPaper-elevation1 {
        box-shadow: none;
      }

      .MuiTableHead-root {
        background-color: transparent;
      }

      h6 {
        text-align: left;
        font: normal normal 18px/24px Roboto;
        letter-spacing: 0px;
        color: #2e2e2e;
        font-weight: 600;
        padding-left: 10px;
      }

      .MuiTableRow-root {
        td {
          padding: 5px;
          font: normal normal 500 12px/16px Roboto;
          letter-spacing: 0px;
          color: #2e2e2e;
          border: none;
          text-align: center;
        }
      }

      .MuiTab-root {
        min-width: auto;
        text-transform: capitalize;
        margin-right: 35px;
        font: normal normal normal 14px/19px Roboto;
        color: #2e2e2e;
        font-weight: 600;
      }

      .MuiTab-root.Mui-selected {
        opacity: 1;
        border-bottom: 3px solid #0065ff;
        color: #0065ff;
        border-radius: 2px;
        font-weight: 600;
      }

      .MuiTabs-indicator {
        display: none;
      }

    }

    .RenewalLinkPopupHistory {

      .button-txt {
        background: #ffffff;
        border: 1px solid #0065ff;
        border-radius: 8px;
        letter-spacing: 0.5px;
        padding: 11px 20px;
        width: 100%;
        min-width: 160px;
        color: #0065ff;
        font: normal normal 600 13px/20px Roboto;
        cursor: pointer;
        outline: none;
        margin: 0px;

        &:hover {
          background: #0065ff;
          color: #ffffff;
          box-shadow: 0 4px 15px rgba(0, 101, 255, 0.3);
        }


      }


      .button {
        background: #ffffff;
        border-radius: 8px;
        letter-spacing: 0.17px;
        padding: 5px;
        width: 100px;
        font: normal normal 600 12px/21px Roboto;
        cursor: pointer;
        outline: none;
        color: #0065ff;
        border-color: #0065ff;

      }

      .pending-button-txt {
        background: #ffffff;
        border-radius: 8px;
        letter-spacing: 0.17px;
        padding: 5px;
        width: 100px;
        font: normal normal 600 12px/21px Roboto;
        outline: none;
        margin: 0px;
      }

      .disablebtn {
        cursor: default !important;
      }

      .TablePagination {
        .MuiTablePagination-input {
          margin-right: 8px !important;
        }

        .MuiButtonBase-root {
          position: static;
          background-color: transparent !important;
        }
      }

    }

    .SubmitBtn {
      background: #e3f2fd;
      border: 1px solid #bbdefb;
      border-radius: 8px;
      letter-spacing: 0.3px;
      padding: 10px 20px;
      width: 150px;
      min-width: 140px;
      color: #0065ff;
      font: normal normal 600 14px/18px Roboto;
      cursor: pointer;
      outline: none;
      margin-top: 6px;

      &:hover {
        background: #bbdefb;
        border-color: #90caf9;
        color: #0052cc;

      }


    }




    .CancellationAttechBtn {
      background: #FFF5F5;
      border: 1px solid #FFE0E0;
      border-radius: 8px;
      letter-spacing: 0.3px;
      padding: 12px 18px;
      width: 100%;
      min-width: 130px;
      font: normal normal 500 14px/18px Roboto;
      color: #D63031;
      cursor: pointer;
      outline: none;
      margin: 0px;
      transition: all 0.2s ease;


      &:hover {
        background: #FFEBEB;
        border-color: #FFB3B3;
        color: #B71C1C;
      }

    }

    .Note {
      color: #df1f1f;
      font: normal normal 600 12px/15px Roboto;
      margin: 20px 0px 0px;
      text-align: center;
    }



    .upload-option {
      .upload-section {
        width: 100%;
      }

      .upload-label {
        font-size: 13px;
        font-weight: bold;
        letter-spacing: 0.22px;
        color: #253858;
        display: block;
        margin-bottom: 12px;
      }

      .upload-button-container {
        width: 100%;
        display: block;
      }

      .compact-upload-btn {
        width: 100%;
        min-height: 40px;
        padding: 7px 16px;
        font-size: 13px;
        font-weight: 500;
        border: 2px dashed #0065ff;
        color: #0065ff;
        background: #f8f9ff;
        border-radius: 8px;
        text-transform: none;
        display: flex;
        align-items: center;
        justify-content: center;

        &:hover {
          border-color: #1976D2 !important;
          background: #e3f2fd !important;
          box-shadow: 0 4px 12px rgba(33, 150, 243, 0.2) !important;
        }

        .upload-icon {
          font-size: 18px;
          position: relative;
          top: 5px;
        }
      }

    }
  }

  .hidden-file-input {
    display: none !important;
  }
}

/* Responsive Design */
@media (max-width: 768px) {

  .upload-option {
    .upload-section {
      margin-top: 10px;
    }

    .compact-upload-btn {
      min-height: 40px !important;
      padding: 10px 12px !important;
      font-size: 12px !important;
    }

    .uploaded-file-container {
      padding: 10px 12px;
    }
  }

  .RenewalLinkPopup {
   .MuiTabs-root {
        padding:0px !important;
      }

    .MuiTab-root {
      margin-right: 0px !important;
    }

  }
}