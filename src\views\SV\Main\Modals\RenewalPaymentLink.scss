.RenewalLinkPopup {
  .MuiDialog-paperWidthSm {
    padding-bottom: 0px;
    height: auto;
    width: 700px;
    box-shadow: 0px 0px 16px #00000014;
    border-radius: 16px;
    height: 96%;

    .MuiDialogContent-root {
      padding: 10px !important;
      height: auto;



      .MuiTableCell-root {
        font: normal normal normal 12px/16px Roboto;
        padding: 5px;
        color: #808080;
        border-bottom: none;
        text-align: center;
      }

      .MuiPaper-elevation1 {
        box-shadow: none;
      }

      .MuiTableHead-root {
        background-color: transparent;
      }

      h6 {
        text-align: left;
        font: normal normal 18px/24px Roboto;
        letter-spacing: 0px;
        color: #2e2e2e;
        font-weight: 600;
        padding-left: 10px;
      }

      .MuiTableRow-root {
        td {
          padding: 5px;
          font: normal normal 500 12px/16px Roboto;
          letter-spacing: 0px;
          color: #2e2e2e;
          border: none;
          text-align: center;
        }
      }

      .MuiTab-root {
        min-width: auto;
        text-transform: capitalize;
        margin-right: 35px;
        font: normal normal normal 14px/19px Roboto;
        color: #2e2e2e;
        font-weight: 600;
      }

      .MuiTab-root.Mui-selected {
        opacity: 1;
        border-bottom: 3px solid #0065ff;
        color: #0065ff;
        border-radius: 2px;
        font-weight: 600;
      }

      .MuiTabs-indicator {
        display: none;
      }

    }

    .RenewalLinkPopupHistory {
      .mt-3 {
        margin-top: 8px;
      }

      .button-txt {
        background: #ffffff;
        border-radius: 8px;
        letter-spacing: 0.17px;
        padding: 8px;
        width: auto;
        font: normal normal 600 12px/21px Roboto;
        cursor: pointer;
        outline: none;
        margin: 5px;
        cursor: pointer;
      }

      .button {
        background: #ffffff;
        border-radius: 8px;
        letter-spacing: 0.17px;
        padding: 5px;
        width: 100px;
        font: normal normal 600 12px/21px Roboto;
        cursor: pointer;
        outline: none;
        color: #0065ff;
        border-color: #0065ff;

      }

      .pending-button-txt {
        background: #ffffff;
        border-radius: 8px;
        letter-spacing: 0.17px;
        padding: 5px;
        width: 100px;
        font: normal normal 600 12px/21px Roboto;
        outline: none;
        margin: 0px;
      }

      .disablebtn {
        cursor: default !important;
      }

      .TablePagination {
        .MuiTablePagination-input {
          margin-right: 8px !important;
        }

        .MuiButtonBase-root {
          position: static;
          background-color: transparent !important;
        }
      }

    }
  }

  // .justifyCenter {
  //   justify-content: center;
  // }

  // .mt-3 {
  //   margin-top: 8px;
  // }

  // .GenerateBtn {
  //   background: #0065ff;
  //   border-radius: 8px;
  //   letter-spacing: 0.17px;
  //   padding: 8px;
  //   width: 150px;
  //   border: none;
  //   color: #ffffff;
  //   font: normal normal 600 12px/21px Roboto;
  //   cursor: pointer;
  //   outline: none;
  //   margin-top: 10px;
  // }


  // .Note {
  //   color: #df1f1f;
  //   font: normal normal 600 12px/15px Roboto;
  //   margin: 15px 0px;
  // }

  // .button-txt {
  //   background: #ffffff;
  //   border-radius: 8px;
  //   letter-spacing: 0.17px;
  //   padding: 8px;
  //   width: 150px;
  //   font: normal normal 600 12px/21px Roboto;
  //   cursor: pointer;
  //   outline: none;
  //   margin-top: 10px;
  //   color: #0065ff;
  //   border-color: #0065ff;
  // }

  // span {
  //   float: right;
  //   font: normal normal 500 12px/24px Roboto;
  //   margin-right: 35px;
  // }

  // .custom-disabled-input input:disabled {
  //   background: #e5e5e5 !important;
  //   border-radius: 12px !important;
  //   border: none !important;
  //   color: #6b6764 !important;
  // }
}