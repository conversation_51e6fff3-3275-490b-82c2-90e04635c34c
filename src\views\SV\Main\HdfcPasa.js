import React, { useEffect, useState } from "react";
import { HdfcPasaPopup } from "./Modals/HdfcPasaPopup";
import User from '../../../../src/services/user.service';
import { CONFIG } from "../../../../src/appconfig";
import rootScopeService from '../../../../src/services/rootScopeService';
import { useSelector } from "react-redux";
import { CALL_API } from "../../../services";
import { SV_CONFIG } from "../../../../src/appconfig";

export default function HdfcPasa(props) {
    let [allLeads] = useSelector(state => {
        let { allLeads } = state.salesview;
        return [allLeads]
    });
    const [OpenHdfcPasa, setOpenHdfcPasa] = useState(false);
    const [CustomerPitchData, setCustomerPitchData] = useState([]);
    const [IsCustPitch, setIsCustPitch] = useState(false);
    const [ISBajajSelf,setISBajajSelf] = useState(false)

    const fnOpenViewDetails = () => {
        setOpenHdfcPasa(true);
    }
    let roleId = User.RoleId;
    let productid = rootScopeService.getProductId();

    useEffect(() => {
        InitValues();
    }, [allLeads]);

    var InitValues = function () {
        setIsCustPitch(false);
        if (allLeads.length > 0) {
            let data = Array.isArray(allLeads) ? allLeads.sort((a,b) => b.CreatedOn - a.CreatedOn) : [];
            if(productid == 115){
                GetCustomerPitchdata(productid,0);
            }
            else if(([7, 1000].indexOf(productid) !== -1)){
                let LatestUTM = FindLatestUtm_Medium(data);
                if(LatestUTM != null && LatestUTM != ''){
                    var InsurerID = 0;
                    InsurerID = FindInsurerId(LatestUTM);
                    
                    if(InsurerID > 0)
                        GetCustomerPitchdata(productid,InsurerID,LatestUTM);
                }
            }
            
        }
    }

    const GetCustomerPitchdata = (productid,InsurerID,LatestUTM) => {
        setCustomerPitchData([])
        const input = {
            url: `coremrs/api/LeadDetails/GetCustomerPitchdata/${rootScopeService.getCustomerId()}/${productid}/${InsurerID}`,
            method: 'GET',
            service: 'MatrixCoreAPI'
        }
        CALL_API(input).then((response) => {
            if (response) {
                setIsCustPitch(true);
                setCustomerPitchData(response);
            }
        });
    }

    const FindLatestUtm_Medium = (data) => {
        data = data.find(lead => lead.UTM_Medium && lead.UTM_Medium != null && lead.UTM_Medium != '');
        if(data){
            return data.UTM_Medium;
        }
    }

    const FindInsurerId = (LatestUTM) => {
        if(SV_CONFIG["TermPasa"]["MaxTerm"] && Array.isArray(SV_CONFIG["TermPasa"]["MaxTerm"]) && SV_CONFIG["TermPasa"]["MaxTerm"].some(str => (LatestUTM.toLowerCase()).includes(str))){
            return 10;//Max-Term
        }
        else if(SV_CONFIG["TermPasa"]["IPRUTerm"] && Array.isArray(SV_CONFIG["TermPasa"]["IPRUTerm"]) && SV_CONFIG["TermPasa"]["IPRUTerm"].some(str => (LatestUTM.toLowerCase()).includes(str))){
            return 5;//IPU-Term
        } 
        else if (SV_CONFIG["TermPasa"]["BajajTerm"] && Array.isArray(SV_CONFIG["TermPasa"]["BajajTerm"]) && SV_CONFIG["TermPasa"]["BajajTerm"].some(str => (LatestUTM.toLowerCase()).includes(str))){
            return 11;//Bajaj-Term
        }
        else if(SV_CONFIG["TermPasa"]["TataTerm"] && Array.isArray(SV_CONFIG["TermPasa"]["TataTerm"]) && SV_CONFIG["TermPasa"]["TataTerm"].some(str => (LatestUTM.toLowerCase()).includes(str))){
            return 2;//Tata
        }
        else if(SV_CONFIG["TermPasa"]["BajajSelf"] && Array.isArray(SV_CONFIG["TermPasa"]["BajajSelf"]) && SV_CONFIG["TermPasa"]["BajajSelf"].some(str => (LatestUTM.toLowerCase()).includes(str))){
            setISBajajSelf(true);
            return 11;//Bajaj-Term  Self
        }
        else if(SV_CONFIG["TermPasa"]["HDFCTerm"] && Array.isArray(SV_CONFIG["TermPasa"]["HDFCTerm"]) && SV_CONFIG["TermPasa"]["HDFCTerm"].some(str => (LatestUTM.toLowerCase()).includes(str))){
            return 16;//HDFC Term
        }
        else 
            return 0;
    }

    return (
        <>
            {(productid === 115 && parseInt(roleId) === 13) && IsCustPitch &&
                <div className="HDFCPasa"><p>Customer eligible for PASA </p> <button onClick={fnOpenViewDetails}>View Details</button></div>

            }
            {(([7, 1000].indexOf(productid) !== -1) && ((parseInt(roleId) === 13) || (parseInt(roleId) === 12))) && IsCustPitch && 
                <div className="MaxPasa">{ISBajajSelf ? <p>Pre-approved customer, shown interest in term plan</p> : <p>Pre approved customer, shown interest in HouseWife Plan</p>} 
                <button onClick={fnOpenViewDetails}>
                    <img alt="viewdetails" src={CONFIG.PUBLIC_URL + "/images/DetailArrow.svg"} />
                </button></div>

            }
            <HdfcPasaPopup open={OpenHdfcPasa} handleClose={() => { setOpenHdfcPasa(false) }} ISBajajSelf={ISBajajSelf} CustomerPitchData={CustomerPitchData}/>
        </>
    )

}
