<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20"><defs><style>.a{fill:#ffcd00;}.b{fill:#ff3051;}.c{fill:#e52b49;}.d{fill:#bcbec0;}.e{fill:#ff9811;}.f{fill:#ff6243;}.g{fill:#e5583c;}.h{fill:#e5890f;}</style></defs><path class="a" d="M98.762,283.233c.018-.012.039-.021.058-.033C98.8,283.213,98.78,283.221,98.762,283.233Zm0,0" transform="translate(-94.647 -271.399)"/><path class="a" d="M231.055,281.216c.025-.01.051-.016.077-.024C231.106,281.2,231.079,281.205,231.055,281.216Zm0,0" transform="translate(-221.427 -269.475)"/><path class="a" d="M235.809,280.216c.029-.005.059-.006.088-.009C235.867,280.21,235.838,280.211,235.809,280.216Zm0,0" transform="translate(-225.983 -268.532)"/><path class="a" d="M373.809,280.8c.02.005.038.012.058.018A.48.48,0,0,0,373.809,280.8Zm0,0" transform="translate(-358.233 -269.101)"/><path class="a" d="M354.762,283.233c.018-.012.039-.021.058-.033C354.8,283.213,354.78,283.221,354.762,283.233Zm0,0" transform="translate(-339.98 -271.399)"/><path class="a" d="M103.055,281.216c.025-.01.051-.016.077-.024C103.106,281.2,103.079,281.205,103.055,281.216Zm0,0" transform="translate(-98.761 -269.475)"/><path class="a" d="M368.994,280.063h-.033a.709.709,0,0,1,.095.009C369.034,280.071,369.015,280.064,368.994,280.063Zm0,0" transform="translate(-353.588 -268.393)"/><path class="a" d="M363.809,280.216c.029-.005.059-.006.088-.009C363.867,280.21,363.838,280.211,363.809,280.216Zm0,0" transform="translate(-348.65 -268.532)"/><path class="a" d="M359.055,281.216c.025-.01.051-.016.077-.024C359.106,281.2,359.079,281.205,359.055,281.216Zm0,0" transform="translate(-344.094 -269.475)"/><path class="a" d="M245.809,280.8c.02.005.038.012.058.018A.481.481,0,0,0,245.809,280.8Zm0,0" transform="translate(-235.567 -269.101)"/><path class="a" d="M107.809,280.216c.029-.005.059-.006.088-.009C107.867,280.21,107.838,280.211,107.809,280.216Zm0,0" transform="translate(-103.317 -268.532)"/><path class="a" d="M226.762,283.233c.018-.012.039-.021.058-.033C226.8,283.213,226.78,283.221,226.762,283.233Zm0,0" transform="translate(-217.313 -271.399)"/><path class="a" d="M112.994,280.063h-.033a.709.709,0,0,1,.095.009C113.034,280.071,113.015,280.064,112.994,280.063Zm0,0" transform="translate(-108.254 -268.393)"/><path class="a" d="M117.809,280.8c.02.005.038.012.058.018A.481.481,0,0,0,117.809,280.8Zm0,0" transform="translate(-112.9 -269.101)"/><path class="a" d="M240.994,280.063h-.033a.709.709,0,0,1,.095.009C241.034,280.071,241.015,280.064,240.994,280.063Zm0,0" transform="translate(-230.921 -268.393)"/><path class="a" d="M47.487,283.6a1.674,1.674,0,0,1-.487-1.18v-1.277a1.059,1.059,0,0,0-.7-1.006c-.019-.006-.038-.013-.058-.018a1.055,1.055,0,0,0-.108-.019c-.031,0-.063-.008-.095-.009a.975.975,0,0,0-.126,0c-.03,0-.06,0-.088.009a1.035,1.035,0,0,0-.121.033c-.026.008-.052.014-.077.024a.966.966,0,0,0-.121.062.505.505,0,0,0-.058.033,1,1,0,0,0-.449.83v.667a1.667,1.667,0,0,1-3.333,0v-.61a1.059,1.059,0,0,0-.7-1.006c-.019-.006-.038-.013-.058-.018a1.055,1.055,0,0,0-.108-.019c-.031,0-.063-.008-.095-.009a.975.975,0,0,0-.126,0c-.03,0-.06,0-.088.009a1.035,1.035,0,0,0-.121.033c-.026.008-.052.014-.077.024a.966.966,0,0,0-.121.062.506.506,0,0,0-.058.033,1,1,0,0,0-.449.83v2a1.667,1.667,0,1,1-3.333,0v-1.944a1.059,1.059,0,0,0-.7-1.006c-.019-.006-.038-.013-.058-.018a1.055,1.055,0,0,0-.108-.019c-.031,0-.063-.008-.095-.009a.975.975,0,0,0-.126,0c-.03,0-.06,0-.088.009a1.035,1.035,0,0,0-.121.033c-.026.008-.052.014-.077.024a.966.966,0,0,0-.121.062.505.505,0,0,0-.058.033,1,1,0,0,0-.449.83v1.333a1.662,1.662,0,0,1-1.667,1.667,1.7,1.7,0,0,1-.667-.137v4.137H49.333v-4.137a1.7,1.7,0,0,1-.667.137A1.674,1.674,0,0,1,47.487,283.6Zm0,0" transform="translate(-30.667 -268.418)"/><path class="b" d="M26,232H9.333A1.333,1.333,0,0,0,8,233.333v3a1.674,1.674,0,0,0,.487,1.18,1.667,1.667,0,0,0,2.847-1.18V235a.995.995,0,0,1,.448-.833c.018-.012.039-.021.058-.033a.989.989,0,0,1,.121-.062c.025-.01.051-.016.077-.024a1.042,1.042,0,0,1,.121-.033c.029-.005.059-.006.088-.009s.057-.006.086-.006c.013,0,.026,0,.039,0a.206.206,0,0,1,.033,0,.483.483,0,0,1,.061.011,1.055,1.055,0,0,1,.108.019l.058.018a1,1,0,0,1,.7.948v2a1.667,1.667,0,0,0,3.333,0v-2a.995.995,0,0,1,.448-.833c.018-.012.039-.021.058-.033a.989.989,0,0,1,.121-.062c.025-.01.051-.016.077-.024a1.042,1.042,0,0,1,.121-.033c.029-.005.059-.006.088-.009s.057-.006.086-.006c.013,0,.026,0,.039,0a.206.206,0,0,1,.033,0,.482.482,0,0,1,.061.011,1.055,1.055,0,0,1,.108.019l.058.018a1,1,0,0,1,.7.948v.667a1.667,1.667,0,1,0,3.333,0V235a.995.995,0,0,1,.448-.833c.018-.012.039-.021.058-.033a.989.989,0,0,1,.121-.062c.025-.01.051-.016.077-.024a1.042,1.042,0,0,1,.121-.033c.029-.005.059-.006.088-.009S22.97,234,23,234c.013,0,.026,0,.039,0a.206.206,0,0,1,.033,0,.482.482,0,0,1,.061.011,1.055,1.055,0,0,1,.108.019l.058.018A1,1,0,0,1,24,235v1.333A1.662,1.662,0,0,0,25.667,238a1.7,1.7,0,0,0,.667-.137,1.676,1.676,0,0,0,1-1.53v-3A1.333,1.333,0,0,0,26,232Zm0,0" transform="translate(-7.667 -222.333)"/><path class="c" d="M10.847,337.18a1.674,1.674,0,0,0,.487-1.18v.667a1.667,1.667,0,1,1-3.333,0V336a1.674,1.674,0,0,0,.487,1.18,1.653,1.653,0,0,0,1.18.487A1.674,1.674,0,0,0,10.847,337.18Zm0,0" transform="translate(-7.667 -322)"/><path class="c" d="M138.847,353.18a1.674,1.674,0,0,0,.487-1.18v.667a1.667,1.667,0,0,1-3.333,0V352a1.669,1.669,0,0,0,2.847,1.18Zm0,0" transform="translate(-130.333 -337.333)"/><path class="c" d="M266.847,321.18a1.674,1.674,0,0,0,.487-1.18v.667a1.667,1.667,0,0,1-3.333,0V320a1.669,1.669,0,0,0,2.847,1.18Zm0,0" transform="translate(-253 -306.667)"/><path class="c" d="M395.333,336v.667a1.676,1.676,0,0,1-1,1.53,1.7,1.7,0,0,1-.667.137A1.662,1.662,0,0,1,392,336.667V336a1.661,1.661,0,0,0,1.667,1.667,1.7,1.7,0,0,0,.667-.137A1.676,1.676,0,0,0,395.333,336Zm0,0" transform="translate(-375.667 -322)"/><path class="d" d="M19.667,464.667H.333a.333.333,0,1,1,0-.667H19.667a.333.333,0,1,1,0,.667Zm0,0" transform="translate(0 -444.667)"/><g transform="translate(3.96 11.667)"><path class="a" d="M351.039,284.142a1,1,0,0,1,.155-.126A1,1,0,0,0,351.039,284.142Zm0,0" transform="translate(-340.372 -283.848)"/><path class="a" d="M119.9,282.221v-.056a1,1,0,0,0-.7-.949A1.059,1.059,0,0,1,119.9,282.221Zm0,0" transform="translate(-118.193 -281.164)"/><path class="a" d="M243.2,280.32a1.055,1.055,0,0,1,.108.019A1.055,1.055,0,0,0,243.2,280.32Zm0,0" transform="translate(-237.026 -280.307)"/><path class="a" d="M247.9,282.221v-.056a1,1,0,0,0-.7-.949A1.059,1.059,0,0,1,247.9,282.221Zm0,0" transform="translate(-240.859 -281.164)"/><path class="a" d="M375.9,282.221v-.056a1,1,0,0,0-.7-.949A1.059,1.059,0,0,1,375.9,282.221Zm0,0" transform="translate(-363.526 -281.164)"/><path class="a" d="M232.9,280.424a1.072,1.072,0,0,1,.121-.033A1.072,1.072,0,0,0,232.9,280.424Zm0,0" transform="translate(-227.158 -280.374)"/><path class="a" d="M110.016,280c-.03,0-.057.006-.086.009a.975.975,0,0,1,.126,0C110.042,280,110.029,280,110.016,280Zm0,0" transform="translate(-109.309 -280)"/><path class="a" d="M115.2,280.32a1.055,1.055,0,0,1,.108.019A1.055,1.055,0,0,0,115.2,280.32Zm0,0" transform="translate(-114.359 -280.307)"/><path class="a" d="M366.016,280c-.03,0-.057.006-.086.009a.975.975,0,0,1,.126,0C366.042,280,366.029,280,366.016,280Zm0,0" transform="translate(-354.643 -280)"/><path class="a" d="M100.16,281.839a.966.966,0,0,1,.121-.062A.966.966,0,0,0,100.16,281.839Zm0,0" transform="translate(-99.947 -281.703)"/><path class="a" d="M356.16,281.839a.967.967,0,0,1,.121-.062A.967.967,0,0,0,356.16,281.839Zm0,0" transform="translate(-345.28 -281.703)"/><path class="a" d="M360.9,280.424a1.072,1.072,0,0,1,.121-.033A1.072,1.072,0,0,0,360.9,280.424Zm0,0" transform="translate(-349.825 -280.374)"/><path class="a" d="M95.039,284.142a1,1,0,0,1,.155-.126A1,1,0,0,0,95.039,284.142Zm0,0" transform="translate(-95.039 -283.848)"/><path class="a" d="M104.9,280.424a1.073,1.073,0,0,1,.121-.033A1.073,1.073,0,0,0,104.9,280.424Zm0,0" transform="translate(-104.491 -280.374)"/><path class="a" d="M238.016,280c-.03,0-.057.006-.086.009a.975.975,0,0,1,.126,0C238.042,280,238.029,280,238.016,280Zm0,0" transform="translate(-231.976 -280)"/><path class="a" d="M223.039,284.142a1,1,0,0,1,.155-.126A1,1,0,0,0,223.039,284.142Zm0,0" transform="translate(-217.706 -283.848)"/><path class="a" d="M371.2,280.32a1.056,1.056,0,0,1,.108.019A1.056,1.056,0,0,0,371.2,280.32Zm0,0" transform="translate(-359.693 -280.307)"/><path class="a" d="M228.16,281.839a.966.966,0,0,1,.121-.062A.966.966,0,0,0,228.16,281.839Zm0,0" transform="translate(-222.613 -281.703)"/></g><path class="e" d="M216,128h2v4.333h-2Zm0,0" transform="translate(-207 -122.667)"/><path class="f" d="M208,2.667a1.333,1.333,0,1,0,2.667,0A8.816,8.816,0,0,0,209.333,0,8.816,8.816,0,0,0,208,2.667Zm0,0" transform="translate(-199.333)"/><path class="e" d="M352,144h2v3.667h-2Zm0,0" transform="translate(-337.333 -138)"/><path class="f" d="M344,18.667a1.333,1.333,0,1,0,2.667,0A8.816,8.816,0,0,0,345.333,16,8.816,8.816,0,0,0,344,18.667Zm0,0" transform="translate(-329.667 -15.333)"/><path class="e" d="M80,144h2v3.667H80Zm0,0" transform="translate(-76.667 -138)"/><path class="f" d="M74.667,18.667A1.335,1.335,0,0,1,73.5,19.99h0a1.359,1.359,0,0,1-.167.01A1.333,1.333,0,0,1,72,18.667,8.812,8.812,0,0,1,73.333,16s.067.1.167.25A7.2,7.2,0,0,1,74.667,18.667Zm0,0" transform="translate(-69 -15.333)"/><path class="g" d="M73.5,19.99a1.359,1.359,0,0,1-.167.01A1.333,1.333,0,0,1,72,18.667,8.812,8.812,0,0,1,73.333,16s.067.1.167.25a7.2,7.2,0,0,0-1.167,2.417A1.336,1.336,0,0,0,73.5,19.99Zm0,0" transform="translate(-69 -15.333)"/><path class="g" d="M209.5,3.99a1.359,1.359,0,0,1-.167.01A1.333,1.333,0,0,1,208,2.667,8.812,8.812,0,0,1,209.333,0s.067.1.167.25a7.2,7.2,0,0,0-1.167,2.417A1.336,1.336,0,0,0,209.5,3.99Zm0,0" transform="translate(-199.333)"/><path class="g" d="M345.5,19.99a1.36,1.36,0,0,1-.167.01A1.333,1.333,0,0,1,344,18.667,8.812,8.812,0,0,1,345.333,16s.067.1.167.25a7.2,7.2,0,0,0-1.167,2.417A1.336,1.336,0,0,0,345.5,19.99Zm0,0" transform="translate(-329.667 -15.333)"/><path class="h" d="M80,144h2v.667H80Zm0,0" transform="translate(-76.667 -138)"/><path class="h" d="M216,128h2v.667h-2Zm0,0" transform="translate(-207 -122.667)"/><path class="h" d="M352,144h2v.667h-2Zm0,0" transform="translate(-337.333 -138)"/></svg>