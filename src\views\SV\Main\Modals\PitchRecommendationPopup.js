import React from "react";
import ModalPopup from '../../../../components/Dialogs/ModalPopup';
import { Grid, Box, Typography } from "@mui/material";

export const PitchRecommendationPopup = (props) => {
    const { lead } = props;

    // Define different pitch recommendations based on product or lead data
    const getPitchRecommendation = () => {
        const productId = lead?.ProductID;
        
        // Default recommendation
        let recommendation = {
            title: "Pitch recommendation",
            content: "Pitch both Cumulative Bonus Super & Claim Shield riders",
            icon: "👥",
            bgColor: "#e3f2fd",
            borderColor: "#2196f3"
        };

        // Customize based on product type
        switch (productId) {
            case 2: // Health Insurance
                recommendation = {
                    title: "Health Insurance Pitch",
                    content: "Pitch both Cumulative Bonus Super & Claim Shield riders for comprehensive health coverage",
                    icon: "🏥",
                    bgColor: "#e8f5e8",
                    borderColor: "#4caf50"
                };
                break;
            case 7: // Term Insurance
                recommendation = {
                    title: "Term Insurance Pitch",
                    content: "Pitch Term Life Insurance with Accidental Death Benefit rider for maximum protection",
                    icon: "🛡️",
                    bgColor: "#fff3e0",
                    borderColor: "#ff9800"
                };
                break;
            case 117: // Motor Insurance
                recommendation = {
                    title: "Motor Insurance Pitch",
                    content: "Pitch comprehensive motor insurance with Zero Depreciation and Engine Protection covers",
                    icon: "🚗",
                    bgColor: "#f3e5f5",
                    borderColor: "#9c27b0"
                };
                break;
            case 115: // Investment
                recommendation = {
                    title: "Investment Pitch",
                    content: "Pitch ULIP with top-up facility for wealth creation and tax benefits",
                    icon: "📈",
                    bgColor: "#e0f2f1",
                    borderColor: "#009688"
                };
                break;
            case 131: // SME Insurance
                recommendation = {
                    title: "SME Insurance Pitch",
                    content: "Pitch comprehensive business insurance package with liability coverage",
                    icon: "🏢",
                    bgColor: "#fce4ec",
                    borderColor: "#e91e63"
                };
                break;
            default:
                // Keep default recommendation
                break;
        }

        return recommendation;
    };

    const recommendation = getPitchRecommendation();

    return (
        <ModalPopup 
            open={props.open} 
            title='' 
            handleClose={props.handleClose} 
            className="pitchRecommendationPopup"
        >
            <div style={{ padding: '20px', minHeight: '150px', textAlign: 'center' }}>
                <Grid container spacing={2}>
                    <Grid item xs={12}>
                        <Box 
                            style={{ 
                                backgroundColor: recommendation.bgColor,
                                border: `2px solid ${recommendation.borderColor}`,
                                borderRadius: '12px',
                                padding: '20px',
                                marginBottom: '15px'
                            }}
                        >
                            {/* Icon */}
                            <div style={{ 
                                fontSize: '48px', 
                                marginBottom: '15px',
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'center',
                                gap: '10px'
                            }}>
                                <span style={{ fontSize: '32px' }}>{recommendation.icon}</span>
                                <span style={{ fontSize: '32px' }}>👥</span>
                            </div>

                            {/* Title */}
                            <Typography 
                                variant="h5" 
                                style={{ 
                                    color: recommendation.borderColor,
                                    fontWeight: 'bold',
                                    marginBottom: '15px',
                                    fontSize: '24px'
                                }}
                            >
                                {recommendation.title}
                            </Typography>

                            {/* Content */}
                            <Typography 
                                variant="body1" 
                                style={{ 
                                    color: '#333',
                                    fontSize: '16px',
                                    lineHeight: '1.5',
                                    fontWeight: '500'
                                }}
                            >
                                {recommendation.content}
                            </Typography>
                        </Box>
                    </Grid>

                    {/* Additional Information Section */}
                    <Grid item xs={12}>
                        <Box 
                            style={{ 
                                backgroundColor: '#f8f9fa',
                                borderRadius: '8px',
                                padding: '15px',
                                border: '1px solid #dee2e6'
                            }}
                        >
                            <Typography 
                                variant="h6" 
                                style={{ 
                                    color: '#495057',
                                    marginBottom: '10px',
                                    fontSize: '16px',
                                    fontWeight: 'bold'
                                }}
                            >
                                Lead Details
                            </Typography>
                            
                            <div style={{ display: 'flex', flexDirection: 'column', gap: '5px' }}>
                                <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                                    <span style={{ fontWeight: '500', color: '#6c757d' }}>Lead ID:</span>
                                    <span style={{ color: '#495057' }}>{lead?.LeadID || 'N/A'}</span>
                                </div>
                                <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                                    <span style={{ fontWeight: '500', color: '#6c757d' }}>Customer:</span>
                                    <span style={{ color: '#495057' }}>{lead?.Name || 'N/A'}</span>
                                </div>
                                <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                                    <span style={{ fontWeight: '500', color: '#6c757d' }}>Product:</span>
                                    <span style={{ color: '#495057' }}>{lead?.ProductName || `Product ${lead?.ProductID}` || 'N/A'}</span>
                                </div>
                                <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                                    <span style={{ fontWeight: '500', color: '#6c757d' }}>Status:</span>
                                    <span style={{ color: '#495057' }}>{lead?.StatusName || 'N/A'}</span>
                                </div>
                            </div>
                        </Box>
                    </Grid>

                    {/* Action Tips */}
                    <Grid item xs={12}>
                        <Box 
                            style={{ 
                                backgroundColor: '#d1ecf1',
                                borderRadius: '8px',
                                padding: '12px',
                                border: '1px solid #bee5eb',
                                textAlign: 'left'
                            }}
                        >
                            <Typography 
                                variant="body2" 
                                style={{ 
                                    color: '#0c5460',
                                    fontSize: '14px',
                                    fontWeight: '500'
                                }}
                            >
                                💡 <strong>Pro Tip:</strong> Use this recommendation to guide your conversation and highlight the key benefits that matter most to this customer.
                            </Typography>
                        </Box>
                    </Grid>
                </Grid>
            </div>
        </ModalPopup>
    );
};
