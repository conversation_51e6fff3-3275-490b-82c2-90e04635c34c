import React from "react";
import ModalPopup from '../../../../components/Dialogs/ModalPopup';
import { Typography } from "@mui/material";
import './PitchRecommendationPopup.css';

export const PitchRecommendationPopup = (props) => {
    const { lead } = props;

    // Define different pitch recommendations based on product or lead data
    const getPitchRecommendation = () => {
        const productId = lead?.ProductID;

        // Default recommendation
        let recommendation = {
            title: "Pitch recommendation",
            content: "Pitch both Cumulative Bonus Super & Claim Shield riders"
        };

        // Customize based on product type
        switch (productId) {
            case 2: // Health Insurance
                recommendation = {
                    title: "Pitch recommendation",
                    content: "Pitch both Cumulative Bonus Super & Claim Shield riders for comprehensive health coverage"
                };
                break;
            case 7: // Term Insurance
                recommendation = {
                    title: "Pitch recommendation",
                    content: "Pitch Term Life Insurance with Accidental Death Benefit rider for maximum protection"
                };
                break;
            case 117: // Motor Insurance
                recommendation = {
                    title: "Pitch recommendation",
                    content: "Pitch comprehensive motor insurance with Zero Depreciation and Engine Protection covers"
                };
                break;
            case 115: // Investment
                recommendation = {
                    title: "Pitch recommendation",
                    content: "Pitch ULIP with top-up facility for wealth creation and tax benefits"
                };
                break;
            case 131: // SME Insurance
                recommendation = {
                    title: "Pitch recommendation",
                    content: "Pitch comprehensive business insurance package with liability coverage"
                };
                break;
            default:
                // Keep default recommendation
                break;
        }

        return recommendation;
    };

    const recommendation = getPitchRecommendation();

    return (
        <ModalPopup
            open={props.open}
            title=''
            handleClose={props.handleClose}
            className="pitchRecommendationPopup"
        >
            <div className="pitch-popup-container">
                {/* Header Section */}
                <div className="pitch-header">
                    <div className="pitch-icon">
                        <span className="icon-people">👥</span>
                    </div>
                    <Typography variant="h5" className="pitch-title">
                        {recommendation.title}
                    </Typography>
                    <Typography variant="body1" className="pitch-content">
                        {recommendation.content}
                    </Typography>
                </div>

                {/* Customer Concern Section */}
                <div className="customer-concern-section">
                    <div className="concern-header">
                        <span className="warning-icon">⚠️</span>
                        <Typography variant="h6" className="concern-title">
                            Customer concern
                        </Typography>
                    </div>

                    <div className="concern-content">
                        <Typography variant="h6" className="concern-subtitle">
                            Services not provided/delivered
                        </Typography>
                        <Typography variant="body2" className="concern-description">
                            Customer <strong>expressed dissatisfaction</strong> with Niva Bupa's lack of OPD or
                            maternity cover during the four-year period of purchasing the policy
                            <strong> leading to unnecessary costs incurred without benefit.</strong>
                        </Typography>
                    </div>
                </div>
            </div>
        </ModalPopup>
    );
};
