import React from "react";
import ModalPopup from '../../../../components/Dialogs/ModalPopup';
import { Typography, IconButton } from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";
import './PitchRecommendationPopup.scss';
import { CONFIG } from "../../../../appconfig";
import WarningRoundedIcon from '@mui/icons-material/WarningRounded';
import { FetchAgentAssist } from "../../../../services/Common";
import { useSnackbar } from "notistack";
import { useEffect, useState } from "react";

export const PitchRecommendationPopup = (props) => {
    const { enqueueSnackbar } = useSnackbar();
    const [recommendations, setRecommendations] = useState([]);
    const [loading, setLoading] = useState(false);

    useEffect(() => {
        if (open && props.LeadID) {
            setLoading(true);
            FetchAgentAssist(props.LeadID)
                .then((result) => {
                    if (result && Array.isArray(result)) {
                        setRecommendations(result);
                    } else {
                        setRecommendations([]);
                        enqueueSnackbar('No recommendations available', { variant: 'info' });
                    }
                })
                .catch((error) => {
                    console.error('Error fetching agent assist:', error);
                    enqueueSnackbar('Failed to fetch recommendations', { variant: 'error' });
                    setRecommendations([]);
                })
                .finally(() => {
                    setLoading(false);
                });
        }
    }, [open, props.LeadID]);




    return (
        <ModalPopup
            open={props.open}
            title=""
            handleClose={props.handleClose}
            className="pitchRecommendationPopup"
            showCloseButton={false}
        >

            <IconButton
                className="custom-close-button"
                onClick={props.handleClose}
                size="large"
            >
                <CloseIcon />
            </IconButton>


            <div className="pitch-header">
                <div className="pitch-icon">
                    <img src={CONFIG.PUBLIC_URL + "/images/salesview/communication.svg"} />
                </div>
                <Typography variant="h5" className="pitch-title">
                    Pitch recommendation
                </Typography>


                {loading ? (
                    <Typography variant="body1" className="pitch-content">
                        Loading recommendations...
                    </Typography>
                ) : recommendations.length > 0 ? (
                    <div className="recommendations-list">
                        {recommendations.map((recommendation, index) => (
                            <div key={index} className="recommendation-item">
                                <div className="recommendation-number">
                                    {index + 1}
                                </div>
                                <Typography variant="body1" className="recommendation-text">
                                    {recommendation}
                                </Typography>
                            </div>
                        ))}
                    </div>
                ) : (
                    <Typography variant="body1" className="pitch-content">
                        No recommendations available
                    </Typography>
                )}
            </div>


            {/* <div className="customer-concern-section">
                <div className="concern-header">
                    <WarningRoundedIcon />
                    <Typography variant="h6" className="concern-title">
                        Customer concern
                    </Typography>
                </div>

                <div className="concern-content">
                    <Typography variant="h6" className="concern-subtitle">
                        Services not provided/delivered
                    </Typography>
                    <Typography variant="body2" className="concern-description">
                        Customer <strong>expressed dissatisfaction</strong> with Niva Bupa's lack of OPD or
                        maternity cover during the four-year period of purchasing the policy
                        <strong> leading to unnecessary costs incurred without benefit.</strong>
                    </Typography>
                </div>
            </div> */}

        </ModalPopup>
    );
};
