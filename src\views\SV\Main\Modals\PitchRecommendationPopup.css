/* Pitch Recommendation Popup Styles */
.pitch-popup-container {
    padding: 0;
    margin: 0;
    background: white;
    border-radius: 12px;
    overflow: hidden;
    min-width: 400px;
    max-width: 500px;
}

/* Header Section - Blue background */
.pitch-header {
    background: #2196F3;
    color: white;
    padding: 24px;
    text-align: left;
    position: relative;
}

.pitch-icon {
    margin-bottom: 16px;
}

.icon-people {
    font-size: 32px;
    display: inline-block;
}

.pitch-title {
    color: white !important;
    font-size: 24px !important;
    font-weight: 600 !important;
    margin-bottom: 8px !important;
    line-height: 1.2 !important;
}

.pitch-content {
    color: white !important;
    font-size: 16px !important;
    font-weight: 400 !important;
    line-height: 1.4 !important;
    margin: 0 !important;
}

/* Customer Concern Section */
.customer-concern-section {
    background: #FFF8E1;
    padding: 20px 24px;
    border-top: 1px solid #E0E0E0;
}

.concern-header {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 16px;
}

.warning-icon {
    font-size: 20px;
    color: #FF9800;
}

.concern-title {
    color: #FF9800 !important;
    font-size: 18px !important;
    font-weight: 600 !important;
    margin: 0 !important;
}

.concern-content {
    margin-left: 28px;
}

.concern-subtitle {
    color: #424242 !important;
    font-size: 16px !important;
    font-weight: 600 !important;
    margin-bottom: 8px !important;
    line-height: 1.3 !important;
}

.concern-description {
    color: #616161 !important;
    font-size: 14px !important;
    line-height: 1.5 !important;
    margin: 0 !important;
}

.concern-description strong {
    color: #424242;
    font-weight: 600;
}

/* Modal Popup Overrides */
.pitchRecommendationPopup .MuiDialog-paper {
    border-radius: 12px !important;
    overflow: hidden !important;
    max-width: 500px !important;
}

.pitchRecommendationPopup .MuiDialogContent-root {
    padding: 0 !important;
}

.pitchRecommendationPopup .MuiDialogTitle-root {
    display: none !important;
}

/* Responsive Design */
@media (max-width: 600px) {
    .pitch-popup-container {
        min-width: 320px;
        max-width: 90vw;
    }
    
    .pitch-header {
        padding: 20px;
    }
    
    .customer-concern-section {
        padding: 16px 20px;
    }
    
    .pitch-title {
        font-size: 20px !important;
    }
    
    .pitch-content {
        font-size: 14px !important;
    }
    
    .concern-title {
        font-size: 16px !important;
    }
    
    .concern-subtitle {
        font-size: 14px !important;
    }
    
    .concern-description {
        font-size: 13px !important;
    }
}

/* Animation for smooth appearance */
.pitch-popup-container {
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}
