import React, { useCallback, useEffect, useState } from "react"

import { <PERSON>rid, CircularProgress, Button } from "@mui/material";
import { GetAvailableSlotsService, IsSourceCustomerWhatsapp, getAppointmentsBySlotIdService } from "../fosServices";
import { API_STATUS } from "../../../../services/Common";
import { createDateMaster, getTimeHour, IsSpokeCities, IsSpokeCitiesDays, SpokeCitiesAppointmentCount } from "../fosCommonHelper";
import TimeSlot from "./TimeSlot";
import DateSlot from "./DateSlot";
import { CONFIG } from "../../../../appconfig";
import { FormSectionHeading } from "./Components";



const AppointmentDateTimeSection = (props) => {
    const { parentLeadId, NewAddress, handleChange, disabled,
        appointmentDateTime, setNewAddress, show, viewOnly, AppointmentSlotListBySlotId,
        setAppointmentSlotListBySlotId, IsLeadRenewal, IsRescheduling, productId, TotalAppointmentsByCityId, HyperLocalRenewal, Block8AMCityList,BlockSlotIds,
        isSrcReferralOrFOSApp,IsEditAppDetails

    } = props
    const [slotAPIStatus, setSlotAPIStatus] = useState(null);
    const [slotMaster, setSlotMaster] = useState([]);
    const [dateMaster, setDateMaster] = useState([])
    const [unavailableSlots, setUnavailableSlots] = useState({});


    const setAppointmentDateTimeSlots = useCallback(() => {
        try {
            let AppointmentDate = appointmentDateTime && new Date(appointmentDateTime);
            // let currentDate= new Date().toISOString().slice(0,10);
            let serverdateobj = Array.isArray(dateMaster) && dateMaster.length > 0 && dateMaster[0].dateObject;
            let serverDate = serverdateobj && new Date(serverdateobj);
            let date = appointmentDateTime && new Date(appointmentDateTime).getDate();
            let hour = appointmentDateTime && new Date(appointmentDateTime).getHours();

            let selectedDateSlot = dateMaster.find((d) => d.date === date);
            let selectedTimeSlot = slotMaster.find((t) => getTimeHour(t.StartTime) === hour);

            if (!selectedDateSlot || AppointmentDate < serverDate || !appointmentDateTime) {
                selectedDateSlot = Array.isArray(dateMaster) && dateMaster.length > 0 && dateMaster[0];
                selectedTimeSlot = null;
            }


            let requestData = { "SelectedDate": selectedDateSlot && selectedDateSlot.fullDate, "LeadId": parentLeadId && parentLeadId.toString() }
            if (!IsSourceCustomerWhatsapp()) {
                getAppointmentsBySlotIdService(requestData).then((res) => {
                    setAppointmentSlotListBySlotId(res);
                }).catch(() => {
                    console.log("error in getAppointmentsBySlotIdService");
                })
            }


            setNewAddress(prevState => ({ ...prevState, AppointmentDateSlot: selectedDateSlot, AppointmentTimeSlot: selectedTimeSlot }))

        } catch (e) {
            console.error(e);
            setNewAddress(prevState => ({ ...prevState, AppointmentDateSlot: null, AppointmentTimeSlot: null }))

        }
    }, [appointmentDateTime, dateMaster, parentLeadId, setAppointmentSlotListBySlotId, setNewAddress, slotMaster])

    const GetAvailableSlotsList = useCallback(() => {
        setSlotAPIStatus(API_STATUS.LOADING)
        if (!IsSourceCustomerWhatsapp() && !NewAddress.AssignmentId) {
            return;
        }
        GetAvailableSlotsService(parentLeadId, NewAddress.AssignmentId).then((res) => {
            if (res) {
                const slotList = Array.isArray(res.SlotsModel) ? res.SlotsModel : [];
                let _unavailableSlots = res.NotAvailableSlots;

                if (slotList) {
                    setSlotAPIStatus(API_STATUS.SUCCESS);
                    let startday = IsLeadRenewal && !IsRescheduling && (!IsSourceCustomerWhatsapp() || HyperLocalRenewal) ? 1 : 0;
                    setSlotMaster(slotList);
                    setUnavailableSlots(_unavailableSlots);
                    const _dateMaster = createDateMaster(res.ServerDate, res.NoOfDays, startday);


                    setDateMaster(_dateMaster);
                    setNewAddress(prevState => ({ ...prevState, AppointmentDateSlot: _dateMaster[0] }))
                }
            } else {
                setSlotAPIStatus(API_STATUS.FAIL);
            }
        }).catch(e => {
            setSlotAPIStatus(API_STATUS.RETRY);
        });

    }, [NewAddress.AssignmentId, parentLeadId, setNewAddress, setAppointmentSlotListBySlotId])

    useEffect(() => {
        if (parentLeadId !== 0) {
            GetAvailableSlotsList();
        }
    }, [GetAvailableSlotsList, parentLeadId]);


    useEffect(() => {
        // Bind slots as per appointmentDateTime
        if (Array.isArray(dateMaster) && dateMaster.length > 0) {
            setAppointmentDateTimeSlots();


        }
    }, [appointmentDateTime, dateMaster, setAppointmentDateTimeSlots])

    if (!show) return null;
    return (
        <>

            <FormSectionHeading
                icon={CONFIG.PUBLIC_URL + "/images/salesview/apDate.svg"}
                title="Appointment date & available slots"
                alt="Time"
            />

            {[7,1000,115].indexOf(productId)>-1&& !IsRescheduling &&  !IsEditAppDetails && <div className="GetPrioritySection">
                 <h3>Get Customer Prefered Slots from Monday to Saturday</h3>                                  
                <p>To ensure optimal service, please prefer pitching appointments scheduling for weekdays and Saturdays—Sunday slots are limited.</p>
                </div>}

            <Grid container spacing={2} className="pd25">
                <div className="blueBg">
                    {appointmentDateTime && <Grid item sm={12} md={12} xs={12}>
                        {/* <p className="scheduledDateTime">Appointment scheduled for : <span>{dayjs(appointmentDateTime).format("DD/MM/YYYY  HH:mm (dddd)")}</span></p> */}
                    </Grid>}
                    {!viewOnly && <Grid item sm={12} md={12} xs={12}>
                        <p className="availableSlot">Select date</p>
                        <ul className="TimeSlotCalendar">
                            {Array.isArray(dateMaster) && dateMaster.length > 0 && dateMaster.map(dateObj =>
                                <DateSlot
                                    key={dateObj.date}
                                    NewAddress={NewAddress}
                                    handleChange={handleChange}
                                    dateObj={dateObj}
                                    disabled={(!IsRescheduling && productId == 2 && !IsLeadRenewal && IsSpokeCities(NewAddress.OfflineCity?.CityId) && !(IsSpokeCitiesDays(dateObj.dateObject?.getDay()) && SpokeCitiesAppointmentCount(TotalAppointmentsByCityId, dateObj.date))) || disabled}

                                />
                            )}
                        </ul>

                        <p className="availableSlot">Available slots</p>
                        {slotAPIStatus === API_STATUS.LOADING && <CircularProgress size="20px" />}
                        {slotAPIStatus === API_STATUS.FAIL && <h5>Slots could not be loaded, Please try again after some time</h5>}
                        {slotAPIStatus === API_STATUS.RETRY && <><h5>Slots could not be loaded!</h5> <Button variant="contained" onClick={GetAvailableSlotsList} >Click here to Retry</Button></>}
                        <ul className="timeSlot">
                            {Array.isArray(slotMaster) && slotMaster.length > 0
                                && slotMaster.map((slot) =>
                                    <TimeSlot
                                        key={slot.SlotId}
                                        slot={slot}
                                        unavailableSlots={unavailableSlots}
                                        NewAddress={NewAddress}
                                        handleChange={handleChange}
                                        disabled={disabled}
                                        productId={productId}
                                        IsLeadRenewal={IsLeadRenewal}
                                        AppointmentSlotAvailability={Array.isArray(AppointmentSlotListBySlotId) && AppointmentSlotListBySlotId.filter((s) => s.SlotId == slot.SlotId)}
                                         Block8AMCityList={Block8AMCityList}
                                         BlockSlotIds={BlockSlotIds}
                                         IsRescheduling={IsRescheduling}
                                         isSrcReferralOrFOSApp={isSrcReferralOrFOSApp}
                                    // IsActive={slot.IsActive? true: false}
                                    />
                                )}
                        </ul>
                    </Grid>}
                </div>
            </Grid>
        </>
    )

}
export default AppointmentDateTimeSection;