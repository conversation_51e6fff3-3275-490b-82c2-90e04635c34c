<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="607.247" height="100.681" viewBox="0 0 607.247 100.681">
  <defs>
    <clipPath id="clip-path">
      <rect id="Rectangle_644" data-name="Rectangle 644" width="607.247" height="100.681" fill="none"/>
    </clipPath>
    <clipPath id="clip-path-2">
      <rect id="Rectangle_643" data-name="Rectangle 643" width="607.247" height="100.68" fill="none"/>
    </clipPath>
    <clipPath id="clip-path-3">
      <rect id="Rectangle_640" data-name="Rectangle 640" width="607.247" height="45.617" fill="none"/>
    </clipPath>
    <clipPath id="clip-path-4">
      <rect id="Rectangle_641" data-name="Rectangle 641" width="505.303" height="42.476" fill="none"/>
    </clipPath>
    <clipPath id="clip-path-5">
      <rect id="Rectangle_642" data-name="Rectangle 642" width="491.65" height="49.088" fill="none"/>
    </clipPath>
  </defs>
  <g id="Group_10142" data-name="Group 10142" transform="translate(-552.17 -502.068)">
    <g id="Group_10155" data-name="Group 10155" transform="translate(552.17 502.068)">
      <g id="Group_10154" data-name="Group 10154" transform="translate(0 0)" clip-path="url(#clip-path)">
        <g id="Group_10153" data-name="Group 10153" transform="translate(0 0.001)">
          <g id="Group_10152" data-name="Group 10152" clip-path="url(#clip-path-2)">
            <g id="Group_10145" data-name="Group 10145" transform="translate(0 32.007)" opacity="0.7">
              <g id="Group_10144" data-name="Group 10144">
                <g id="Group_10143" data-name="Group 10143" clip-path="url(#clip-path-3)">
                  <path id="Path_752" data-name="Path 752" d="M112.759,53.82C98.745,49.9,8.145,58.047,1.963,57.218-3.547,55.84,3.791,26.987,8.669,22.54c1.727-1.573,3.78-1.971,8.543-1.425,4.425.511,45.657-1.826,104.568-4.933,1.312.854,5.071-2.224,5.817-1.161,1.282,1.356,49.2-.56,140.715-2.416,16.686-.338,29.045-.516,32.4-.557,8.142-.1,1.691,1.62,11.283,1,20.436-1.32,64.926.6,80.4-.656,12.411-1,15.077-1.029,26.945.25,6.062.653,25.24.961,43.831,1.079s36.622-.036,40.328-.931c1.831-.442,3.2-.231,3.176.406,1.708.159,21.045,1.186,22.758,1.29.349.019,22.769,1.37,22.887,1.7.324.648,1.686,1,3,.631,2.388-.667,50.9-.53,51.6,1.359,2.015,5.419-5.809,27.414-11.615,32.65-6.045,5.449-36.548-2.6-62.521-.178,0,0-7.349-.269-18.374-.675-109.311-4.014-103.975-2.49-124.52-1.115-2.847-1.963-14.012,2.523-20.7-.862-2.8-1.417-22.824-1.768-27.3-.486-.961.275-8.093.253-18.41.129-32.07-.387-25.644-.546-57.9-.357-10,.06-35.785.478-40.575,2.3-20.241-3.143-92.2,2.732-112.252,4.233" transform="translate(0 -11.658)" fill="#feef38"/>
                </g>
              </g>
            </g>
            <g id="Group_10148" data-name="Group 10148" transform="translate(56.169 58.205)" opacity="0.7">
              <g id="Group_10147" data-name="Group 10147">
                <g id="Group_10146" data-name="Group 10146" clip-path="url(#clip-path-4)">
                  <path id="Path_753" data-name="Path 753" d="M125.794,63.678c-10.47-4.03-86.963.269-92.546-.942C28.706,61.111,35.807,32.6,40,28.4c1.481-1.5,3.193-1.732,7.132-1.032,6.232,1.315,86.86-1.068,86.86-1.068,1.072.887,4.244-2.089,4.844-1.008,1.869,2.509,132.521-.62,143.661-.744,6.759-.074,1.418,1.636,9.37.958,16.947-1.334,53.886.063,66.707-1.482,10.273-1.279,12.485-1.3,22.358-.247,10.088.95,63.678-.28,69.795-2.259,1.494-.546,2.639-.346,2.648.291,0,.132,37.775.675,37.951,1.169a2,2,0,0,0,2.508.5c1.963-.653,42.171-2.968,42.826-1.109,2,5.282-3.411,27.722-8.056,33.182-4.741,5.825-31.026-.785-52.114,2.7l-98.233.135-21.046,2.141c-2.415-1.908-11.682,2.855-17.3-.552-2.378-1.353-19.1-1.592-22.794-.173-3.205,1.1-88.8-1.864-97.563,1.93-15.645-3.228-79.059,1.2-93.764,1.944" transform="translate(-31.784 -21.203)" fill="#feef38"/>
                </g>
              </g>
            </g>
            <g id="Group_10151" data-name="Group 10151" transform="translate(49.593 -0.001)" opacity="0.7">
              <g id="Group_10150" data-name="Group 10150" transform="translate(0 0)">
                <g id="Group_10149" data-name="Group 10149" clip-path="url(#clip-path-5)">
                  <path id="Path_754" data-name="Path 754" d="M122.539,49.088c-10-3.939-84.561.236-90.432-1.057-4.427-1.642,2.618-30.116,6.683-34.308,1.448-1.493,3.132-1.793,6.94-1.008,6.24,1.282,76.667-.774,84.5-1.029,1.052.884,4.109-2.1,4.7-1.027.92,1.255,33.154.991,66.5.222s67.813-2.059,73.227-2.2c6.571-.167,1.417,1.587,9.142.818,16.437-1.639,52.428-.793,64.84-2.633,9.95-1.477,12.087-1.5,21.736-.7,9.842.807,61.972-1.666,67.769-3.942,1.441-.563,2.55-.417,2.587.217,0,0,.578.025,1.583.025,5.212-.008,2.31-.03,16.826-.091.423,0,18.418-.167,18.547.162a1.935,1.935,0,0,0,2.462.426c1.884-.681,40.818-4.28,41.579-2.452,2.178,5.23-1.843,27.766-6.01,33.407-4.39,5.944-30.327.217-50.737,4.3,0,0-23.975.733-47.957,1.384-57.548,1.557-42.651.546-68.442,3.78-2.422-1.856-11.252,3.02-16.913-.22-2.34-1.34-18.7-1.252-22.271.233-1.549.563-23.251.469-45.62.645s-45.407.6-49.648,2.556c-15.337-3.042-77.222,1.743-91.6,2.487" transform="translate(-30.698 0.001)" fill="#feef38"/>
                </g>
              </g>
            </g>
          </g>
        </g>
      </g>
    </g>
  </g>
</svg>
