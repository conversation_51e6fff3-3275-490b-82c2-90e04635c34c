import { SV_CONFIG } from "../appconfig";
import { localStorageCache } from "../utils/utility";
import rootScopeService from './rootScopeService';

import { CALL_API } from ".";
// import User from "./user.service";


const masterService = {
    getSupplierByProduct: (productId) => {
        let key = "GetSupplierByProduct" + productId;
        let cachedData = localStorageCache.readFromCache(key);

        if (cachedData !== null) {
            return Promise.resolve(cachedData)
        }

        const input = {
            url: "api/Master/GetSupplierByProduct?ProductId=" + productId,
            method: 'GET',
            service: 'MatrixCoreAPI',
        }

        return CALL_API(input).then(function (response) {
            localStorageCache.writeToCache(key, response);
            return response;
        });
    },
    getSuppliers: () => {
        let cachedData = localStorageCache.readFromCache('Suppliers');
        if (cachedData !== null) { return Promise.resolve(cachedData) }

        const input = {
            url: `coremrs/api/Master/Suppliers`,
            method: 'GET',
            service: "MatrixCoreAPI",
        };
        return CALL_API(input).then(function (response) {
            localStorageCache.writeToCache('Suppliers', response);
            return response;
        });
    },
    // GetProductPlans: (productId) => {
    //     let cachedData = localStorageCache.readFromCache(`GetProductPlans${productId}`);
    //     if (cachedData !== null) { return Promise.resolve(cachedData) }

    //     const input = {
    //         url: `Master/GetProductPlans/${productId}`,
    //         method: 'GET', service: 'core', timeout: 'l'
    //     };
    //     return CALL_API(input).then(function (response) {
    //         response = response.GetProductPlansResult.Data;
    //         localStorageCache.writeToCache(`GetProductPlans${productId}`, response);
    //         return response;
    //     })
    // },
    GetProductPlansFromCore: (ProductId, SupplierId, Source) => {
        const input = {
            url: "api/Master/GetProductPlans?ProductId=" + ProductId + "&supplierId=" + SupplierId + "&Source=" + Source,
            method: 'GET',
            service: 'MatrixCoreAPI',
        }
        if (ProductId && SupplierId) {
            return CALL_API(input);
        }
        else {
            return Promise.reject(new Error('Invalid ProductId or SupplierId'));
        }
    },
    getCrossSellProducts: (productId, roleId) => {

        let cachedData = localStorageCache.readFromCache(`getCrossSellProducts${productId}`);
        if (!!cachedData) { return Promise.resolve(cachedData) }

        const input = {
            url: `coremrs/api/Master/GetCrossSellProducts/${productId}`,
            method: 'GET', service: 'MatrixCoreAPI', timeout: 'l'
        };
        return CALL_API(input).then(function (response) {
            if (!!response && Array.isArray(response) && response.length > 0) { localStorageCache.writeToCache(`getCrossSellProducts${productId}`, response) };
            return response;
        })

    },

    getSubProductByProductID: (productId) => {
        let cachedData = localStorageCache.readFromCache(`GetSubProduct${productId}`);
        if (!!cachedData) { return Promise.resolve(cachedData) }
        let input = {}
        input = {
            url: `coremrs/api/Master/GetSubProduct/${productId}`,
            method: 'GET', service: 'MatrixCoreAPI'
        };
        return CALL_API(input).then(response => {
            if (!!response && Array.isArray(response) && response.length > 0) { localStorageCache.writeToCache(`GetSubProduct${productId}`, response) };
            return response;
        });

    },

    getCountriesValidations: (productId) => {
        let cachedData = localStorageCache.readFromCache(`GetCountryValidations`);
        let cacheExpiration = 12 * 60 * 60 * 1000; // 24 hours in milliseconds

        let isCacheValid = cachedData && cachedData.timestamp && Date.now() - cachedData.timestamp < cacheExpiration;

        if (isCacheValid) {
            // Filter only relevant CountryList for the given productId
            const productData = cachedData?.data?.find(item => item.ProductId.includes(productId));
            return Promise.resolve(productData ? productData.CountryList : []);
        }
    
        let input = {
            url: `api/SalesView/GetConfig?Key=CountryValidation`,
            method: 'GET',
            service: 'MatrixCoreAPI'
        };
    
        return CALL_API(input).then(response => {
            if (response && response.Countries && Array.isArray(response.Countries) && response.Countries.length > 0) {
                // Cache the entire Countries list
                const newCacheData = { data: response.Countries, timestamp: Date.now() };
                localStorageCache.writeToCache('GetCountryValidations', newCacheData);
    
                // Find country list for the given productId
                const productData = response.Countries.find(item => item.ProductId.includes(productId));
                return productData ? productData.CountryList : [];
            }
            return [];
        });
    },

    getAnnualIncomeListByPrdId: (productId) => {
        let cachedData = localStorageCache.readFromCache(`GetAnnualIncomeListByPrdId${productId}`);
        if (cachedData !== null) { return Promise.resolve(cachedData) }

        const input = {
            url: `coremrs/api/Master/GetAnnualIncomebyProductId/${productId}`,
            method: 'GET', service: 'MatrixCoreAPI'
            
        };
        return CALL_API(input).then(response => {
            if(response !== null){
                localStorageCache.writeToCache(`GetAnnualIncomeListByPrdId${productId}`, response);
            }
            return response;
        });
    },
    GetCallingDetails: (CustomerId, LeadId) => {
        let ProductId = rootScopeService.getProductId();

        const input = {
            url: "onelead/api/Communication/getCallingNoData/" + CustomerId + '/' + ProductId + '/' + LeadId,
            method: 'GET', service: 'MatrixCoreAPI',
        }

        return CALL_API(input); //Getcallingdetails

    },
    getSubProductType: (productId) => {
        let cachedData = localStorageCache.readFromCache(`GetSubProductType${productId}`);
        if (cachedData !== null) { return Promise.resolve(cachedData) }

        const input = {
            url: `coremrs/api/Master/GetSubProductType/${productId}`,
            method: 'GET', service: 'MatrixCoreAPI'
        };
        return CALL_API(input).then(function (response) {
            localStorageCache.writeToCache(`GetSubProductType${productId}`, response);
            return response;
        });
    },

    getVehicalMakeListByProduct: (productId) => {
        let cachedData = localStorageCache.readFromCache(`GetVehicalMakeListByProduct${productId}`);
        if (cachedData !== null) { return Promise.resolve(cachedData) }

        const input = {
            url: `coremrs/api/Master/GetVehicalMakeListByProduct/${productId}`,
            method: 'GET', service: 'MatrixCoreAPI'
        };
        return CALL_API(input).then(function (response) {
            if(response !== null){
                localStorageCache.writeToCache(`GetVehicalMakeListByProduct${productId}`, response);
            }
            return response;
        });
    },
    getMakeModelListByProduct: (productId, makeId) => {
        const input = {
            url: `coremrs/api/Master/GetMakeModelListByProduct/${productId}/${makeId}`,
            method: 'GET', service: 'MatrixCoreAPI'
        };
        return CALL_API(input);
    },
    getPaymentModes: (productId) => {
        let cachedData = localStorageCache.readFromCache(`GetPaymentModes${productId}`);
        if (cachedData !== null) { return Promise.resolve(cachedData) }
        // const input = {
        //     url: `Master/GetPaymentModes/${productId}`,
        //     method: 'GET', service: 'core'
        // };
        const input = {
            url: `coremrs/api/Master/GetPaymentModes/${productId}`,
            method: 'GET', service: "MatrixCoreAPI",
        };
        return CALL_API(input).then(function (response) {
            // response = response.GetPaymentModesResult.Data;
            localStorageCache.writeToCache(`GetPaymentModes${productId}`, response);
            return response;
        });
    },
    getCities: () => {
        let cachedData = localStorageCache.readFromCache(`GetCityList`);
        if (!!cachedData) { return Promise.resolve(cachedData) }

        const input = {
            url: `coremrs/api/Master/GetCityList`,
            method: 'GET', service: "MatrixCoreAPI",
        };
        return CALL_API(input).then(function (response) {
            if (!!response && Array.isArray(response) && response.length > 0) { localStorageCache.writeToCache(`GetCityList`, response) };
            return response;
        });

        // else {
        //     const input = {
        //         url: `Master/GetCityList`,
        //         method: 'GET', service: 'core'
        //     };
        //     return CALL_API(input).then(function (response) {
        //         response = response.GetCityListResult.Data;
        //         if (Array.isArray(response) && response.length > 0) {
        //             localStorageCache.writeToCache(`GetCityList`, response);
        //         }
        //         return response;
        //     });
        // }

    },
    getSMEOccupationList: () => {
        // let cachedData = localStorageCache.readFromCache(`GetSMEOccupationList`);
        // if (cachedData !== null) { return Promise.resolve(cachedData) }
        const input = {
            url: `coremrs/api/Master/GetSMEOccupationList`,
            method: 'GET', service: "MatrixCoreAPI",
        };
        // const input = {
        //     url: `Master/GetSMEOccupationList`,
        //     method: 'GET', service: 'core'
        // };
        return CALL_API(input).then(function (response) {
            //response = response.GetSMEOccupationListResult;
            // localStorageCache.writeToCache(`GetSMEOccupationList`, response);
            return response;
        });
    },
    getSMEPlanDeal: () => {
        let cachedData = localStorageCache.readFromCache(`GetSMEPlanDeal`);
        if (cachedData !== null) { return Promise.resolve(cachedData) }
        // const input = {
        //     url: `Master/GetSMEPlanDeal`,
        //     method: 'GET', service: 'core'
        // };
        const input = {
            url: `coremrs/api/Master/GetSMEPlanDeal`,
            method: 'GET', service: "MatrixCoreAPI",
        };
        return CALL_API(input).then(function (response) {
            //response = response.GetSMEPlanDealResult.Data;
            localStorageCache.writeToCache(`GetSMEPlanDeal`, response);
            return response;
        });
    },
    // common service
    updateagentstatus: (EmpId, Status) => {
        
        
        const input = {
            url: `onelead/api/LeadPrioritization/updateagentstatus?onCall=false`,
            method: 'POST', service: 'MatrixCoreAPI',
            requestData: { 'AgentCode': EmpId, 'status': Status, 'opensv': 0, 'TotalCalls': 0, 'TotalTalkTime': 0 }
        };

        return CALL_API(input).then(function (response) {
            return response;
        });
    },
    updateagentloginstatus: (EmpId, Status) => {
        const input = {
            url: `onelead/api/Dialer/updateAgentLoginStatus`,
            method: 'POST', service: 'MatrixCoreAPI',
            requestData: { 'AgentCode': EmpId, 'status': Status}
        };
        return CALL_API(input).then(function (response) {
            return response;
        });
    },
    getagenstats: (UserId) => {
        const input = {
            url: SV_CONFIG["CustomerNotificationURL"][SV_CONFIG["environment"]] + "customer/getagenstats/" + UserId + "/",
            method: 'GET', service: 'custom'
        };
        return CALL_API(input).then(function (response) {
            if (response.length > 0) {
                return response;
            }
            else {
                return [];
            }

        });
    },
    getMasterTypeList: (MasterTypeId) => {
        const input = {
            url: `coremrs/api/Master/GetMasterTypeList/${MasterTypeId}`,
            method: 'GET', service: 'MatrixCoreAPI'
        };
        return CALL_API(input).then(function (response) {
            return response;
        });
    },
    GetShopTypes: () => {
        const input = {
            url: "api/Master/GetShopTypes",
            method: 'GET',
            service: 'MatrixCoreAPI',
        }
        return CALL_API(input).then(function (response) {
            return response;
        });
    },
    GetCountryList: () => {
        const input = {
            url: `coremrs/api/Master/GetCountryList`,
            method: "GET",
            service: "MatrixCoreAPI",
        };

        return CALL_API(input); //GetCountryList

    },
    GetUserGroups: (ProductId, SourcePage) => {
        const input = {
            url: `api/SalesView/GetUserGroup?ProductID=${ProductId}&SourcePage=${SourcePage}`,
            method: 'GET',
            service: 'MatrixCoreAPI'
        };
        return CALL_API(input);
    },
    GetUserList: (ProductId, RoleId) => {
        const input = {
            url: `coremrs/api/MRSCore/GetUserList?ProductId=${ProductId}&RoleId=${RoleId}`, method: 'GET', service: 'MatrixCoreAPI',
        };
        return CALL_API(input);
    },
    GetProfessions: () => {
        const input = {
            url: "api/Master/GetProfessions",
            method: 'GET',
            service: 'MatrixCoreAPI',
        }
        return CALL_API(input).then(function (response) {
            return response;
        });
    },
    queueLogin: () => {

        // try {
        //     let isCallPicked = window.localStorage.getItem('iscallpicked');
        //     if(AgentCallctxapi && AgentCallctxapi.popwin && (isCallPicked == "false" || isCallPicked == null)){
        //         var user = window.atob(window.localStorage.getItem('User'));
        //         user = jQuery.parseJSON(user);
        //         AgentCallctxapi.popwin.queueAction('login', user.EmployeeId, user.Queue, user.Asterisk_IP);
        //     }
        // }
        // catch (e) { console.log(e); }
    },
    GetDoctorAssociations: () =>{
        try{
            let key = "GetDoctorAssociations";
            let cachedData = localStorageCache.readFromCache(key);
    
            if (cachedData !== null) {
                return Promise.resolve(cachedData)
            }
            const input = {
                url: `coremrs/api/Master/GetDoctorAssociations`,
                method: 'GET',
                service: "MatrixCoreAPI",
            };
           return CALL_API(input).then(function (response) {
            localStorageCache.writeToCache(key, response);
            return response;
        });
        }
        catch{
            return [];
        }
       
    },
    GetSmeRenewalLeadBookingDetails: (leadId) => {
        const input = {
            url: "api/SalesView/GetSmeRenewalLeadBookingDetails?LeadId=" + leadId,
            method: 'GET',
            service: 'MatrixCoreAPI',
        }
        return CALL_API(input).then(function (response) {
            return response;
        });
    },
    //SME GMC
    GetPerLifeRaterUrl: (reqData) => {
        const input = {
            url: `api/SME/GeneratePerLifeRateURL`,
            method: "POST",
            service: "MatrixCoreAPI",
            requestData: reqData,
            //timeout: 's'
            timeout: 3000
        };
        return CALL_API(input);
    },
}

export default masterService;