import React, { useState, useEffect } from "react";
import rootScopeService from "../../../../services/rootScopeService";
import ModalPopup from "../../../../components/Dialogs/ModalPopup";
import { <PERSON><PERSON>, But<PERSON> } from "@mui/material";
import { CONFIG } from "../../../../appconfig";
import { CALL_API } from "../../../../services";
import { useSnackbar } from "notistack";
import DeleteIcon from '@mui/icons-material/Delete';
import { updateStateInRedux } from "../../../../store/actions/SalesView/SalesView";
import { useDispatch } from "react-redux";
import User from "../../../../services/user.service";
import { gaEventTracker } from "../../../../helpers";

let policyCopyDocTypeId = 56;
let invoiceDocTypeId = 67;
let gstCopyDocTypeId = 774;
let panCopyDocTypeId = 3;
let paymentProofDocTypeId = 853;

const DocsUploadComponent = (props) => {
    const { LeadId, SubProductId, IsSmePOS, TransitType, fileType, docTypeId, Id, setNewUploadedFiles, NewUploadedFiles, setUploadedFiles, UploadedFiles, setIsFileUploading } = props;
    const [AllFiles, setAllFiles] = useState({});
    const { enqueueSnackbar } = useSnackbar();
    const [file, setfile] = useState();
    const [DeletedFile, setDeletedFile] = useState();
    const IsDisabled = file ? false : true;
    let productId = rootScopeService.getProductId();
    let customerId = rootScopeService.getCustomerId();
    
    useEffect(() => {
        setfile(GetFileData(UploadedFiles, docTypeId));
    }, [UploadedFiles])

    const GetFileData = (files, docTypeId) => {
        let file = null;
        if (files && Array.isArray(files) && docTypeId) {
            files.forEach(f => {
                if (f.DocTypeId == docTypeId) {
                    file = f;
                }
            })
        }
        return file;
    };

    const HandleFileUpload = async (file, docTypeId) => {
        if (file) {
            const data = {
                UploadedFile: file,
                DocTypeId: docTypeId,
                LeadId: LeadId,
                CustomerId: customerId,
                ProductId: productId,
                timeout: 6000
            };
            const input = {
                url: "api/UploadFile/UploadPolicy_V2",
                method: 'POST',
                service: 'commonUploadFileToUrl',
                requestData: data
            };
            try{
                gaEventTracker('SME_UploadPolicy_V2', document.URL, LeadId);
            }catch{}
            return CALL_API(input);
        }
    };

    const HandleMiscFileUpload = async (file, type) => {
        if (productId == 131) {
            const data = {
                UploadedFile: file,
                LeadId: LeadId,
                CustomerId: customerId,
                ProductId: productId,
                EnquiryId: 0,
                Type: type
            }
            const input = {
                url: "api/UploadFile/UploadMiscDocument",
                method: 'POST',
                service: 'commonUploadFileToUrl',
                requestData: data,
                timeout: 6000
            };
            try{
                gaEventTracker('SME_UploadMiscDocument', document.URL, LeadId);
            }catch{}
            return CALL_API(input);
        }
    };

    const UploadCustomerProof = async (file, type) => {
        if (productId == 131) {
            const data = {
                UploadedFile: file,
                LeadId: LeadId,
                CustomerId: customerId,
                ProductId: productId,
                EnquiryId: 0,
                Type: type
            }
            const input = {
                url: "api/UploadFile/UploadCustomerProof",
                method: 'POST',
                service: 'commonUploadFileToUrl',
                requestData: data,
                timeout: 6000
            };
            try{
                gaEventTracker('SME_UploadCustomerProof', document.URL, LeadId);
            }catch{}
            return CALL_API(input);
        }
    };

    const DownloadDocument = (documentId, documentTypeId) => {
        var request = {
            custId: customerId.toString(),
            docId: documentId,
            docTypeId: documentTypeId
        }
        GetDocumentPath(request).then
            ((response) => {
                if (response && response.ttlDocUrl) {
                    window.open(response.ttlDocUrl);
                }
                else {
                    window.alert("Error: " + response.statusMsg);
                }
            });
    };

    const GetDocumentPath = (request) => {
        const _input = {
            url: `api/SalesView/GetDocumentUrl`,
            method: 'POST',
            service: 'MatrixCoreAPI',
            requestData: request,
            timeout: 6000
        }
        return CALL_API(_input);
    };

    const GetFileName = (docTypeId) => {
        let fileName = 'Attach';
        if (docTypeId == policyCopyDocTypeId) {
            fileName = (AllFiles && AllFiles.PolicyCopy) ? (AllFiles.PolicyCopy.name) : fileName
        }
        else if (docTypeId == invoiceDocTypeId) {
            fileName = (AllFiles && AllFiles.InvoiceCopy) ? (AllFiles.InvoiceCopy.name) : fileName
        }
        else if (docTypeId == gstCopyDocTypeId) {
            fileName = (AllFiles && AllFiles.GstCopy) ? (AllFiles.GstCopy.name) : fileName
        }
        else if (docTypeId == panCopyDocTypeId) {
            fileName = (AllFiles && AllFiles.PanCopy) ? (AllFiles.PanCopy.name) : fileName
        }
        else if (docTypeId == paymentProofDocTypeId) {
            fileName = (AllFiles && AllFiles.PaymentProof) ? (AllFiles.PaymentProof.name) : fileName
        }
        return fileName;
    };

    const OnDocAttached = (e, docTypeId) => {
        if (docTypeId == policyCopyDocTypeId) {
            setAllFiles(prevState => ({ ...prevState, PolicyCopy: e.target.files[0] }));
        }
        else if (docTypeId == invoiceDocTypeId) {
            setAllFiles(prevState => ({ ...prevState, InvoiceCopy: e.target.files[0] }));
        }
        else if (docTypeId == gstCopyDocTypeId) {
            setAllFiles(prevState => ({ ...prevState, GstCopy: e.target.files[0] }));
        }
        else if (docTypeId == panCopyDocTypeId) {
            setAllFiles(prevState => ({ ...prevState, PanCopy: e.target.files[0] }));
        }
        else if (docTypeId == paymentProofDocTypeId) {
            setAllFiles(prevState => ({ ...prevState, PaymentProof: e.target.files[0] }));
        }
    };

    const SetUploadedDocs = (docDetails, docTypeId) => {
        if (docDetails) {
            let files = [];
            if (NewUploadedFiles && NewUploadedFiles.length > 0) {
                files = JSON.parse(JSON.stringify(NewUploadedFiles));
                let index = files.findIndex(item => item.DocTypeId === docTypeId);
                if (index > -1) {
                    files.splice(index, 1);
                }
            }
            files.push(docDetails);
            setNewUploadedFiles(files);
        }
    };

    const OnDocUpload = async (docTypeId) => {
        try {
            setIsFileUploading(true);
            let file = null;
            let docDetails = null;
            let msg = null;
            if (docTypeId == policyCopyDocTypeId) {
                file = (AllFiles && AllFiles.PolicyCopy) ? AllFiles.PolicyCopy : null;
                if (file) {
                    if (file.name.substring(file.name.lastIndexOf('.') + 1).toLowerCase() === "pdf") {
                        let fileUploadResponse = await HandleFileUpload(file, docTypeId);
                        if (fileUploadResponse && fileUploadResponse.Status) {
                            docDetails = OnUploadSuccess(fileUploadResponse.Data.policyCopyDetails.policyDocId, docTypeId, file.name, 'Policy Copy');
                        }
                        else {
                            msg = fileUploadResponse.Message ? fileUploadResponse.Message : "Upload failed";
                        }
                    }
                    else {
                        msg = "Policy Copy is required in pdf format.";
                    }
                }
            }
            else if (docTypeId == invoiceDocTypeId) {
                file = (AllFiles && AllFiles.InvoiceCopy) ? AllFiles.InvoiceCopy : null;
                if (file) {
                    if (["pdf", "docx", "doc"].indexOf(file.name.substring(file.name.lastIndexOf('.') + 1).toLowerCase()) > -1) {
                        let fileUploadResponse = await HandleMiscFileUpload(file, "Invoice");
                        if (fileUploadResponse && fileUploadResponse.ok) {
                            docDetails = OnUploadSuccess(fileUploadResponse.docId, docTypeId, file.name, 'Invoice');
                        }
                        else {
                            msg = fileUploadResponse.msg ? fileUploadResponse.msg : "Upload failed";
                        }
                    }
                    else {
                        msg = "Invoice file is required in pdf/word format."
                    }
                }
            }
            else if (docTypeId == gstCopyDocTypeId) {
                file = (AllFiles && AllFiles.GstCopy) ? AllFiles.GstCopy : null;
                if (file) {
                    if (file.name.substring(file.name.lastIndexOf('.') + 1).toLowerCase() === "pdf") {
                        let fileUploadResponse = await UploadCustomerProof(file, "GSTCopy");
                        if (fileUploadResponse && fileUploadResponse.ok) {
                            docDetails = OnUploadSuccess(fileUploadResponse.docId, docTypeId, file.name, 'GST Copy');
                        }
                        else {
                            msg = fileUploadResponse.msg ? fileUploadResponse.msg : "Upload failed";
                        }
                    }
                    else {
                        msg = "GST Copy should be in pdf format.";
                    }
                }
            }
            else if (docTypeId == panCopyDocTypeId) {
                file = (AllFiles && AllFiles.PanCopy) ? AllFiles.PanCopy : null;
                if (file) {
                    if (["pdf", "jpeg", "png", "jpg"].indexOf(file.name.substring(file.name.lastIndexOf('.') + 1).toLowerCase()) > -1) {
                        let fileUploadResponse = await UploadCustomerProof(file, "PANCopy");
                        if (fileUploadResponse && fileUploadResponse.ok) {
                            docDetails = OnUploadSuccess(fileUploadResponse.docId, docTypeId, file.name, 'PAN Copy');
                        }
                        else {
                            msg = fileUploadResponse.msg ? fileUploadResponse.msg : "Upload failed";
                        }
                    }
                    else {
                        msg = "PAN Copy should be in pdf/jpeg/jpg/png format.";
                    }
                }
            }
            else if (docTypeId == paymentProofDocTypeId) {
                file = (AllFiles && AllFiles.PaymentProof) ? AllFiles.PaymentProof : null;
                if (file) {
                    if (["pdf", "jpeg", "png", "jpg", "docx", "doc"].indexOf(file.name.substring(file.name.lastIndexOf('.') + 1).toLowerCase()) > -1) {
                        let fileUploadResponse = await HandleMiscFileUpload(file, "paymentReceipt");
                        if (fileUploadResponse && fileUploadResponse.ok) {
                            docDetails = OnUploadSuccess(fileUploadResponse.docId, docTypeId, file.name, 'Payment Proof');
                        }
                        else {
                            msg = fileUploadResponse.msg ? fileUploadResponse.msg : "Upload failed";
                        }
                    }
                    else {
                        msg = "Payment Proof is required in pdf/word/image format."
                    }
                }
            }
            SetUploadedDocs(docDetails, docTypeId);
            ShowUploadError(msg, docTypeId);
        }
        catch {
            // Exception handling
        }
        finally {
            setIsFileUploading(false);
        }
    };

    const ShowUploadError = (message, docTypeId) => {
        if (message) {
            enqueueSnackbar(message, { variant: 'error', autoHideDuration: 3000, });
            setNewUploadedFiles({});
            if (docTypeId == policyCopyDocTypeId) {
                setAllFiles(prevState => ({ ...prevState, PolicyCopy: null }));
            }
            else if (docTypeId == invoiceDocTypeId) {
                setAllFiles(prevState => ({ ...prevState, InvoiceCopy: null }));
            }
            else if (docTypeId == gstCopyDocTypeId) {
                setAllFiles(prevState => ({ ...prevState, GstCopy: null }));
            }
            else if (docTypeId == panCopyDocTypeId) {
                setAllFiles(prevState => ({ ...prevState, PanCopy: null }));
            }
            else if (docTypeId == paymentProofDocTypeId) {
                setAllFiles(prevState => ({ ...prevState, PaymentProof: null }));
            }
            if (DeletedFile) {
                let filesUploaded = JSON.parse(JSON.stringify(UploadedFiles));
                filesUploaded.push(DeletedFile);
                setUploadedFiles(filesUploaded);
            }
        }
    };

    const OnUploadSuccess = (docId, docTypeId, fileName, fileType) => {
        let docDetails = {
            DocId: docId,
            DocTypeId: docTypeId,
            FileName: fileName
        };
        enqueueSnackbar("Successfully uploaded " + fileType, { variant: 'success', autoHideDuration: 3000, });
        return docDetails;
    };

    const HandleFileDelete = () => {
        let filesUploaded = JSON.parse(JSON.stringify(UploadedFiles));
        let index = filesUploaded.findIndex(item => item.DocTypeId === docTypeId);
        if (index > -1) {
            setDeletedFile(filesUploaded[index]);
            filesUploaded.splice(index, 1);
        }
        setUploadedFiles(filesUploaded);
    };

    return <Grid container spacing={2}>
        <Grid item sm={7} md={7} xs={7} >
            <input
                id={Id}
                type="file"
                className="fileUploadAndViewBtnHide"
                onChange={(e) => { OnDocAttached(e, docTypeId) }}
                disabled={props.IsSmeFosAgentGroup && Id=="DocsUploadPolicyCopyId"}
            />
            <label htmlFor={Id}>
                {file &&
                    <Button
                        fullWidth={true}
                        className="fileUploadBtnText"
                        onClick={() => DownloadDocument(file.DocId, docTypeId)}>
                        {(<a>{file.FileName}</a>)}
                    </Button>}
                {!file &&
                    <Button
                        fullWidth={true}
                        className="fileUploadBtnText fileUploadBtnFlex"
                        component="span"
                        startIcon={<img src={CONFIG.PUBLIC_URL + "/images/salesview/attechment.svg"} />}>
                        {GetFileName(docTypeId)}
                    </Button>}
            </label>
        </Grid>
        <Grid item sm={5} md={5} xs={5} >
            <Button
                onClick={(e) => HandleFileDelete()}
                disabled={IsDisabled}
                className={IsDisabled ? "ClearIconDisabled" : "ClearIcon"}
                id={Id + "Btn"}
            >
                <DeleteIcon className="red" />
            </Button>
            <Button
                fullWidth={true}
                className="UploadBtn"
                component="span"
                onClick={() => OnDocUpload(docTypeId)}
            >
                {fileType}
            </Button>
        </Grid>
    </Grid>
}

export const DocsUpload = (props) => {
    const reduxDispatch = useDispatch();
    const { enqueueSnackbar } = useSnackbar();
    const [ExistingFiles, setExistingFiles] = useState([]);
    const [UploadedFiles, setUploadedFiles] = useState([]);
    const [NewUploadedFiles, setNewUploadedFiles] = useState([]);
    const [IsFileUploading, setIsFileUploading] = useState(false);
    let productId = rootScopeService.getProductId();
    let customerId = rootScopeService.getCustomerId();
    
    useEffect(() => {
        SaveDocuments(true);
    }, [])

    const IsValid = () => {
        let isValid = true, Message = "";
        if (productId === 131) {
            if (User && User.RoleId && User.RoleId == 13) {
                let paymentProofFile = null;
                if (NewUploadedFiles && NewUploadedFiles.length > 0) {
                    paymentProofFile = NewUploadedFiles.find(element => {
                        return element.DocTypeId == paymentProofDocTypeId;
                    });
                }
                if (!paymentProofFile && ExistingFiles && ExistingFiles.length > 0) {
                    paymentProofFile = ExistingFiles.find(element => {
                        return element.DocTypeId == paymentProofDocTypeId;
                    });
                }
                if (!paymentProofFile ||
                    (paymentProofFile && paymentProofFile.FileName && ["pdf", "jpeg", "png", "jpg", "docx", "doc"].indexOf(paymentProofFile.FileName.substring(paymentProofFile.FileName.lastIndexOf('.') + 1).toLowerCase()) === -1)) {
                    isValid = false;
                    Message += "Payment Proof is required in pdf/word/image format.\n";
                }
            }
            else {
                if (props.IsSmePOS) {
                    let policyCopyIndex = -1;
                    if (NewUploadedFiles && NewUploadedFiles.length > 0) {
                        policyCopyIndex = NewUploadedFiles.findIndex(item => item.DocTypeId === policyCopyDocTypeId && item.FileName && item.FileName.substring(item.FileName.lastIndexOf('.') + 1).toLowerCase() === "pdf")
                    }
                    if (policyCopyIndex === -1 && ExistingFiles && ExistingFiles.length > 0) {
                        policyCopyIndex = ExistingFiles.findIndex(item => item.DocTypeId === policyCopyDocTypeId && item.FileName && item.FileName.substring(item.FileName.lastIndexOf('.') + 1).toLowerCase() === "pdf")
                    }
                    if (policyCopyIndex === -1) {
                        isValid = false;
                        Message += "Policy Copy is required in pdf format.\n";
                    }

                    // let panFile = null;
                    // if (NewUploadedFiles && NewUploadedFiles.length > 0) {
                    //     panFile = NewUploadedFiles.find(element => {
                    //         return element.DocTypeId == panCopyDocTypeId;
                    //     });
                    // }
                    // if (!panFile && ExistingFiles && ExistingFiles.length > 0) {
                    //     panFile = ExistingFiles.find(element => {
                    //         return element.DocTypeId == panCopyDocTypeId;
                    //     });
                    // }
                    // if (!panFile ||
                    //     (panFile && panFile.FileName && ["pdf", "jpeg", "png", "jpg"].indexOf(panFile.FileName.substring(panFile.FileName.lastIndexOf('.') + 1).toLowerCase()) === -1)) {
                    //     isValid = false;
                    //     Message += "PAN Copy is required in pdf/jpeg/jpg/png format.\n";
                    // }
                }
                if (props.TransitType === "Single Transit") {
                    let invoiceFile = null;
                    if (NewUploadedFiles && NewUploadedFiles.length > 0) {
                        invoiceFile = NewUploadedFiles.find(element => {
                            return element.DocTypeId == invoiceDocTypeId;
                        });
                    }
                    if (!invoiceFile && ExistingFiles && ExistingFiles.length > 0) {
                        invoiceFile = ExistingFiles.find(element => {
                            return element.DocTypeId == invoiceDocTypeId;
                        });
                    }
                    if (!invoiceFile ||
                        (invoiceFile && invoiceFile.FileName && ["pdf", "docx", "doc"].indexOf(invoiceFile.FileName.substring(invoiceFile.FileName.lastIndexOf('.') + 1).toLowerCase()) === -1)) {
                        isValid = false;
                        Message += "Invoice file is required in pdf/word format.\n";
                    }
                }
            }
            if (!isValid) {
                enqueueSnackbar(Message, { variant: 'error', autoHideDuration: 6000, style: { whiteSpace: 'pre-line' } });
            }
            return isValid;
        };
    }

    const SaveDocuments = (isPageLoad) => {
        if (isPageLoad || IsValid()) {
            const data = {
                Documents: NewUploadedFiles,
                LeadId: props.LeadId
            }
            const input = {
                url: "api/SalesView/AddUpdateUploadedDocs",
                method: 'POST',
                service: 'MatrixCoreAPI',
                requestData: data
            };
            CALL_API(input).then((response) => {
                if (response && response.Status) {
                    if (!isPageLoad) {
                        reduxDispatch(updateStateInRedux({ key: "IsSmeDocsUploadValid", value: true }));
                        enqueueSnackbar("Successfully uploaded.", { variant: 'success', autoHideDuration: 3000, });
                        reduxDispatch(updateStateInRedux({ key: "DocsUploadPanel", value: false }))
                    }
                    setUploadedFiles(response.Data);
                    setExistingFiles(response.Data);
                }
                else {
                    enqueueSnackbar("Error occured", { variant: 'error', autoHideDuration: 3000, });
                    reduxDispatch(updateStateInRedux({ key: "IsSmeDocsUploadValid", value: false }))
                }
            });
        }
        else {
            reduxDispatch(updateStateInRedux({ key: "IsSmeDocsUploadValid", value: false }))
        }
    };

    return (
        <ModalPopup open={props.open} title='Upload Documents' className="DocUploadPopup" handleClose={props.handleClose}>
            {/* PolicyCopy */}
            <DocsUploadComponent
                fileType={"Upload Policy Copy"}
                docTypeId={policyCopyDocTypeId}
                Id={"DocsUploadPolicyCopyId"}
                setNewUploadedFiles={setNewUploadedFiles}
                NewUploadedFiles={NewUploadedFiles}
                setUploadedFiles={setUploadedFiles}
                UploadedFiles={UploadedFiles}
                setIsFileUploading={setIsFileUploading}
                {...props}
            />
            {/* Invoice Document */}
            <DocsUploadComponent
                fileType={"Upload Invoice"}
                docTypeId={invoiceDocTypeId}
                Id={"DocsUploadInvoiceCopyId"}
                setNewUploadedFiles={setNewUploadedFiles}
                NewUploadedFiles={NewUploadedFiles}
                setUploadedFiles={setUploadedFiles}
                UploadedFiles={UploadedFiles}
                setIsFileUploading={setIsFileUploading}
                {...props}
            />
            {/* PAN Copy */}
            <DocsUploadComponent
                fileType={"Upload PAN Copy"}
                docTypeId={panCopyDocTypeId}
                Id={"DocsUploadPanCopyId"}
                setNewUploadedFiles={setNewUploadedFiles}
                NewUploadedFiles={NewUploadedFiles}
                setUploadedFiles={setUploadedFiles}
                UploadedFiles={UploadedFiles}
                setIsFileUploading={setIsFileUploading}
                {...props}
            />
            {/* GST Copy */}
            <DocsUploadComponent
                fileType={"Upload GST Copy"}
                docTypeId={gstCopyDocTypeId}
                Id={"DocsUploadGstCopyId"}
                setNewUploadedFiles={setNewUploadedFiles}
                NewUploadedFiles={NewUploadedFiles}
                setUploadedFiles={setUploadedFiles}
                UploadedFiles={UploadedFiles}
                setIsFileUploading={setIsFileUploading}
                {...props}
            />
            <DocsUploadComponent
                fileType={"Upload Payment Proof"}
                docTypeId={paymentProofDocTypeId}
                Id={"DocsUploadPaymentProofId"}
                setNewUploadedFiles={setNewUploadedFiles}
                NewUploadedFiles={NewUploadedFiles}
                setUploadedFiles={setUploadedFiles}
                UploadedFiles={UploadedFiles}
                setIsFileUploading={setIsFileUploading}
                {...props}
            />
            {/* Save all files uploaded, in db at once */}
            <div className="text-center">
                <Button
                    fullWidth={true}
                    className="saveButton"
                    component="span"
                    onClick={() => SaveDocuments(false)}
                    disabled={IsFileUploading}
                >
                    {'Save'}
                </Button>
            </div>
        </ModalPopup>
    )
}