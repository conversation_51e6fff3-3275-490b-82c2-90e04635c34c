import React, { useEffect, useState } from "react";
import { useSelector } from "react-redux";
import { SV_CONFIG } from "../../../appconfig";
import { CALL_API, GetIframeURL } from "../../../services";
import rootScopeService from "../../../services/rootScopeService";
import User from "../../../services/user.service";
import NewSVCalendar from "./NewSVCalendar/NewSVCalendar";

export default function SetCallback(props) {
    let [Url, setUrl] = useState("");
    let [CalendarData, setCalendarData] = useState({});
    let [IsShowNewCalender, setIsShowNewCalender] = useState(false);
    const [IsShowCalender, setIsShowCalender] = useState(false);
    let [ParentLeadId, isRenewal, connectedLead, next5leads, allLeads] = useSelector(state => {
        let { parentLeadId, IsRenewal, connectedLead, next5leads, allLeads } = state.salesview;
        return [parentLeadId, IsR<PERSON>wal, connectedLead, next5leads, allLeads];
    });
    const CallType = useSelector(state => state.salesview.CallType);

    let [AgentStats] = useSelector(state => {
        let { AgentStats } = state.salesview;
        return [AgentStats];
    });
    let BookedLead = AgentStats[0] && Array.isArray(AgentStats[0].BookedLeads) ? AgentStats[0].BookedLeads : [];

    const IsShowNewSVCalender = () => {
        if ((window && window.SV_CONFIG_UNCACHED && window.SV_CONFIG_UNCACHED.IsShowNewCalender == true) || (SV_CONFIG.IsShowNewCalender == true)) {
            setIsShowNewCalender(true);
            return true;
        }
        setIsShowNewCalender(false);
        return false;
    }

    const GetMyCalendar = function () {
        let callType = CallType || localStorage.getItem("calltype");
        let isIbCallType = false;
        if (
            callType && (callType.indexOf("IB")
                || callType.indexOf("C2C")
                || callType.indexOf("POD")
                || callType.indexOf("PDOB")) && SV_CONFIG.ApplyIBCheck
        ) {
            isIbCallType = true;
        }
        if (IsShowNewSVCalender()) {
            var IsAgent = (User.RoleId === 13 && !isIbCallType);
            var calendarInput = {
                CustomerId: (rootScopeService.getCustomerId() || 0),
                IsQueueAgent: false
            };
            window.sessionStorage.setItem("calendarInput", JSON.stringify(calendarInput));
            let CalendarRequest = {
                AgentID: IsAgent ? User.UserId : 0,
                IsAgentUser: IsAgent,
                ParentID: ParentLeadId,
                UserID: User.UserId,
                ProductID: rootScopeService.getProductId(),
                IsRenewal: isRenewal,
                ServiceLead: getServiceLead(),
                CustomerId: rootScopeService.getCustomerId() || 0,
                IsQueueAgent: false,
            };
            setCalendarData(CalendarRequest);
        }
        else {
            // let calendarUrl = "https://mobilematrix.policybazaar.com/PGV/calendar.htm?x=0&y=false&z=345779404&v=false&w=30155&prd=7&renewal=0&ServiceLead=0";
            let calendarUrl = GetIframeURL('calendar', ParentLeadId, { isRenewal, ServiceLead: getServiceLead() });
            setUrl(calendarUrl);
        }

        let isChatGroup = true;

        let Ispriority = rootScopeService.getPriority();
        let UserGroup = User.UserGroupList;
        if (UserGroup && UserGroup.length > 0) {
            let UserGroupId = UserGroup[0].GroupId;

            if (SV_CONFIG.CallBackAllowed[SV_CONFIG.environment].indexOf(UserGroupId) > -1) {
                isChatGroup = false;
            }
        }
        if (Ispriority && isChatGroup) {
            const input = {
                url: `onelead/api/LeadPrioritization/IsCallBackAllowed/${ParentLeadId}`,
                method: 'GET', service: 'MatrixCoreAPI',
            }
            CALL_API(input).then((resultdata) => {
                //header.IsCallBackAllowed(rootScopeService.getParentLeadId()).then(function (resultdata) {
                if (resultdata && !resultdata.status) {
                    var ConfirmMessage = resultdata.message + " Press Ok to View Your Calendar?";
                    var response = window.confirm(ConfirmMessage);
                    if (response === true) {
                        setIsShowCalender(true);
                    } else {
                        props.handleClose();
                        return;
                    }
                } else {
                    setIsShowCalender(true);
                }
            }, function () {
                setIsShowCalender(true);
            });
        } else {
            setIsShowCalender(true);
        }
    }

    const IsSosUser = function () {
        var ret = false;

        User.PrdGroupList.forEach((item) => {
            if ([7, 1000].indexOf(parseInt(item.ProductId)) != -1) {
                if ([1442, 1102].indexOf(parseInt(item.GroupId)) != -1) {
                    ret = true;
                }
            }
        });
        return ret;
    }

    const getServiceLead = function () {
        //return 0;
        var servicelead = 0; //42628009
        try {
            if (IsSosUser()) {
                var bookedLeads = BookedLead;
                var parentId = ParentLeadId;
                var leadId = allLeads;
                var Progressiveleads = next5leads;

                if (bookedLeads != undefined && bookedLeads != null && bookedLeads.length > 0) {
                    let index;
                    //var string = rootScopeService.getLeadIds();
                    //var array = string.split(';');
                    var Leads = rootScopeService.getLeads();
                    Leads.forEach(function (vdata, key) {
                        if (vdata.StatusId >= 13) {
                            index = functiontofindIndexByKeyValue(bookedLeads, "LeadId", vdata.LeadID);
                            //break;
                        }
                    });

                    if (index >= 0) {
                        if (bookedLeads[index].ReasonId === 50) {
                            servicelead = bookedLeads[index].LeadId;
                        }
                    }
                }

                if (servicelead === 0 && connectedLead !== undefined && connectedLead != null) {
                    if (connectedLead.callType === "Inbound") {
                        if (connectedLead.leadId === leadId) {
                            servicelead = connectedLead.leadId;
                        }
                        else if (connectedLead.leadId === parentId) {
                            servicelead = connectedLead.leadId;
                        }
                    }
                }

                if (servicelead == 0 && Progressiveleads != undefined && Progressiveleads != null && Progressiveleads.length > 0) {
                    //var string = rootScopeService.getLeadIds();
                    //var array = string.split(';');
                    let index;
                    var Leads = rootScopeService.getLeads();
                    Leads.forEach(Leads, function (vdata, key) {
                        if (vdata.StatusId >= 13) {
                            index = functiontofindIndexByKeyValue(Progressiveleads, "LeadId", vdata.LeadID);
                        }
                    });

                    if (index >= 0) {
                        if (Progressiveleads[index].ReasonId == 30) {
                            servicelead = bookedLeads[index].LeadId;
                        }
                    }
                }
            }
        } catch (error) {

        }

        return servicelead;
    }


    const functiontofindIndexByKeyValue = (arraytosearch, key, valuetosearch) => {
        for (var i = 0; i < arraytosearch.length; i++) {
            if (arraytosearch[i][key] == valuetosearch) {
                return i;
            }
        }
        return null;
    };

    useEffect(() => {
        GetMyCalendar();
    }, []);
    return (
        <>
            {IsShowCalender &&
                <>
                    {
                        IsShowNewCalender ?
                            <NewSVCalendar data={CalendarData}></NewSVCalendar>
                            :
                            <iframe title="calendar" src={Url} style={{ width: '100%', height: '100%' }}></iframe>
                    }
                </>

            }
        </>
    )
}
