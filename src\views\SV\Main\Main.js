import React from "react";
import LeadList from './LeadList';
import CustomerHistory from './CustomerHistory';
import Comments from './Comments';
// import SendSoftCopy from './SendSoftcopy'
import { Grid } from "@mui/material";
import Recommendations from "./Recommendations";
import PreferredInsurer from "./PreferredInsurer";
import CreateBooking from "./CreateBooking";
import AgentAssistant from "./AgentAssist";
import CrossSell from "./CrossSell";
import User from "../../../services/user.service";
import RightBlock from "../RightBlock";
import { useTheme } from '@mui/styles';
import { useMediaQuery } from '@mui/material';
import ErrorBoundary from "../../../hoc/ErrorBoundary/ErrorBoundary";
import { useSelector } from "react-redux";
// import CallDetails from "../RightBlock/CallDetails";
import TopBar from "./TopBar";
import rootScopeService from "../../../services/rootScopeService";
import { IsRenewalAgent } from "../../../layouts/SV/components/Sidebar/helper/sidebarHelper";
import { SV_CONFIG } from "../../../appconfig";
import { IsValidUserGroup } from "../../../services/Common";
// import { setNoOfLeadCardsToShow } from "../../../store/actions/SalesView/SalesView";

export default function Main(props) {
    const theme = useTheme();
    // const dispatch = useDispatch();
    const isLargeDesktop = useMediaQuery(theme.breakpoints.up('lg'), {
        defaultMatches: true
    });
    const isDesktop = useMediaQuery(theme.breakpoints.up('md'), {
        defaultMatches: true
    });
    let [ParentLeadId, noOfLeadCardsToShow,IsRenewal,IsPreferredInsurer] = useSelector(state => {
        let { parentLeadId, noOfLeadCardsToShow,IsRenewal,IsPreferredInsurer } = state.salesview;
        return [parentLeadId, noOfLeadCardsToShow,IsRenewal,IsPreferredInsurer]
    });
    const ShowAssignCriticalComponents = useSelector(state => state.salesview.ShowAssignCriticalComponents);
    var SMEFosGroups = IsValidUserGroup(SV_CONFIG["SMEFOSGroups"], [13]);

    const getLeadListCol = (breakpoint) => {
        if (noOfLeadCardsToShow === 0) return false;

        // return isDesktop
        //     ? Math.min(noOfLeadCardsToShow * 4, 12)
        //     : Math.min(noOfLeadCardsToShow * 3, 9);

        switch (breakpoint) {
            case 'lg':
                return Math.min(noOfLeadCardsToShow * 4, 12);
            case 'md':
                return Math.min(noOfLeadCardsToShow * 6, 12);
            case 'sm':
                return Math.min(noOfLeadCardsToShow * 6, 12);
            case 'xs':
                return 12;
            default:
                return 12;
        }
    }
    return (
        <>
            <Grid container spacing={2}>
                <TopBar />
            </Grid>
            <Grid container>
                <Grid item sm={getLeadListCol('sm')} md={getLeadListCol('md')} lg={getLeadListCol('lg')} xs={getLeadListCol('xs')}>
                    <ErrorBoundary name="Leads">
                        <div className="leadview">
                            <LeadList />
                        </div>
                    </ErrorBoundary>
                </Grid>

                {(noOfLeadCardsToShow === 1 && isLargeDesktop) &&
                    <Grid item sm={4} md={4} xs={12}>
                        <ErrorBoundary name="Comments">
                            <Comments className="mx-8px openDIvHeight" maxComments={4} />
                        </ErrorBoundary>
                    </Grid>
                }
                {((noOfLeadCardsToShow < 3 && isLargeDesktop) || (noOfLeadCardsToShow < 2)) &&
                    <>
                        {/* { paddingLeft: '5px', paddingRight: '10px' } */}
                        <Grid item sm={6} md={6} lg={4} xs={12}>
                            <ErrorBoundary name="CustomerHistory">
                                <CustomerHistory className="mx-8px" classWhenopen="openDIvHeight" />
                            </ErrorBoundary>
                        </Grid>
                    </>
                }
                {isDesktop === false
                    ? <Grid item sm={12} lg={4} md={(noOfLeadCardsToShow > 2) ? 12 : 4} xs={12}>
                        <ErrorBoundary name="RightBlock">
                            <RightBlock Isshow={!!ParentLeadId} />
                        </ErrorBoundary>
                    </Grid>
                    : null
                }
            </Grid>

            {/* <div className="othersection mt-1">
                <Grid container spacing={3}>
                    
                </Grid>
            </div> */}
            {/* {rootScopeService.getProductId() === 2 &&
                <Grid container spacing={3}>
                    <ErrorBoundary name="AgentAssistant">
                        <AgentAssistant />
                    </ErrorBoundary>
                </Grid>
            } */}
            {rootScopeService.getProductId() === 2 && IsRenewal == 0 && IsPreferredInsurer &&
                <div className="Preferred-section">
                    <Grid container spacing={3}>
                        <ErrorBoundary name="PreferredInsurer">
                            <PreferredInsurer User={User} />
                        </ErrorBoundary>
                    </Grid>
                </div>
            }
            <div className="recomendations-section">
                <Grid container spacing={3}>
                    <ErrorBoundary name="Recommendations">
                        <Recommendations User={User} />
                    </ErrorBoundary>
                </Grid>
            </div>

            {ShowAssignCriticalComponents &&
                <Grid container spacing={3}>
                    <ErrorBoundary name="CreateBooking">
                        <CreateBooking />
                    </ErrorBoundary>
                </Grid>
            }
            {ShowAssignCriticalComponents && !SMEFosGroups &&
                <Grid container spacing={3}>
                    <ErrorBoundary name="CrossSell">
                        <CrossSell />
                    </ErrorBoundary>
                </Grid>
            }
        </>
    )
}
