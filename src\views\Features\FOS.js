import React, { useCallback, useEffect, useState } from "react";
import {
    TextField,
    Grid,
    CircularProgress,
    InputAdornment,
    LinearProgress,
    FormLabel,
    FormControlLabel,
    Radio,
    Typography,
    Button,
    Box,
} from "@mui/material";
import makeStyles from '@mui/styles/makeStyles';
import { useSnackbar } from 'notistack';
import { Autocomplete } from '@mui/material';
import User from "../../services/user.service";
import masterService from "../../services/masterService";
import { CALL_API } from "../../services";
import { SelectDropdown, TextInput } from "../../components";
import { CONFIG, SV_CONFIG } from "../../appconfig";
import './FOS.scss'
import { default as conditionWiseColumns, defaultFOSColumns } from "../Features/assets/conditionWiseColumns";
import { GetAgentTypeByBookingService, GetOfflineCitiesService, GetPincodesForCityIDService, IsSourceCustomerWhatsapp, SetAppointmentDataService, UpdateAppointmentStatusService, getAppointmentsBySlotIdService, getBasicLeadDetailsService, getIsLeadRenewalService, SaveCoreAddressUsageService, getPrefCommService, requestWhatsAppOptIn } from "./FosHelpers/fosServices";
import { getDistinctCitiesAppointmentMapping, getTimeHour, isPageSource, isAppCreatedSource, styleFn, IsStringEqual, bypassGenderInput, } from "./FosHelpers/fosCommonHelper";
import { AddressNew, Education, Income, IncomeDocs, pageOpeningSources, AppSubstatusMaster, AssignmentIdTypeMapping, AppointmentCreatedSources, gendersMaster, InvestmentTypeMaster } from "./FosHelpers/fosMasters";
import { API_STATUS, GetCustAddress } from "../../services/Common";
import AppointmentDateTimeSection from "./FosHelpers/Components/AppointmentDateTimeSection";
import { FormSectionHeading } from "./FosHelpers/Components/Components";
import { debounce } from "lodash";
import { useQuery } from "../../hooks/useQuery";
import { AppointmentBookSuccessPopup } from "./FosHelpers/Components/AppointmentBookSuccessPopup";
import { getLandmarkdetailsService, GetAppointmentDataService, IsAppMarkCancelService, TriggerSaveCustomerLocationData, GetCustomerLocationData, CheckCustomerLocationAvailable, IsAppointmentCreatedService, CheckAgentAvailabilityInCity, GetTotalAppointmentsByCityIdService } from "./FosHelpers/fosServices";
import FOSCards from "./Cards/FOSCards";
import CurrentAppointmentAction from "./FosHelpers/Components/CurrentAppointmentAction";
import { FosCancelReasonPopup } from "./FosHelpers/Components/FosCancelReasonPopup";
import { CommonModalPopUp } from "../SV/Main/Modals/CommonModalPopUp";
import { getCountryMaster, GetAssignmentTypebyId, GetAssignmentTypebyIdChips, IsSpokeCities, IsSpokeCitiesDays, prioritizeStateId } from "./FosHelpers/fosCommonHelper";
import { gaEventTracker } from "../../helpers";
import { ReadAppointmentDraft, WriteAppointmentDraftbyKey, ReadAppointmentDraftbyKey, DeleteAppointmentDraft, ShowAgentAvailWarning, SpokeCitiesAppointmentCount } from "./FosHelpers/fosCommonHelper";
import { PreviewCustomerPopUp } from "../SV/Main/Modals/PreviewCustomerPopUp";
import { SavedCustomerAddress } from "../SV/Main/Modals/SavedCustomerAddress";
import { HowItWorksPopup } from "../SV/Main/Modals/HowItWorksPopup";
import { AgentAvailabilityWarningPopUp } from "../SV/Main/Modals/AgentAvailabilityWarningPopUp"
import HelpOutlineIcon from '@mui/icons-material/HelpOutline';
import InfoOutlinedIcon from '@mui/icons-material/InfoOutlined';
import Tooltip, { tooltipClasses } from '@mui/material/Tooltip';
import { styled } from '@mui/material/styles';
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import dayjs from "dayjs";


const LightTooltip = styled(({ className, ...props }) => (
    <Tooltip {...props} classes={{ popper: className }} />
))(({ theme }) => ({
    [`& .${tooltipClasses.tooltip}`]: {
        backgroundColor: theme.palette.common.white,
        color: 'rgba(0, 0, 0, 0.87)',
        boxShadow: theme.shadows[1],
        fontSize: 13,
    },
}));

export default function FOS(props) {
    const [OpenHistory, setOpenHistory] = useState(false);
    const [OpenPreviewCustomer, setOpenPreviewCustomer] = useState(false);
    const [IsRescheduling, setIsRescheduling] = useState(false);
    const [IsEditAppDetails, setIsEditAppDetails] = useState(false);
    const [ActiveReschedulebtn, setActiveReschedulebtn] = useState(false);
    const [ShowNoCityServiceable, setShowNoCityServiceable] = useState(false);
    const [IsLeadRenewal, setIsLeadRenewal] = useState(null);
    const [IsSuccessfullySaved, setIsSuccessfullySaved] = useState(false);
    const [AppCreatedbyUserId, setAppCreatedbyUserId] = useState(0);
    const [IsAppointmentCreated, setIsAppointmentCreated] = useState(false);
    const [waOptInBtnDisabled, setWaOptInBtnDisabled] = useState(false);


    let IsShowFields = IsRescheduling ? true : false;

    const [AppointmentDetailsfromDB, setAppointmentDetailsfromDB] = React.useState([]);


    let IsSrcCustomerWhatsapp = IsSourceCustomerWhatsapp();
    let query = useQuery();
    let view = query.get("view");
    let pageOpeningSource = query.get("src"); // "", "referralPage", "MatrixGoApp"
    let AppCreatedSource = query.get("AppCreatedSource");

    // let IsLeadRenewal = (query.get("LeadSource") && query.get("LeadSource").toLowerCase() == "renewal") ? true : false;
    let disabled;
    view === "true" ? (disabled = true) : (disabled = false);
    console.log("AppointmentCreatedSource", AppCreatedSource);
    if (!pageOpeningSource) {
        pageOpeningSource = pageOpeningSources.salesview
    }

    const isSrcReferralOrFOSApp = isPageSource(pageOpeningSource, [
        pageOpeningSources.referralPage,
        pageOpeningSources.MatrixGoApp
    ]);
    const isSrcMatrixGoApp = isPageSource(pageOpeningSource, [
        pageOpeningSources.MatrixGoApp
    ]);

    const isSrcCrossSell = isPageSource(pageOpeningSource, [
        pageOpeningSources.crosssell
    ]);
    const conditions = {
        showAfterAppointmentPopup: isSrcReferralOrFOSApp,
        bindHomeCitiesOnly: !!(SV_CONFIG && SV_CONFIG.BindHomeCityConfig && isSrcReferralOrFOSApp), //isSrcReferralOrFOSApp,//--- ABCD--
        hideAnotherReferralOption: isSrcMatrixGoApp
    }

    const isAppCreatedSourceWAAppByCust = isAppCreatedSource(AppCreatedSource, [AppointmentCreatedSources.waappbycust]);

    const [selectedSubStatusID, setselectedSubStatusID] = useState(0);
    const [OpenFosCancelReasonPopup, setOpenFosCancelReasonPopup] = useState(false);
    const { enqueueSnackbar } = useSnackbar();

    let [OfflineCitiesMaster, setOfflineCitiesMaster] = useState([]);
    // let [FosCities, setFosCities] = useState(['']);
    const [parentLeadId, setparentLeadId] = useState(0);
    const [CustomerId, setCustomerId] = useState(0);
    const [ProcessId, setProcessId] = useState(0);
    // const [subStatusId, setSubStatusId] = useState(0);
    const [IsTermProduct, setIsTermProduct] = useState(false);
    const [PincodeMaster, setPincodeMaster] = useState([]);
    const [pincodeAPIstatus, setPincodeAPIstatus] = useState(API_STATUS.SUCCESS);

    // Appointment DateTime from db (if saved)
    const [appointmentDateTime, setAppointmentDateTime] = useState();

    const [DistinctCitiesMaster, setDistinctCitiesMaster] = useState([]);
    const [DistinctCitiesAppointmentMappingMaster, setDistinctCitiesAppointmentMappingMaster] = useState([]);
    const [zoneList, setZoneList] = useState([]);
    const [appointmentTypeList, setAppointmentTypeList] = useState([]);
    const [NewAddress, setNewAddress] = useState(AddressNew);
    const [Suppliers, setSuppliers] = useState([]);
    const [Plans, setPlans] = useState([]);
    // const [PlanListData, setPlanListData] = useState([]);
    const [recommendationCardList, setRecommendationCardList] = useState([]);
    // const [productId] = useState(rootScopeService.getProductId());
    const [productId, setProductId] = useState(0);
    const [fosAppointmentTypeMaster, setFosAppointmentTypeMaster] = useState([]);

    const [AssignmentTypeMaster, setAssignmentTypeMaster] = useState([]);

    const [FOSMaster, setFOSMaster] = useState([]);
    // let [IsShowOfflineCity, setIsShowOfflineCity] = useState(false);
    let [IsShowSaveBtn, setIsShowSaveBtn] = useState(true);
    // let [IsShowFosCity, setIsShowFosCity] = useState(false);
    const [Visible, setVisible] = useState({});
    const [userId, setUserId] = useState(0);
    const [openPopup, setOpenPopup] = useState(null);
    const [StatusId, setStatusId] = useState(0);
    const [CurrentSelectedCountry, setCurrentSelectedCountry] = useState("");
    const [CountryMaster, setCountryMaster] = useState([]);
    const [CurrentAssignmentId, setCurrentAssignmentId] = useState(-1);
    const [AssignmentTypesForCity, setAssignmentTypesForCity] = useState([]);
    const [IsCustomerLocationAvailable, setIsCustomerLocationAvailable] = useState(false);
    const [AskLocationBtnDisabled, setAskLocationBtnDisabled] = useState(false);
    const [ShowAdditionalFields, setShowAdditionalFields] = useState(false);
    const [showAgentAvailabilityWarningPopUp, setshowAgentAvailabilityWarningPopUp] = useState(false);
    const [HandleAgentAvailability, setHandleAgentAvailability] = useState("")
    const [SubmitReqData, setSubmitReqData] = useState(null);
    const [productWiseCondition, setProductWiseCondition] = useState();
    let isZoneInputVisible = false
    try {
        isZoneInputVisible = !(!(zoneList && Array.isArray(zoneList))
            || (zoneList.length === 0)
            || (zoneList.length === 1 && zoneList[0].ZoneId === 0)
            || zoneList.every((zone) => zone.ZoneId === 0));// No zones available
    } catch { }
    const useStyles = makeStyles(styleFn);
    const classes = useStyles();
    const [InputLandmarkValue, setInputLandmarkValue] = React.useState('');
    const [options, setOptions] = React.useState([]);
    const [bookedAppointmentId, setBookedAppointmentId] = useState(null);
    const [userAssignmentId, setUserAssignmentId] = useState(0);
    const [CustomerSource, setCustomerSource] = useState("");
    const [GetAppointmentDataAPIStatus, setGetAppointmentDataAPIStatus] = useState(API_STATUS.LOADING);
    const [ShowErrorBorderonLandmark, setShowErrorBorderonLandmark] = useState(false);
    const [AppointmentSlotListBySlotId, setAppointmentSlotListBySlotId] = useState([]);
    const [isGenderDisabled, setIsGenderDisabled] = useState(false);
    const [IsCustConfirmed, setIsCustConfirmed] = useState(false);
    const [PreviewCustomerResponse, setPreviewCustomerResponse] = useState(null);
    const [OpenSavedCustomerAddress, setOpenSavedCustomerAddress] = useState(false);
    const [AddressData, setAddressData] = useState([]);
    const [TotalAppointmentsByCityId, setTotalAppointmentsByCityId] = useState(null);
    const [RestrictLandmarkIcon, setRestrictLandmarkIcon] = useState(false);
    const ProductIdforAPI = IsLeadRenewal ? 147 : productId;
    const [HowItWorksOpen, setHowItWorksOpen] = useState(false);
    const [IsLandmarkAttempted, setIsLandmarkAttempted] = useState(false);
    const [MotorExpDeldataEligible, setMotorExpDeldataEligible] = useState(null);
    const [isCustomerNotOptedInWhatsapp, setIsCustomerNotOptedInWhatsapp] = useState(false);
    const [WAOptblink,setWAOptblink]=useState(false);

    let CountryAsIndia = 392;
    let UAEUser = false;
    let _IsAppointmentCreateRestriction = false;
    let RestrictLandmarkforCustGroups = false;
    let usergrp = User.UserGroupList || [];
    usergrp.forEach(function (item, key) {
        if (SV_CONFIG.UAEAppointmentGroups && (SV_CONFIG.UAEAppointmentGroups.indexOf(item.GroupId) > -1)) {
            UAEUser = true;
        }
        if (SV_CONFIG.AppCreateRestrictionGrps && (SV_CONFIG.AppCreateRestrictionGrps.indexOf(item.GroupId) > -1)) {
            _IsAppointmentCreateRestriction = true;
        }
        if (SV_CONFIG.RestrictLandmarkforCustGroups && (SV_CONFIG.RestrictLandmarkforCustGroups.indexOf(item.GroupId) > -1)) {
            RestrictLandmarkforCustGroups = true;
        }
    });

    let IsManualTriggerAllowed = SV_CONFIG && Array.isArray(SV_CONFIG.IsManualTriggerAllowedProduct) && SV_CONFIG.IsManualTriggerAllowedProduct.indexOf(productId) > -1 ? true : false//checkUserGroup(SV_CONFIG && SV_CONFIG.ManualDropLocationTrigger)
    let IsNewAppointmentAddress = SV_CONFIG && Array.isArray(SV_CONFIG.IsNewAppointmentAddressProduct) && SV_CONFIG.IsNewAppointmentAddressProduct.indexOf(productId) > -1 ? true : false//checkUserGroup(SV_CONFIG && SV_CONFIG.FOSAddressGroupList)
    let RestrictLandmarkforCustProductIds = SV_CONFIG && Array.isArray(SV_CONFIG.RestrictLandmarkforCustProductIds) && SV_CONFIG.RestrictLandmarkforCustProductIds
    let IsAppointmentCreateRestriction = User && User.RoleId == 13 && _IsAppointmentCreateRestriction;
    let IsMotorRestrictionCityIds = SV_CONFIG && Array.isArray(SV_CONFIG.MotorCityId) && SV_CONFIG.MotorCityId;
    let IsWhatsappLocationManualProductId = SV_CONFIG && Array.isArray(SV_CONFIG.WhatsappLocationManualProductId) && SV_CONFIG.WhatsappLocationManualProductId.indexOf(productId) > -1 ? true : false
    // let IsNewAppointmentAddress = User && User.RoleId == 13 && checkUserGroup(SV_CONRestrictLandmarkforCustProductIdsFIG && SV_CONFIG.FOSAddressGroupList);
    //let IsNewAppointmentAddress = SV_CONFIG  && (SV_CONFIG.IsNewAppointmentAddressProduct==productId)? true:false;
    let FreeTextAddressValidationMessage = IsNewAppointmentAddress ? 'House No. /Building No. /Plot No. /Block No.' : 'Address';
    let AutoSuggestValidationMessage = IsNewAppointmentAddress ? 'Society/Colony/Locality or Near By Temple/Mall/School/Hospital/Famous place' : 'Nearby popular area/Place/Street';
    let Block8AMCityList = SV_CONFIG && Array.isArray(SV_CONFIG.Block8AMCityList) && SV_CONFIG.Block8AMCityList;
    let BlockSlotIds = SV_CONFIG && Array.isArray(SV_CONFIG.BlockSlotIds) && SV_CONFIG.BlockSlotIds;

    const getDetailsFromURL = () => {
        let routeParams = window.location.pathname.split('/');
        if (!routeParams) {
            return null;
        }
        let _userId, _parentLeadId, _ProductId, _CustomerId, _CustomerSource;
        gaEventTracker('FOS form', 'FOS form opened');


        let reqIndex = CONFIG.PUBLIC_URL ? 4 : 2;
        _parentLeadId = parseInt(atob(routeParams[reqIndex]).split('/')[2]);
        _ProductId = parseInt(atob(routeParams[reqIndex]).split('/')[1]);
        _CustomerId = parseInt(atob(routeParams[reqIndex]).split('/')[0]);
        _userId = parseInt(atob(routeParams[reqIndex]).split('/')[3]);

        return {
            _parentLeadId,
            _ProductId,
            _userId,
            _CustomerId,
            _CustomerSource
        }


    }


    const initStates = (leadData) => {
        const { _parentLeadId, _ProductId, _userId, _CustomerId } = leadData;

        setCustomerId(_CustomerId);
        setProductId(_ProductId)
        setparentLeadId(_parentLeadId);
        setUserId(_userId);
    }
    useEffect(() => {
        if (NewAddress.AppointmentType == 5) {
            enqueueSnackbar("Cancel the store appointment to schedule a Home appointment.", {
                variant: 'error',
                autoHideDuration: 5000,
            });

        }
    }, [NewAddress.AppointmentType])

    useEffect(() => {
        if (NewAddress.OfflineCityId != 0 && !IsReschedulingAppoinment())
            SaveAppointmentDrafted();
    }, [NewAddress])

    useEffect(() => {
        if ((productId == 7) || (productId == 115 && NewAddress.AssignmentId != 3)) {
            setShowAdditionalFields(true)
        }
        else {
            setShowAdditionalFields(false)
        }
    }, [NewAddress.AssignmentId, productId])
    useEffect(() => {
        const leadData = getDetailsFromURL();
        if (!leadData) return;
        const { _parentLeadId, _ProductId, _userId, _CustomerSource } = leadData;

        if (_CustomerSource) {
            setCustomerSource(_CustomerSource);
        }

        if (_ProductId == 2) {
            getIsLeadRenewalService(_parentLeadId).then((res) => {
                if (res) {
                    setIsLeadRenewal(res.IsRenewal);
                }
                initStates(leadData);

                if (res.Message !== "Success") {
                    setGetAppointmentDataAPIStatus(API_STATUS.FAIL);
                    res.parentLeadId = parentLeadId;
                    const gaDump = JSON.stringify(res);
                    gaEventTracker('FOS getIsLeadRenewalService notsuccess', gaDump);
                }
            }).catch((err) => {
                setGetAppointmentDataAPIStatus(API_STATUS.FAIL);
                let error;
                try {
                    error = JSON.stringify(err);
                } catch (e) { }
                gaEventTracker('FOS getIsLeadRenewalService error', error);
            })
        }
        else {
            initStates(leadData);
        }

        //Need to check
        if (conditions.bindHomeCitiesOnly) {
            GetAgentTypeByBooking(_userId);
        }

    }, [NewAddress.AssignmentId]);

    useEffect(() => {

        if (parentLeadId !== 0) {
            if (parentLeadId != localStorage.getItem('LandMarkLeadId')) {
                localStorage.setItem('LandMarkTimeStamp', 0);
                localStorage.setItem('LandMarkLeadId', parentLeadId);
            }

            if (Array.isArray(fosAppointmentTypeMaster) && fosAppointmentTypeMaster.length === 0) {
                GetAppointmentTypeMaster(ProductIdforAPI);
            }

        }
    }, [parentLeadId]);

    useEffect(() => {
        setIsTermProduct(false);
        if (productId !== 0 && !IsSrcCustomerWhatsapp) {
            getSuppliers();
            // GetProductPlans()
        }
        if (productId === 2) {
            setProductWiseCondition(conditionWiseColumns.Health)

        }
        else if (productId === 115) {
            setProductWiseCondition(conditionWiseColumns.Investment)
        }
        else if (productId === 7 || productId === 1000) {
            setProductWiseCondition(conditionWiseColumns.Term)
            setIsTermProduct(true);
        }
        else if (productId === 117) {
            setProductWiseCondition(conditionWiseColumns.Motor)
        }
        else if (productId === 131) {
            setProductWiseCondition(conditionWiseColumns.SME)
        }

    }, [productId]);

    useEffect(() => {

        if (OfflineCitiesMaster.length === 0 && parentLeadId > 0) {
            GetOfflineCities(0, ProductIdforAPI);
        }
        else if (IsSrcCustomerWhatsapp && OfflineCitiesMaster.length === 0) {
            GetOfflineCities(0, ProductIdforAPI);
        }
        let _visible;
        _visible = defaultFOSColumns.HomeVisit;
        setVisible(_visible);
        if (parentLeadId > 0) {
            CheckCustomerLocationAvailable(parentLeadId).then((response) => {
                response == true ? setIsCustomerLocationAvailable(true) : setIsCustomerLocationAvailable(false);

            }
            ).catch((error) => {
                console.log("Error occured in CheckCustomerLocationAvailable", error)
            })

            IsAppointmentCreatedService(parentLeadId).then((response) => {
                if (response != null && response.Data != null) {
                    response.Data.AppointmentCount > 0 ? setIsAppointmentCreated(true) : setIsAppointmentCreated(false);
                }
            }
            ).catch((error) => {
                console.log("Error occured in IsAppointmentCreated", error)
            })
            // if (productId == 117) {
            //     GetCarDetailsService(parentLeadId).then((res) => {
            //         if (res != null && res.Data != null) {
            //             if (res.Data.PolicyType && res.Data.PolicyType.toLocaleLowerCase() == 'new' && CheckExpectedDeliveryDate(res.Data.ExpectedDeliveryDate)) {
            //                 setMotorExpDeldataEligible(true);
            //             }
            //         }

            //     }).catch((error) => {
            //         console.log("Error occured in GetCarDetailsService", error)
            //     })
            // }


        }

    }, [productId, parentLeadId]);

    useEffect(() => {
        if (DistinctCitiesMaster.length > 0) {
            setGetAppointmentDataAPIStatus(API_STATUS.LOADING);
            GetAppointmentData();
        }
    }, [DistinctCitiesMaster]);

    useEffect(() => {
        if (Array.isArray(SV_CONFIG.AppointmentstateCityPriorityProductIds) && SV_CONFIG.AppointmentstateCityPriorityProductIds.length > 0 && SV_CONFIG.AppointmentstateCityPriorityProductIds.indexOf(productId) > -1 && NewAddress.LeadStateId > 0 && NewAddress.LeadCityID > 0 && DistinctCitiesMaster.length > 0) {
            const _DistinctCitiesMaster = JSON.parse(JSON.stringify(DistinctCitiesMaster));
            const sortedData = prioritizeStateId(_DistinctCitiesMaster, NewAddress.LeadStateId, NewAddress.LeadCityID);
            console.log("sortedList is", sortedData);
            setDistinctCitiesMaster(sortedData);
        }
    }, [NewAddress.LeadStateId, NewAddress.LeadCityID])


    useEffect(() => {

        if (NewAddress.OfflineCity && NewAddress.OfflineCity.CityId) {
            // updateAppointmentList(NewAddress.OfflineCity)

            if (!IsReschedulingAppoinment()) {
                updatePincodeMaster(NewAddress.OfflineCity.CityId);
                GetTotalAppointmentsByCityId(NewAddress.OfflineCity.CityId);
            }

        }
    }, [NewAddress.OfflineCity])
    useEffect(() => {

        if (Array.isArray(fosAppointmentTypeMaster) && fosAppointmentTypeMaster.length > 0
            && NewAddress.OfflineCity && NewAddress.OfflineCity.CityId
        ) {
            updateAppointmentList(NewAddress.OfflineCity);
            // if(!IsEditAppDetails)
            // {
            //     setCurrentAssignmentId(-1);
            // }
            if (!IsReschedulingAppoinment()) {
                ChangeAssignmentMaster();
            }


        }
    }, [NewAddress.OfflineCity, fosAppointmentTypeMaster])

    useEffect(() => {
        if (NewAddress.AppointmentType !== ""
            && Array.isArray(fosAppointmentTypeMaster)
            && fosAppointmentTypeMaster.length > 0
        ) {
            //  ChangeAssignmentMaster();
            // setIsShowOfflineCity(true);
        }

    }, [NewAddress.AppointmentType, fosAppointmentTypeMaster]);

    useEffect(() => {
        if (CustomerId != 0) {
            GetCustAddress(CustomerId).then((result) => {
                result = result ? result.data : null;
                if (result != null) {
                    var arr = [];
                    let obj = {},
                        element = {};
                    for (var i = 0; i < result.length; i++) {
                        obj = {};
                        element = result[i];
                        obj = {
                            House: element.address,
                            AddressType: element.addressType,
                            AddressTypeId: element.addressTypeId,
                            City: element.cityName,
                            CityId: element.city,
                            CreatedOn: element.createdOn,
                            CustAddrId: element.custAddrId,
                            IsActive: element.isActive,
                            Locality: element.addressLine2,
                            Landmark: element.addressLine3,
                            PinCode: element.pincode,
                            State: element.stateName,
                        };
                        arr.push(obj);
                    }

                    setAddressData(arr);

                    //setAddressData(result.data.data);
                } else {
                    setAddressData([]);
                }
            });
        }
    }, [CustomerId])

    // useEffect(() => {

    //     updateZoneList()
    //     // if (NewAddress.AssignmentId && Object.keys(productWiseCondition) && Object.keys(productWiseCondition).length > 0 && productWiseCondition != null && NewAddress.AssignmentId !== "") {
    //     // }// commenting this code because currently we need to set visible values irrespective of assignment Type
    // }, [NewAddress.AssignmentId]);

    const SaveAppointmentDrafted = () => {
        let key = `${userId}_${parentLeadId}`;
        console.log("SaveAppointmentDrafted", key);
        var AppointmentDataDraft = ReadAppointmentDraft();


        //Updating key in draft
        if (AppointmentDataDraft || !IsReschedulingAppoinment()) {
            let DataObj = {};
            DataObj['Appdata'] = { ...NewAddress };
            AppointmentDataDraft = WriteAppointmentDraftbyKey(AppointmentDataDraft, key, DataObj);
        }
    }

    const IsAppointmentDrafted = () => {
        let key = `${userId}_${parentLeadId}`;
        var AppointmentDataDraftPresent = ReadAppointmentDraftbyKey(key);
        let ans = !!AppointmentDataDraftPresent ? true : false;
        return ans;

    }

    /// Removing // Need to check
    const GetAgentTypeByBooking = (UserId) => {
        GetAgentTypeByBookingService(UserId).then((res) => {
            if (res && res.AssigmentId) {
                if (productId == 2 && res.AssigmentId == 2) {
                    res.AssigmentId = 4;
                }
                setUserAssignmentId(res.AssigmentId);
                setNewAddress(prevState => ({ ...prevState, AssignmentId: res.AssigmentId }));
            }
        });
    }
    const UpdateReschduleStatus = () => {
        let reqData =
        {
            "LeadID": 0,
            "UserID": 4020,
            "SubStatusId": 2005,
            "StatusId": StatusId || 4,
            "EncryptedCustomerId": window.localStorage.getItem("EncryptedCustomerId"),
            "EncryptLeadId": window.localStorage.getItem("EncryptedLeadId"),
            "Source": CustomerSource,
            "CancelReasonId": 0
        }
        return UpdateAppointmentStatusService(reqData);
    }

    const GetTotalAppointmentsByCityId = (CityId) => {
        GetTotalAppointmentsByCityIdService(CityId).then((res) => {
            if (res.Status == true && res.Data != null) {
                let _TotalAppointmentsByCityId = {};
                res.Data.map((value) => {
                    let obj = {};
                    obj["WeekDays"] = value.WeekDays;
                    obj["TotalAppointments"] = value.TotalAppointments;
                    _TotalAppointmentsByCityId = { ..._TotalAppointmentsByCityId, [value.AppointmentDay]: obj };

                })
                console.log("_TotalAppointmentsByCityId", _TotalAppointmentsByCityId);
                setTotalAppointmentsByCityId(_TotalAppointmentsByCityId)

            }
        }).catch((error) => {
            console.log("error in GetTotalAppointmentsByCityId", error)
        })

    }

    const handleChange = (event) => {
        const regx = /^[0-9\b]+$/;
        let val = event.target.value;
        switch (event.target.name) {
            case "OfflineCity":
                if (event.target.value) {

                    // if(MotorExpDeldataEligible && IsMotorRestrictionCityIds.indexOf(event.target.value.CityId)>-1)
                    // {
                    //     enqueueSnackbar("Appointment creation is not allowed for new cars for selected city.", { variant: 'error', autoHideDuration: 1500 });
                    //     setNewAddress({ ...NewAddress, OfflineCity: "", OfflineCityId: 0, Pincode:"" });
                    //     return;
                    // }else{
                    setNewAddress({
                        ...NewAddress, OfflineCity: event.target.value, OfflineCityId: event.target.value.CityId,
                        AssignmentId: "", ZoneId: "",
                        Pincode: "", Landmark: ""
                    });

                    setOptions([]);
                    setInputLandmarkValue('');


                }
                else {
                    setNewAddress({ ...NewAddress, OfflineCity: "", OfflineCityId: 0 });
                }

                break;
            case 'AppointmentType':
                setNewAddress({ ...NewAddress, AppointmentType: event.target.value, AssignmentId: "", ZoneId: "" })
                break;

            case "AssignmentType":
                setNewAddress({ ...NewAddress, AssignmentId: event.target.value, ZoneId: "" })
                break;
            case 'zone':
                setNewAddress({ ...NewAddress, ZoneId: event.target.value })
                break;
            case "Comments":
                setNewAddress({ ...NewAddress, Comments: event.target.value })
                break;
            case "Address":
                setNewAddress({ ...NewAddress, Address: event.target.value })
                break;
            case "Address1":

                setNewAddress({ ...NewAddress, Address1: event.target.value })
                break;

            case "Landmark":
                setNewAddress({ ...NewAddress, Landmark: event.target.value });
                break;
            case "NearBy":
                setNewAddress({ ...NewAddress, NearBy: event.target.value });
                break;
            case "Pincode":
                if (event.target.value !== '' && !regx.test(event.target.value)) {
                    enqueueSnackbar("Only Numbers allowed", {
                        variant: 'error',
                        autoHideDuration: 3000,
                    });
                }
                else {
                    setNewAddress({ ...NewAddress, Pincode: val, Landmark: "" });
                    setInputLandmarkValue('')
                    setOptions([]);
                }
                break;
            case "AppointmentDateSlot":
                setActiveReschedulebtn(true);
                setNewAddress({ ...NewAddress, AppointmentDateSlot: event.target.value, AppointmentTimeSlot: null })
                if (!IsSourceCustomerWhatsapp()) {

                    let requestData = { "SelectedDate": event.target.value && event.target.value.fullDate, "LeadId": parentLeadId && parentLeadId.toString() }

                    getAppointmentsBySlotIdService(requestData).then((res) => {
                        setAppointmentSlotListBySlotId(res);
                    }).catch(() => {
                        console.log("error in getAppointmentsBySlotIdService");
                    })

                }

                break
            case 'Supplier':
                setNewAddress({ ...NewAddress, Supplier: event.target.value, Plan: "" })
                setPlans([]);
                let SupplierId = event.target.value;
                if (SupplierId) {
                    masterService.GetProductPlansFromCore(productId, SupplierId, "Recommendation").then(res => {
                        setPlans(res);
                    }).catch((e) => {
                        setPlans([]);
                        enqueueSnackbar("Could not fetch plans!", { variant: 'error', autoHideDuration: 3000 });
                    })
                }
                break;
            case 'Plan':
                setNewAddress({ ...NewAddress, Plan: event.target.value })
                break;
            case 'Income':
                setNewAddress({ ...NewAddress, IncomeId: event.target.value })
                break;
            case 'IncomeDocs':
                setNewAddress({ ...NewAddress, IncomeDocsId: event.target.value })
                break;
            case 'Education':
                setNewAddress({ ...NewAddress, EducationId: event.target.value })
                break;
            case 'appointmentTimeSlot':
                setActiveReschedulebtn(true);
                setNewAddress({ ...NewAddress, AppointmentTimeSlot: event.target.value })
                break;
            case 'Country':
                if (event.target.value) {
                    //setCurrentSelectedCountry(event.target.value)
                    setNewAddress({
                        ...NewAddress, CountryId: event.target.value, OfflineCity: "", OfflineCityId: 0,
                        AssignmentId: "", ZoneId: "",
                        Pincode: "", Landmark: ""
                    });
                    setInputLandmarkValue('');
                    setOptions([]);

                }
                break;
            case 'gender':
                if (event.target.value) {
                    let Gender = event.target.value;
                    setNewAddress({ ...NewAddress, Gender })
                }
                break;
            case 'InvestmentType':
                if (event.target.value) {
                    let InvestmentTypeId = event.target.value;
                    setNewAddress({ ...NewAddress, InvestmentTypeId })
                }
                break;

            case 'PolicyType':
                if (event.target.value === 'Fresh') {
                    setNewAddress({ ...NewAddress, HealthPolicyType: event.target.value, Portability: false, PolicyExpiryDate: null });
                } else if (event.target.value === 'Portable') {
                    setNewAddress({ ...NewAddress, HealthPolicyType: event.target.value, Portability: true });
                }
                break;

            default: break;
        }
    }
    const CheckSpecialEligibility = () => {
        if (productId == 2 && isAppCreatedSourceWAAppByCust && !IsEditAppDetails && IsRescheduling)
            return true;
        else return false;

    }
    const IsReschedulingAppoinment = () => {
        if (IsEditAppDetails || IsRescheduling) {
            return true;
        }
        return false
    };

    const isGenderMandatory = () => {
        // gender mandatory for new appointments only
        if (bypassGenderInput(productId)) {
            return false;
        }
        if (IsReschedulingAppoinment())
            return false;
        return true;
    };

    const validateAppointmentData = (newAddress, _appointmentDateTime) => {
        let IsSpecialEligibility = CheckSpecialEligibility();

        if (!newAddress) {
            enqueueSnackbar("All the fields are Mandatory!", { variant: 'error', autoHideDuration: 3000 });
            return false;
        }

        if (IsAppointmentCreateRestriction && IsManualTriggerAllowed && !IsReschedulingAppoinment() && !IsAppointmentCreated && !NewAddress.IsImportAddressClicked) {
            enqueueSnackbar("Please import the customer's shared location to schedule an appointment.", { variant: 'error', autoHideDuration: 3000 });
            return false;
        }
        if (isGenderMandatory() && newAddress.Gender <= 0) {
            enqueueSnackbar("Please select customer's gender to continue", { variant: 'error', autoHideDuration: 3000 });
            return false;
        }
        if ((newAddress && newAddress.AppointmentType == 5 && !IsSrcCustomerWhatsapp)) {
            enqueueSnackbar("Cancel the store appointment to schedule a Home appointment.", { variant: 'error', autoHideDuration: 5000 });
            return false;
        }
        if (newAddress && (newAddress.OfflineCity === "" || !newAddress.OfflineCity) && !IsSrcCustomerWhatsapp) {
            enqueueSnackbar("Please select a City to continue", { variant: 'error', autoHideDuration: 3000 });
            if (IsReschedulingAppoinment()) {
                gaEventTracker('FOS form', 'In Reschduling/Editing Select City Error');
            }
            return false;
        }
        // if (newAddress && !IsSrcCustomerWhatsapp && !IsReschedulingAppoinment() && MotorExpDeldataEligible && IsMotorRestrictionCityIds.indexOf(newAddress.OfflineCity.CityId) > -1) {
        //     enqueueSnackbar("Appointment creation is not allowed for new cars for selected city.", { variant: 'error', autoHideDuration: 3000 });

        //     return false;
        // }
        if (!newAddress.Pincode && !IsSrcCustomerWhatsapp && (!UAEUser || (UAEUser && NewAddress.CountryId == CountryAsIndia))) {
            enqueueSnackbar("Please select a Pincode to continue", { variant: 'error', autoHideDuration: 3000 });
            return false;
        }
        if ((newAddress && !newAddress.AppointmentType && !IsSrcCustomerWhatsapp)) {

            let AppointmentTypedump = {}
            AppointmentTypedump.AppointmentType = newAddress && newAddress.AppointmentType;
            AppointmentTypedump.fosAppointmentTypeMaster = fosAppointmentTypeMaster;
            AppointmentTypedump.leadid = parentLeadId;
            AppointmentTypedump = JSON.stringify(AppointmentTypedump);
            gaEventTracker('APPOINTMENTTYPE_FAIL_DUMP', AppointmentTypedump);

            // if(Array.isArray(fosAppointmentTypeMaster) && fosAppointmentTypeMaster.length>0 && fosAppointmentTypeMaster[0].AppointmentId > 0){

            // }
            // else{
            //     enqueueSnackbar("Please select Appointment Type.", { variant: 'error', autoHideDuration: 3000 });
            //     return false;
            // }
            enqueueSnackbar("Please select Appointment Type.", { variant: 'error', autoHideDuration: 3000 });
            return false;
        }//to be removed
        else if (!IsSrcCustomerWhatsapp && newAddress && newAddress.AssignmentId == "" && AssignmentTypeMaster && Array.isArray(AssignmentTypeMaster) && AssignmentTypeMaster.length == 0 && ShowNoCityServiceable) {
            // enqueueSnackbar("The selected city can only be served by " + AssignmentTypesForCity, { variant: 'error', autoHideDuration: 3000 });
            enqueueSnackbar("The selected city can not be served by you", { variant: 'error', autoHideDuration: 3000 });

            return false;
        }
        else if (!conditions.bindHomeCitiesOnly && newAddress && newAddress.AssignmentId == "" && !IsSrcCustomerWhatsapp) {
            enqueueSnackbar("Please select Assigned To.", { variant: 'error', autoHideDuration: 3000 });
            return false;
        }
        //Need to check
        else if (conditions.bindHomeCitiesOnly && !userAssignmentId && !IsSrcCustomerWhatsapp) {
            enqueueSnackbar("Assignment could not be found.", { variant: 'error', autoHideDuration: 3000 });
            return false;
        }
        else if (isZoneInputVisible && newAddress && !newAddress.ZoneId && !IsSrcCustomerWhatsapp) {
            enqueueSnackbar("Please select office zone", { variant: 'error', autoHideDuration: 3000 });
            return false;
        }
        else if (!newAddress.AppointmentDateSlot) {
            enqueueSnackbar("Please select a Date Slot for Appointment", { variant: 'error', autoHideDuration: 3000 });
            return false;
        }
        else if (!newAddress.AppointmentTimeSlot) {
            enqueueSnackbar("Please select a Time Slot for Appointment", { variant: 'error', autoHideDuration: 3000 });
            return false;
        }
        else if (!_appointmentDateTime) {
            enqueueSnackbar("Please select a Date and time for Appointment", { variant: 'error', autoHideDuration: 3000 });
            return false;
        }
        else if (AdditionaldetailsMandatory() && newAddress && newAddress.IncomeId == 0) {
            enqueueSnackbar("Please select Income", { variant: 'error', autoHideDuration: 3000 });
            return false;
        }
        else if (AdditionaldetailsMandatory() && newAddress && newAddress.IncomeDocsId == 0) {
            enqueueSnackbar("Please select Income Docs Availability", { variant: 'error', autoHideDuration: 3000 });
            return false;
        }
        else if (AdditionaldetailsMandatory() && newAddress && newAddress.EducationId == 0) {
            enqueueSnackbar("Please select Education Type", { variant: 'error', autoHideDuration: 3000 });
            return false;
        }

        // We are making non mandatory Income, IncomeDocs and Education field even for creating the appointment also.

        // else if (IsTermProduct && newAddress && NewAddress.AssignmentId != "3" && newAddress.IncomeId == 0 && !IsSrcCustomerWhatsapp) {
        //     enqueueSnackbar("Please select Income", { variant: 'error', autoHideDuration: 3000 });
        //     return false;
        // }
        // else if (IsTermProduct && newAddress && NewAddress.AssignmentId != "3" && newAddress.IncomeDocsId == 0 && !IsSrcCustomerWhatsapp) {
        //     enqueueSnackbar("Please select Income Docs Availability", { variant: 'error', autoHideDuration: 3000 });
        //     return false;
        // }
        // else if (IsTermProduct && newAddress && NewAddress.AssignmentId != "3" && newAddress.EducationId == 0 && !IsSrcCustomerWhatsapp) {
        //     enqueueSnackbar("Please select Education Type.", { variant: 'error', autoHideDuration: 3000 });
        //     return false;
        // }

        //  assignement type =3 (self)
        // else if (newAddress && newAddress.AssignmentId == "3" && (newAddress.City == "" || newAddress.Landmark == "" || newAddress.Address == "" || newAddress.Address1 == "" || newAddress.Pincode == "" || appointmentDateTime == "")) {
        //     enqueueSnackbar("All fields are mandatory.", { variant: 'error', autoHideDuration: 3000 });
        //     return false;
        // }
        //  AssignmentId type =2 (FOS)
        else if (newAddress && newAddress.AssignmentId == "2" && !IsSrcCustomerWhatsapp && (newAddress.OfflineCity == "" || !newAddress.Address.trim() || ((!UAEUser || (UAEUser && NewAddress.CountryId == CountryAsIndia)) && newAddress.Pincode == "") || _appointmentDateTime == "") && !IsSpecialEligibility) {
            enqueueSnackbar("All fields are mandatory.", { variant: 'error', autoHideDuration: 3000 });
            return false;
        }
        else if (newAddress && !IsSrcCustomerWhatsapp && Visible.Address && !NewAddress.Address.trim() && !IsSpecialEligibility) {
            enqueueSnackbar("All fields are mandatory.", { variant: 'error', autoHideDuration: 3000 });
            return false;
        }
        // else if (newAddress && !IsSrcCustomerWhatsapp && Visible.Address1 && !NewAddress.Address1.trim()) {
        //     enqueueSnackbar("All fields are mandatory.", { variant: 'error', autoHideDuration: 3000 });
        //     return false;
        // }
        //  appointment type =1 (offline)
        else if (productId != 2 && newAddress && newAddress.AppointmentType == "4" && !IsSrcCustomerWhatsapp && newAddress.AssignmentId == "1" && (newAddress.OfflineCity == "" || _appointmentDateTime == "" || !newAddress.Address.trim() || ((!UAEUser || (UAEUser && NewAddress.CountryId == CountryAsIndia)) && newAddress.Pincode == "") || _appointmentDateTime == "") && !IsSpecialEligibility) {
            enqueueSnackbar("All fields are mandatory.", { variant: 'error', autoHideDuration: 3000 });
            return false;
        }
        else if (productId == 2 && ProcessId == 4 && newAddress && newAddress.AppointmentType == "4" && !IsSrcCustomerWhatsapp && newAddress.AssignmentId == "1" && (newAddress.OfflineCity == "" || _appointmentDateTime == "" || !newAddress.Address.trim() || ((!UAEUser || (UAEUser && NewAddress.CountryId == CountryAsIndia)) && newAddress.Pincode == "") || _appointmentDateTime == "") && !IsSpecialEligibility) {
            enqueueSnackbar("All fields are mandatory.", { variant: 'error', autoHideDuration: 3000 });
            return false;
        }
        else if (newAddress && newAddress.AppointmentType == "5" && newAddress.AssignmentId == "1" && !IsSrcCustomerWhatsapp && (newAddress.OfflineCity == "" || _appointmentDateTime == "") && !IsSpecialEligibility) {
            enqueueSnackbar("All fields are mandatory.", { variant: 'error', autoHideDuration: 3000 });
            return false;
        }
        //storeoffline
        else if (productId == 2 && newAddress && !IsSrcCustomerWhatsapp && newAddress.AppointmentType == "4" && newAddress.AssignmentId == "4" && (newAddress.OfflineCity == "" || _appointmentDateTime == "" || !newAddress.Address.trim() || ((!UAEUser || (UAEUser && NewAddress.CountryId == CountryAsIndia)) && newAddress.Pincode == "") || _appointmentDateTime == "") && !IsSpecialEligibility) {
            enqueueSnackbar("All fields are mandatory.", { variant: 'error', autoHideDuration: 3000 });
            return false;
        }

        else if (Visible.Landmark && !IsSrcCustomerWhatsapp && !newAddress.Landmark && !IsSpecialEligibility)// since landmark is a mandatory field
        {
            enqueueSnackbar("All fields are mandatory.", { variant: 'error', autoHideDuration: 3000 });
            return false;

        }
        else if (Visible.Landmark && !IsSrcCustomerWhatsapp && !newAddress.place_id && !IsSpecialEligibility)// since landmark is a mandatory field
        {
            //enqueueSnackbar("NearBy Popular area/Place/Street is mandatory to select from AutoSuggested List.", { variant: 'error', autoHideDuration: 3000 });
            enqueueSnackbar(AutoSuggestValidationMessage + ' is mandatory to select from AutoSuggested List.', { variant: 'error', autoHideDuration: 3000 });
            return false;

        }
        else if (!IsSrcCustomerWhatsapp && Visible.Address && newAddress && newAddress.Address && newAddress.Address.trim().length < 10 && !IsSpecialEligibility) {
            enqueueSnackbar("Min 10 Characters for " + FreeTextAddressValidationMessage, { variant: 'error', autoHideDuration: 3000 });
            return false;
        }
        else if (!IsSrcCustomerWhatsapp && !IsReschedulingAppoinment() && newAddress && !newAddress.Comments) {
            enqueueSnackbar("Customer Interest/Special Instructions are mandatory",
                { variant: 'error', autoHideDuration: 3000 });
            return false;
        }
        else if (!IsSrcCustomerWhatsapp && !IsReschedulingAppoinment() && newAddress && NewAddress.Comments && newAddress.Comments.trim().length < 10) {
            enqueueSnackbar("Min 10 characters are required for Customer Interest/Special Instructions",
                { variant: 'error', autoHideDuration: 3000 });
            return false;
        }
        else if (!IsSrcCustomerWhatsapp && newAddress && NewAddress.Comments && newAddress.Comments.trim().length > 250) {
            enqueueSnackbar("Maximum 250 charactres are allowed for Customer Interest/Special Instructions",
                { variant: 'error', autoHideDuration: 3000 });
            return false;
        }
        else if (!IsSrcCustomerWhatsapp && productId == 115 && !IsReschedulingAppoinment() && newAddress && !NewAddress.InvestmentTypeId) {
            enqueueSnackbar("Please select Investment Type",
                { variant: 'error', autoHideDuration: 3000 });
            return false;
        }

        else if (!IsSrcCustomerWhatsapp && Visible.NearBy && Visible.Address && newAddress && NewAddress.Address && newAddress.NearBy
            && IsStringEqual(newAddress.Address, newAddress.NearBy) && !IsSpecialEligibility) {
            enqueueSnackbar("Address fields can not have matching values",
                { variant: 'error', autoHideDuration: 3000 });
            return false;
        }
        else if (!IsSrcCustomerWhatsapp && !IsReschedulingAppoinment() && productId == 2 && !IsLeadRenewal && IsSpokeCities(newAddress.OfflineCity?.CityId) && !IsSpokeCitiesDays(_appointmentDateTime.getDay())) {
            enqueueSnackbar("Appointment can only be scheduled for Wednesday, Thursday, Friday",
                { variant: 'error', autoHideDuration: 3000 });
            return false;
        }
        else if (!IsSrcCustomerWhatsapp && !IsReschedulingAppoinment() && productId == 2 && !IsLeadRenewal && IsSpokeCities(newAddress.OfflineCity?.CityId) && IsSpokeCitiesDays(_appointmentDateTime.getDay()) && !SpokeCitiesAppointmentCount(TotalAppointmentsByCityId, _appointmentDateTime.getDate())) {
            enqueueSnackbar("Appointment Capacity is full for the selected day",
                { variant: 'error', autoHideDuration: 3000 });
            return false;
        } else if (askPortability() && !NewAddress.HealthPolicyType) {
            enqueueSnackbar("Please select the policy type the customer is interested in.", { variant: 'error', autoHideDuration: 3000 });
            return false;
        } else if (
            newAddress.PolicyExpiryDate &&
            (dayjs(newAddress.PolicyExpiryDate).isBefore(dayjs(), 'day') ||
                dayjs(newAddress.PolicyExpiryDate).isAfter(dayjs().add(60, 'day'), 'day'))
        ) {
            enqueueSnackbar("Please select an expiry date within the next 60 days.", { variant: 'error', autoHideDuration: 3000 });
            return false;
        }

        return true;

    }

    const SaveAppointmentData = async function (params) {

        let newAddress = NewAddress;
        let _appointmentDateTime = null;
        // let appointmentTypeId_failsafe = -1;
        try {
            _appointmentDateTime = new Date(NewAddress.AppointmentDateSlot.fullDate);
            let hrs = getTimeHour(NewAddress.AppointmentTimeSlot.StartTime);
            // Need to set UTCHours as per backend, otherwise hours are in IST
            _appointmentDateTime.setUTCHours(hrs);
        } catch (e) {
            console.error(e);
        }
        // Need to check
        let _assignmentId = newAddress.AssignmentId;
        if (conditions.bindHomeCitiesOnly) {
            _assignmentId = userAssignmentId;
        }

        const isValid = validateAppointmentData(newAddress, _appointmentDateTime);

        if (!isValid) {
            return new Promise.reject({ isError: true, message: "Invalid appointment data" });
        }


        try {
            let gadump = {}
            gadump.Data = newAddress;
            gadump.pageOpeningSource = pageOpeningSource;
            gadump = JSON.stringify(gadump);
            gaEventTracker('APPOINTMENT_PAGE_OPENING_SOURCE', gadump, parentLeadId);
        } catch (e) { }
        // if(!NewAddress.AppointmentType && Array.isArray(fosAppointmentTypeMaster) && fosAppointmentTypeMaster.length>0 && fosAppointmentTypeMaster[0].AppointmentId > 0){
        //     appointmentTypeId_failsafe = fosAppointmentTypeMaster[0].AppointmentId;
        // }


        let reqData = {
            "CustomerId": IsSrcCustomerWhatsapp ? 0 : CustomerId,
            "ParentId": IsSrcCustomerWhatsapp ? 0 : parentLeadId,
            "UserId": IsSrcCustomerWhatsapp ? 4020 : userId,
            "OfflineCityId": (newAddress.OfflineCity && newAddress.OfflineCity.CityId) || 0,
            "Pincode": newAddress.Pincode || 0,
            // "AppointmentType": appointmentTypeId_failsafe > -1 ? appointmentTypeId_failsafe : (NewAddress.AppointmentType || 0),
            "AppointmentType": NewAddress.AppointmentType || 0,
            "AssignmentId": _assignmentId,//newAddress.AssignmentId, // Need to check
            "ZoneId": newAddress.ZoneId || 0,
            "AppointmentDateTime": _appointmentDateTime, //
            "SlotId": newAddress.AppointmentTimeSlot.SlotId,

            "Address": newAddress.Address && newAddress.Address.trim(),
            "Address1": null,//(newAddress.Address1 && newAddress.Address1.trim()),
            // "CityId": (newAddress.City && newAddress.City.CityID) || 0,
            "Landmark": newAddress.Landmark || "",
            "NearBy": (newAddress.NearBy && newAddress.NearBy.trim()) || "",
            "Comments": newAddress.Comments || "",
            "PlanList": recommendationCardList || [],

            "subStatusId": IsEditAppDetails ? newAddress.subStatusId : (IsRescheduling ? 2005 : undefined),
            "IncomeId": newAddress.IncomeId,
            "IncomeDocsId": newAddress.IncomeDocsId,
            "EducationId": newAddress.EducationId,
            "Gender": bypassGenderInput(productId) ? 0 : newAddress.Gender,
            "Source": IsSrcCustomerWhatsapp ? CustomerSource : (isSrcCrossSell ? pageOpeningSources.crosssell : "matrix"),
            "place_id": newAddress.place_id,
            "EncryptedLeadId": IsSrcCustomerWhatsapp ? window.localStorage.getItem('EncryptedLeadId') : "",
            "EncryptedCustomerId": IsSrcCustomerWhatsapp ? window.localStorage.getItem('EncryptedCustomerId') : "",
            "InvestmentTypeId": newAddress.InvestmentTypeId,
            "Portability": newAddress.Portability,
            "PolicyExpiryDate": newAddress.PolicyExpiryDate,
        };
        if (params && params.CancelReasonId) {
            reqData.CancelReasonId = params.CancelReasonId;
        }
        if ([7, 1000].indexOf(productId) > -1) {
            reqData = { ...reqData, productId: productId }
        }
        setSubmitReqData(reqData);
        let _cityPass = (newAddress.OfflineCity && newAddress.OfflineCity.CityId) || 0;
        let result = !IsEditAppDetails && ShowAgentAvailWarning(productId, _cityPass) ? (await CheckAgentAvailabilityInCity(parentLeadId, NewAddress.OfflineCity.CityId, NewAddress.AppointmentTimeSlot.SlotId, new Date(_appointmentDateTime).toISOString(), userId)) : true
        if (result) {
            return SaveAppointmentDataService(reqData);

        }
        else {
            return ProvideAgentAvailabilityWarningService(reqData);
            //setshowAgentAvailabilityWarningPopUp(true);
        }
    };

    const BindformFields = (response) => {
        setRecommendationCardList([]);
        var newStateArray = [];
        let arr = response.PlanList || [];
        for (var i = 0; i < arr.length; i++) {
            if (arr.length > 0) {
                // consider only one suggestion

                // if (supplierName.length > 0 && planName.length > 0) {
                let data = { "supplierId": parseInt(arr[i].supplierId), "SupplierName": arr[i].supplierName, "planId": parseInt(arr[i].planId), "PlanName": arr[i].planName }
                //var newStateArray = [...recommendationCardList];

                newStateArray.push(data);
                setRecommendationCardList(newStateArray)
            }

        }
    }

    const BindCityMaster = (response) => {

        let OfflineCityArr = DistinctCitiesMaster.filter(item => item.CityId == response.OfflineCityId);
        //let OfflineCityArr = TotalOfflineCitiesMaster.filter(item => item.CityId == response.OfflineCityId);

        if (OfflineCityArr.length > 0) {
            return OfflineCityArr[0];
        }
        return "";
    }


    const SaveAppointmentDataService = (reqData) => {
        setIsShowSaveBtn(false);
        setBookedAppointmentId(null);


        return SetAppointmentDataService(reqData).then((response) => {

            if (response != null) {

                if (response && parseInt(response.StatusCode) === 200) {
                    if (Array.isArray(AddressData) && AddressData.length > 0 && productId == 2 && !IsLeadRenewal && !IsReschedulingAppoinment()) {
                        try {
                            let reqData = {
                                "LeadId": parentLeadId,
                                "IsAddressUsed": NewAddress.IsCoreAddressImported,
                                "PreviousAddressClicked": NewAddress.PreviousAddressClicked
                            }
                            SaveCoreAddressUsageService(reqData).then((res) => {
                                if (res) {
                                    console.log("Saved Successfully")
                                }
                            }).catch((error) => {
                                console.log("error in SaveCoreAddressUsageService")
                            })

                        }
                        catch (e) {
                            console.log("error in SaveCoreAddressUsage")
                        }

                    }

                    setIsShowSaveBtn(true);
                    setIsSuccessfullySaved(true);
                    GetAppointmentData();
                    //ClearAddressDetails();
                    // enqueueSnackbar(response.Message, { variant: 'success', autoHideDuration: 3000, });
                    window.parent.postMessage({ "action": "SetAppointmentBooked" }, '*');
                    // window.parent.postMessage({ "action": response.Message }, '*');

                    if (response && response.data && response.data.AppointmentId) {
                        setBookedAppointmentId(response.data.AppointmentId);
                    }
                    if (conditions.showAfterAppointmentPopup) {
                        setOpenPopup("AppointmentBookSuccessPopup")
                    }
                    if (!isPageSource(pageOpeningSource, pageOpeningSources.salesview)) {
                        enqueueSnackbar("Appointment Saved Successfully", { variant: 'Success', autoHideDuration: 3000, });

                    }
                    //window.parent.postMessage({ "action": "AppCancel" }, '*');
                    if (IsSrcCustomerWhatsapp === true) {
                        UpdateReschduleStatus().then(() => {
                            if (window.localStorage.getItem('CustomerWhatsappURL')) {
                                setTimeout(() => {
                                    window.open(window.localStorage.getItem('CustomerWhatsappURL'), "_self")
                                }, 1500);
                            }
                        });

                        // window.history.back();

                    }


                }
                else {
                    setIsShowSaveBtn(true);
                    enqueueSnackbar(response.Message, { variant: 'error', autoHideDuration: 5000 });
                }
            }
            else {
                setIsShowSaveBtn(true);
                enqueueSnackbar("Something went wrong, Please Refresh page and try again", { variant: 'error', autoHideDuration: 3000, });
            }
            return response;
        }).catch((err) => {
            setIsShowSaveBtn(true);
            enqueueSnackbar("Something went wrong, please resubmit after few second", { variant: 'error', autoHideDuration: 3000, });
            throw err;

        });
    }

    const ProvideAgentAvailabilityWarningService = (reqData) => {
        setshowAgentAvailabilityWarningPopUp(true);
    }

    useEffect(() => {
        if (HandleAgentAvailability == "continue") {
            setshowAgentAvailabilityWarningPopUp(false);
            if (IsRescheduling) {
                setActiveReschedulebtn(false)
                SaveAppointmentDataService(SubmitReqData).then((response) => {
                    if (response && response.IsSaved) {
                        enqueueSnackbar("Data Save successfully", {
                            variant: 'success',
                            autoHideDuration: 3000,
                        });
                        setTimeout(() => {
                            window.parent.postMessage({ "action": "AppCancel" }, '*');
                        }, 1000);
                    } else {
                        props.handleClose();

                    }
                }).catch((error) => {
                    console.warn(error && error.message)
                })

            }
            else {
                SaveAppointmentDataService(SubmitReqData).then((response) => {
                    if (response && response.IsSaved) {
                        setIsEditAppDetails(false);
                        window.parent.postMessage({ "action": "AppointmentUpdate" }, '*');
                    }
                }).catch((error) => {
                    console.warn(error && error.message)
                })
            }
        }


    }, [HandleAgentAvailability])
    const updatePincodeMaster = (cityId) => {
        setPincodeMaster([]);
        setPincodeAPIstatus(API_STATUS.LOADING);

        GetPincodesForCityIDService(cityId, productId, IsLeadRenewal ? 'Renewal' : '').then((res) => {
            res = Array.isArray(res) ? res : [];
            // converting to string for Autocomplete
            setPincodeMaster(res.map(pincode => String(pincode)));
            setPincodeAPIstatus(API_STATUS.SUCCESS)
        }).catch((err) => {

            setPincodeMaster([]);
            setPincodeAPIstatus(API_STATUS.FAIL)
        })

    }

    const BindCountry = (SelectedOfflineCity) => {
        const SelectedCountry = DistinctCitiesMaster && DistinctCitiesMaster.filter(item => item.CityId == SelectedOfflineCity)
        if (SelectedCountry.length > 0)
            return SelectedCountry[0].CountryId;
    }



    const GetAppointmentData = () => {

        GetAppointmentDataService(CustomerId, parentLeadId).then(function (response) {
            if (response && response.CustomerId > 0) {
                setAppointmentDetailsfromDB(response);
                setIsRescheduling(true);
                setAppointmentDateTime(response.AppointmentDateTime);
                setStatusId(response.StatusId);
                setAppCreatedbyUserId(response.AppCreatedBy);
                setIsCustConfirmed(response.IsCustConfirmed);
                BindformFields(response);
                const SelectedOfflineCity = BindCityMaster(response);
                const _CurrentSelectedCountry = BindCountry(response.OfflineCityId);
                setCurrentAssignmentId(response.AssignmentId);
                // setCurrentSelectedCountry(_CurrentSelectedCountry);

                setNewAddress(prevState => ({ ...prevState, ...response, CountryId: _CurrentSelectedCountry, OfflineCity: SelectedOfflineCity, place_id: response.location && response.location.place_id, Address: (response.Address + (!!response.Address1 ? ', ' + response.Address1 : "")) }));

                let key = `${userId}_${parentLeadId}`;
                DeleteAppointmentDraft(key);

            }
            else {
                try {
                    let key = `${userId}_${parentLeadId}`;

                    let AppointmentDataDraft = ReadAppointmentDraftbyKey(key);
                    if (!!AppointmentDataDraft) {
                        let NewObj = AppointmentDataDraft.Appdata;
                        setNewAddress(NewObj);

                        if (!!NewObj.Supplier) {
                            masterService.GetProductPlansFromCore(productId, NewObj.Supplier, "Recommendation").then(res => {
                                setPlans(res);
                            }).catch((e) => {
                                setPlans([]);
                                enqueueSnackbar("Could not fetch plans!", { variant: 'error', autoHideDuration: 3000 });
                            })
                        }

                        let _appointmentDateTime = null;
                        _appointmentDateTime = NewObj && NewObj.AppointmentDateSlot && new Date(NewObj.AppointmentDateSlot.fullDate);
                        let hrs = NewObj && NewObj.AppointmentTimeSlot && getTimeHour(NewObj.AppointmentTimeSlot.StartTime);
                        //Need to set UTCHours as per backend, otherwise hours are in IST
                        _appointmentDateTime && hrs && _appointmentDateTime.setUTCHours(hrs);
                        setAppointmentDateTime(_appointmentDateTime)
                        setCurrentAssignmentId(NewObj.AssignmentId);
                    }
                }
                catch {

                }
            }

            DeleteAppointmentDraft();
            setGetAppointmentDataAPIStatus(API_STATUS.SUCCESS);


        }).catch(() => {
            setGetAppointmentDataAPIStatus(API_STATUS.FAIL);
        });
    }


    const GetOfflineCities = (Type, productId) => {
        GetOfflineCitiesService(parentLeadId, Type, productId).then(function (response) {
            if (response) {
                //Need to check
                if (conditions.bindHomeCitiesOnly) {
                    response = response.filter((_city) => _city.AppointmentTypeId === 4);
                }


                setOfflineCitiesMaster(response);

                const _CountryMaster = getCountryMaster(response);
                console.log("Distinct countries", _CountryMaster)
                setCountryMaster(_CountryMaster);
                console.log("OfflineCitiesMaster", response)
                const { DistinctCities, CityMapping } = getDistinctCitiesAppointmentMapping(response);
                console.log("123 sortedList is");

                setDistinctCitiesMaster(DistinctCities)
                console.log("Distinct Cities Master", DistinctCities)
                setDistinctCitiesAppointmentMappingMaster(CityMapping);
                console.log("DistinctCitiesAppointmentMappingMaster", CityMapping)

            }
        });
    }

    // const GetCityList = () => {
    // masterService.getCities().then(function (result) {
    //     setCities(result);
    // });
    // }

    const getSuppliers = () => {
        masterService.getSuppliers().then(function (response) {
            let uniqueSuppliers = [], map = {};
            response.forEach((supplier) => {
                let valid = (supplier.ProductId == productId && !map[supplier.OldSupplierId]);
                if (valid) {
                    uniqueSuppliers.push(supplier);
                    map[supplier.OldSupplierId] = 1;
                }
            });
            setSuppliers(uniqueSuppliers);
        });
    }



    const supplierList = Suppliers.filter(supplier => {
        let valid = true;
        valid = (valid && (supplier.ProductId == productId));
        return valid;
    });


    const planList = Array.isArray(Plans) ? Plans.filter(plan => {
        let valid = true;
        valid = valid && (plan.ProductId == productId);
        return valid;
    }) : []


    const GetAppointmentTypeMaster = (productId) => {
        const input = {
            url: `api/SalesView/GetAppointmentTypeList/${parentLeadId}/${productId}`,
            method: 'GET', service: 'MatrixCoreAPI',
        };
        return CALL_API(input).then(function (response) {
            if (response && response.fosAppointmentList != null) {
                let arr = [];
                setFOSMaster(response);
                arr = response.fosAppointmentList;
                if (arr.length > 0) {
                    if (arr[0].hasOwnProperty('ProcessId')) {
                        setProcessId(arr[0].ProcessId);
                    }
                    if (arr[0].AppointmentId == 5) {
                        enqueueSnackbar("Cancel the store appointment to schedule a Home appointment", { variant: 'error', autoHideDuration: 5000, });
                    }
                }
                else {
                    enqueueSnackbar("No cities available to schedule appointment", { variant: 'error', autoHideDuration: 3000, });
                }
                setFosAppointmentTypeMaster(arr);

                setNewAddress(prevState => ({ ...prevState, AppointmentType: arr[0].AppointmentId }))
            }
        }).catch((err) => {
            enqueueSnackbar("Please re-open the appointment module", { variant: 'error', autoHideDuration: 3000, });
        });
    }
    const AddItem = () => {

        if (NewAddress.Supplier == "" || NewAddress.Supplier == undefined) {
            enqueueSnackbar("Please select supplier", {
                variant: 'error',
                autoHideDuration: 3000,
            });
            return;
        }
        else if (NewAddress.Plan == "" || NewAddress.Plan == undefined) {
            enqueueSnackbar("Please select Plan", {
                variant: 'error',
                autoHideDuration: 3000,
            });
            return;
        }

        if (recommendationCardList.length > 0) {
            let Isexist = recommendationCardList.filter(obj => {
                return obj.planId == NewAddress.Plan
            });

            if (Isexist.length > 0) {
                enqueueSnackbar("Plan is already added", {
                    variant: 'error',
                    autoHideDuration: 3000,
                });
                return;
            }

        }


        let supplierName = supplierList.filter(obj => {
            return obj.SupplierId == NewAddress.Supplier
        });

        let planName = planList.filter(obj => {
            return obj.PlanId == NewAddress.Plan
        });



        if (supplierName.length > 0 && planName.length > 0) {
            let data = { "supplierId": NewAddress.Supplier, "SupplierName": supplierName[0].SupplierName, "planId": NewAddress.Plan, "PlanName": planName[0].PlanName }
            var newStateArray = [...recommendationCardList];
            newStateArray.push(data);
            setRecommendationCardList(newStateArray)
        }
    }

    const RemoveItem = (data) => {
        var newStateArray = [...recommendationCardList];
        for (var i = 0; i < newStateArray.length; i++) {
            if (newStateArray[i].supplierId == data.supplierId && newStateArray[i].planId == data.planId) {
                newStateArray.splice(i, 1);
                break;
            }
        }
        setRecommendationCardList(newStateArray)
    }

    const ChangeAssignmentMaster = () => {
        setShowNoCityServiceable(false);
        if (FOSMaster && FOSMaster.fosAppointmentList && FOSMaster.fosAssignmentList.length > 0) {
            let _assigmentIDs = []

            setCurrentAssignmentId(-1);


            if (NewAddress.OfflineCity && NewAddress.OfflineCity.CityId) {
                const cityObject = DistinctCitiesAppointmentMappingMaster[NewAddress.OfflineCity.CityId];

                if (cityObject && cityObject.mappings) {
                    const appointmentAssignmentZoneMapping = cityObject.mappings;
                    appointmentAssignmentZoneMapping.forEach((mapObj) => {
                        if (mapObj.AppointmentTypeId === fosAppointmentTypeMaster[0].AppointmentId) {
                            //if (mapObj.AppointmentTypeId === NewAddress.AppointmentType) {
                            _assigmentIDs.push(mapObj.AssigntmentId);
                        }
                    })

                };
            }
            let arr = FOSMaster.fosAssignmentList.filter(obj => {

                if (_assigmentIDs.find((id) => id === obj.AssignmentId)) {
                    return obj.AppointmentId == fosAppointmentTypeMaster[0].AppointmentId//NewAddress.AppointmentType
                }
                //}
                return false
            });

            if (arr.length === 0) {
                setShowNoCityServiceable(true);

                let _AssignmentTypesForCity = ''

                _AssignmentTypesForCity = Array.isArray(_assigmentIDs) && _assigmentIDs.map((item) =>

                    AssignmentIdTypeMapping[item - 1] && AssignmentIdTypeMapping[item - 1].AssignmentType
                ).join(", ")
                console.log("at line 1170 _AssignmentTypesForCity", _AssignmentTypesForCity)
                setAssignmentTypesForCity(_AssignmentTypesForCity)
            }


            let tempAssignmentArray = arr && arr.map(a => a.AssignmentId);
            if (arr.length > 1 && tempAssignmentArray && tempAssignmentArray.indexOf(1) > -1 && (tempAssignmentArray.indexOf(4) > -1 || tempAssignmentArray.indexOf(2) > -1)) {
                const index = arr.findIndex(item => item.AssignmentId === 1);
                arr.splice(index, 1);

            }

            setAssignmentTypeMaster(arr);
            if (arr.length === 1)// for only one result
            {
                setNewAddress({ ...NewAddress, AssignmentId: arr[0].AssignmentId, AssignmentType: arr[0].AssignmentType, ZoneId: "" })
                setCurrentAssignmentId(arr[0].AssignmentId);

            }

        }
    }

    const updateAppointmentList = (city) => {
        let _appointmentTypeIDs = [];

        if (city && city.CityId) {
            const cityObject = DistinctCitiesAppointmentMappingMaster[city.CityId];

            if (cityObject && cityObject.appointmentTypeIDs) { _appointmentTypeIDs = cityObject.appointmentTypeIDs };
        }
        _appointmentTypeIDs = Array.isArray(_appointmentTypeIDs) ? _appointmentTypeIDs : [];

        const _appointmentTypeList =
            Array.isArray(fosAppointmentTypeMaster)
                ? fosAppointmentTypeMaster.filter(appointmentType => _appointmentTypeIDs.includes(appointmentType.AppointmentId))
                : [];
        setAppointmentTypeList(_appointmentTypeList);

        // Need to check
        if (conditions.bindHomeCitiesOnly && _appointmentTypeList.length > 0) {
            setNewAddress(prevState => ({ ...prevState, AppointmentType: _appointmentTypeList[0].AppointmentId, AssignmentId: "", ZoneId: "" }))
        }
    }

    const updateZoneList = () => {
        let _zones = [];

        if (NewAddress.OfflineCity && NewAddress.OfflineCity.CityId) {
            const cityObject = DistinctCitiesAppointmentMappingMaster[NewAddress.OfflineCity.CityId];

            if (cityObject && cityObject.mappings) {
                const appointmentAssignmentZoneMapping = cityObject.mappings;
                appointmentAssignmentZoneMapping.forEach((mapObj) => {
                    if (
                        mapObj.AppointmentTypeId === NewAddress.AppointmentType
                        && mapObj.AssigntmentId === NewAddress.AssignmentId
                    ) {
                        _zones.push(mapObj);
                    }
                })

            };
        }

        // if (city && city.CityId) {
        //     const cityObject = DistinctCitiesAppointmentMappingMaster[city.CityId];

        //     if (cityObject && cityObject.zones) { _zones = cityObject.zones };
        // }
        _zones = Array.isArray(_zones) ? _zones : [];
        setZoneList(_zones);
    }
    const GetLandmarkData = (inputkey) => {
        if (inputkey !== '') {

            let pincode = NewAddress.Pincode && String(NewAddress.Pincode)//NewAddress.Pincode;
            let ts = parseInt(localStorage.getItem('LandMarkTimeStamp'));
            if (ts === 0 || (ts + 300000 < new Date().getTime())) {

                ts = new Date().getTime();
                //setTimeStamp(ts);
                localStorage.setItem('LandMarkTimeStamp', ts);
            }
            let SessionToken = parentLeadId + '_' + ts;
            // const input = {
            //     url: `fos/api/FOS/getAutoSuggestions/${SessionToken}/${pincode}/${inputkey}`,
            //     method: 'GET', service: 'MatrixCoreAPI',

            // };
            let reqData =
            {
                "Pincode": (UAEUser && NewAddress.CountryId != CountryAsIndia && !pincode) ? "0" : pincode,
                "sessiontoken": SessionToken,
                "Input": inputkey,
                "CityId":(NewAddress.OfflineCity && NewAddress.OfflineCity.CityId) || 0
            }
            // let response=[
            //     {
            //      "main_text": "policybazaar Corporate Office",
            //      "secondary_text":"sector 44, Gurugram, Haryana, India",
            //      "place_id": "ChIJ____P-wYDTkR3zi0uU6PufM",
            //      },
            //      {
            //      "main_text": "Policybazaar UAE",
            //      "secondary_text":"dubai - United Arab Emirates",
            //      "place_id": "ChIJI3yqfHJrXz4RXKjr3UZS2Hw",
            //      },

            // ]
            getLandmarkdetailsService(reqData).then((response) => {
                if (response) {
                    let newOptions = [];
                    // if (NewAddress.Landmark) {
                    //     newOptions = [NewAddress.Landmark];
                    // }
                    if (response) {
                        newOptions = [...newOptions, ...response];
                    }
                    setOptions(newOptions);
                }

            }).catch((err) => {
                setOptions([]);
            })
        }
        else {
            setOptions([]);
        }



    }

    const onClickAppDetails = () => {

        setIsRescheduling(false);
        setIsEditAppDetails(true);
    }
    const validateReschduleAppointmentData = () => {
        let newAddress = NewAddress;
        let _appointmentDateTime = '';
        try {
            _appointmentDateTime = new Date(NewAddress.AppointmentDateSlot.fullDate);
            let hrs = getTimeHour(NewAddress.AppointmentTimeSlot.StartTime);
            // Need to set UTCHours as per backend, otherwise hours are in IST
            _appointmentDateTime.setUTCHours(hrs);
        } catch (e) {
            console.error(e);
        }
        const isValid = validateAppointmentData(newAddress, _appointmentDateTime);

        return isValid;

    }
    const fnOpenFosCancelReason = (Substatus, IsCustConfirmed) => {
        if (Substatus.Id == 2005) {
            let isEligibleReschdule = true;
            if (IsCustConfirmed) {
                IsAppMarkCancelService(parentLeadId).then(function (response) {
                    if (response && response.status === false) {
                        isEligibleReschdule = false;
                        enqueueSnackbar(response.message, {
                            variant: 'error',
                            autoHideDuration: 3000,
                        });
                        return;
                    }
                    else if (response && response.status == true) {
                        if (validateReschduleAppointmentData()) {
                            setselectedSubStatusID(Substatus.Id);
                            setOpenFosCancelReasonPopup(true);
                        }
                    }
                }).catch(err => {
                    console.log("Error in App mark cancel API", err);
                })
            }
            else {
                if (validateReschduleAppointmentData()) {
                    setselectedSubStatusID(Substatus.Id);
                    setOpenFosCancelReasonPopup(true);
                }
            }

        }

        if (Substatus.Id === 2004) {

            IsAppMarkCancelService(parentLeadId).then(function (response) {
                if (response && response.status === false) {
                    enqueueSnackbar(response.message, {
                        variant: 'error',
                        autoHideDuration: 3000,
                    });
                    return;
                }
                else {
                    setselectedSubStatusID(Substatus.Id);
                    setOpenFosCancelReasonPopup(true);
                }
            }).catch(err => {
                console.log("Error in App mark cancel API", err);
            })
        }

    }

    const fnUpdateAppointmentData = () => {

        let newAddress = NewAddress;
        let _appointmentDateTime = null;
        try {
            _appointmentDateTime = new Date(NewAddress.AppointmentDateSlot.fullDate);
            let hrs = getTimeHour(NewAddress.AppointmentTimeSlot.StartTime);
            // Need to set UTCHours as per backend, otherwise hours are in IST
            _appointmentDateTime.setUTCHours(hrs);
        } catch (e) {
            console.error(e);
        }
        // Need to check
        let _assignmentId = newAddress.AssignmentId;
        if (conditions.bindHomeCitiesOnly) {
            _assignmentId = userAssignmentId;
        }

        const isValid = validateAppointmentData(newAddress, _appointmentDateTime);

        if (!isValid) {
            return;
        }

        SaveAppointmentData().then((response) => {
            if (response && response.IsSaved) {
                setIsEditAppDetails(false);
                window.parent.postMessage({ "action": "AppointmentUpdate" }, '*');
            }
        }).catch((err) => {
            console.warn(err && err.message)
        });


    }

    const fnSaveAppointmentData = (skip) => {
        SaveAppointmentData({}).then((response) => {
            if (response && response.IsSaved) {
                setIsEditAppDetails(false);
                window.parent.postMessage({ "action": "AppointmentUpdate" }, '*');
            }
        }).catch((err) => {
            console.warn(err && err.message)
        });
    }

    const AskLocation = () => {
        if (isCustomerNotOptedInWhatsapp && !waOptInBtnDisabled) {
            setWAOptblink(true)
            enqueueSnackbar("Click \"Request WhatsApp Opt-in\" button first, and remember to pitch WhatsApp opt in before requesting location", {
                variant: 'error',
                autoHideDuration: 2000,
            });
            return
        }
        setAskLocationBtnDisabled(true);
        setRestrictLandmarkIcon(false);
        if (IsCustomerLocationAvailable) {
            enqueueSnackbar("Location Requests to customers can be sent once only.", {
                variant: 'error',
                autoHideDuration: 2000,
            });
            return false;
        }
        let reqData =
        {
            "LeadId": parentLeadId,
            "CityId": NewAddress.OfflineCity && NewAddress.OfflineCity.CityId,
            "City": NewAddress.OfflineCity && NewAddress.OfflineCity.CityName,
            "CountryId": NewAddress.OfflineCity && NewAddress.OfflineCity.CountryId,
            "Source": "Matrix",
            "CreatedBy": userId
        }
        TriggerSaveCustomerLocationData(reqData).then((response) => {
            console.log("88888", response)
            setIsCustomerLocationAvailable(true);
            enqueueSnackbar("Location request sent to customer via WhatsApp and SMS.", {
                variant: 'success',
                autoHideDuration: 3000,
            });
        }).catch((error) => {
            console.log("88888", error)
            setAskLocationBtnDisabled(false);
        })
    }

    const GetCustomerDetails = () => {
        GetCustomerLocationData(CustomerId, parentLeadId).then((response) => {
            setOpenPreviewCustomer(true);
            if (response != null && response.Data != null) {
                console.log("GetCustomerLocationData", response);
                let OfflineCityObj = { "CityId": response.Data.CityId, "CityName": response.Data.City, "CountryId": response.Data.CountryId }
                setPreviewCustomerResponse(response.Data);

            }

        }).catch((error) => {
            console.log("error in GetCustomerLocationData", error)
        })
    }
    const RestrictLandmarkEdit = () => {

        if (!IsSrcCustomerWhatsapp && NewAddress && (NewAddress.OfflineCity === "" || !NewAddress.OfflineCity)) {
            return true;
        }
        else if ((!UAEUser && NewAddress && !NewAddress.Pincode) || (UAEUser && NewAddress.CountryId == CountryAsIndia && NewAddress && !NewAddress.Pincode)) {
            return true;
        }
    }
    const handleAssignmentChange = (event, obj) => {
        setNewAddress({ ...NewAddress, AssignmentId: obj.AssignmentId, AssignmentType: obj.AssignmentType, ZoneId: "" })
        setCurrentAssignmentId(obj.AssignmentId);
    }

    const IsRequestLocationcrieteria = () => {
        if (((RestrictLandmarkforCustProductIds && RestrictLandmarkforCustProductIds.indexOf(productId) > -1) || RestrictLandmarkforCustGroups) && IsManualTriggerAllowed && !IsAppointmentCreated && !IsReschedulingAppoinment() && !IsCustomerLocationAvailable && !AskLocationBtnDisabled) {
            return true;
        }
        return false;
    }

    const CheckLandmarkRestriction = (e) => {
        // alert("In landmark")
        setIsLandmarkAttempted(true);
        if (RestrictLandmarkEdit()) {
            e.preventDefault();
            setShowErrorBorderonLandmark(true)
            enqueueSnackbar('Please select City/Pincode first to enter the landmark',
                { variant: 'error', autoHideDuration: 3000, }); return false
        }
       

        else if (IsRequestLocationcrieteria()) {
            e.preventDefault();
            setRestrictLandmarkIcon(true);
            setShowErrorBorderonLandmark(true)
            enqueueSnackbar(WAOptblink?'Click "Request WhatsApp Opt-in" button first, and remember to pitch WhatsApp Opt-in location':'you have to request customer Location first',
                { variant: 'error', autoHideDuration: 3000, }); return false
        }

        else {
            setShowErrorBorderonLandmark(false);
            setRestrictLandmarkIcon(false);
        }
        return true;

    }

    const AdditionaldetailsMandatory = () => {
        if (!IsReschedulingAppoinment() && productId == 115 && NewAddress.AssignmentId != 3)
            return true;
        else
            return false;

    }
    const fnCloseFosPopUp = () => {
        props.handleClose();
    }
    // const fn_SetOpenAddress = () => {
    //     try {

    //         gaEventTracker('FOSAPPOINTMENT', 'VIEW_SAVED_ADDRESS', parentLeadId);
    //     }
    //     catch (e) {
    //         console.log("error in APPOINTMENT_VIEW_SAVED_ADDRESS")
    //     }
    //     setNewAddress((prevState) => ({
    //         ...prevState,
    //         PreviousAddressClicked: true
    //     }));
    //     setOpenSavedCustomerAddress(true)
    // }
    let GetLandmarkDataDebounce = useCallback(debounce(GetLandmarkData, 300), [parentLeadId, NewAddress.Pincode]);

    React.useEffect(() => {
        let active = true;


        if (InputLandmarkValue === '') {
            setOptions(NewAddress.Landmark ? [NewAddress.Landmark] : []);
            return undefined;
        }


        let results = GetLandmarkDataDebounce(InputLandmarkValue);


        return () => {
            active = false;
        };
    }, [NewAddress.Landmark, InputLandmarkValue]);
    useEffect(() => {

        if ((GetAppointmentDataAPIStatus === API_STATUS.SUCCESS) && isGenderMandatory()) {
            getBasicLeadDetailsService(parentLeadId).then((response) => {
                if (response.Gender > 0) {
                    setNewAddress(prevState => ({ ...prevState, Gender: response.Gender, LeadStateId: response.StateID, LeadCityID: response.CityID }))
                    setIsGenderDisabled(true);
                }
                else {
                    setNewAddress(prevState => ({ ...prevState, LeadStateId: response.StateID, LeadCityID: response.CityID }))
                }
            });
        }
        if ((GetAppointmentDataAPIStatus === API_STATUS.SUCCESS)) {
            const requestData = {
                CustomerId: CustomerId,
                CommTypeId: 5,
                LeadId: parentLeadId,
                UserId: userId,
                Source: "FOS",
            }

            const waOptoutTriggerProducts = (SV_CONFIG && Array.isArray(SV_CONFIG.waOptoutTriggerProds) ? SV_CONFIG.waOptoutTriggerProds : []) || [];

            if (waOptoutTriggerProducts.includes(productId)) {
                getPrefCommService(requestData).then((response) => {
                    if (response?.Status) {
                        setIsCustomerNotOptedInWhatsapp(!response.Data)
                    }
                });
            }
        }
    }, [GetAppointmentDataAPIStatus])

    let url = "";
    if (!IsSrcCustomerWhatsapp) {
        url = `${SV_CONFIG["matrixdashboard"][SV_CONFIG["environment"]]}/admin/FosAppointmentData?leadid=` + parentLeadId;
    }

    if ([API_STATUS.LOADING].includes(GetAppointmentDataAPIStatus)) {
        return <LinearProgress color="secondary" variant="query" />;
    }


    if ([API_STATUS.FAIL].includes(GetAppointmentDataAPIStatus)) {
        return <h4>Unable to fetch data, Please retry after some time.</h4>
    }
    let showGender = true;
    if (IsReschedulingAppoinment() && (!NewAddress.Gender && NewAddress.Gender <= 0)) {
        showGender = false
    }

    const handleWAOptInRequest = () => {

        if (waOptInBtnDisabled) {
            return;
        }

        setWaOptInBtnDisabled(true)
        setWAOptblink(false);
        const requestBody = {
            leadId: parentLeadId,
            userId: userId,
            source: "FOS"
        }

        requestWhatsAppOptIn(requestBody)
            .then(response => {
                const message = response.status
                    ? 'Whatsapp OptIn Request Sent Successfully!'
                    : 'Whatsapp OptIn Request Not Sent!'

                if (!response.status) {
                    setTimeout(() => {
                        setWAOptblink(false);
                        setWaOptInBtnDisabled(false);
                    }, 10000)
                }

                enqueueSnackbar(message, { variant: response.status ? 'success' : 'error', autoHideDuration: 5000 })
            })
            .catch(error => {
                enqueueSnackbar("Whatsapp OptIn Request Not Sent!", { variant: 'error', autoHideDuration: 5000 })
                console.error("Error sending WhatsApp Opt-In request:", error);

                setTimeout(() => {
                       setWAOptblink(false);
                    setWaOptInBtnDisabled(false);
                }, 10000)
            })
    }

    const askPortability = () => {
        const fosPortableProducts = (SV_CONFIG && Array.isArray(SV_CONFIG.fosPortableProducts) ? SV_CONFIG.fosPortableProducts : []) || [];

        return fosPortableProducts.includes(productId) && !IsLeadRenewal && !IsReschedulingAppoinment()
    }

    return (
        <>
            <div className="Fosheader">
                <Grid container spacing={1}>
                    <Grid item sm={8} md={8} xs={8} >
                        <h6>{IsEditAppDetails ? 'Edit Appointment' : 'Schedule Appointment'}</h6>
                    </Grid>
                    <Grid item sm={4} md={4} xs={4} >
                        {!IsSrcCustomerWhatsapp && url && <button onClick={() => { setOpenHistory(true) }} className="viewHistoryBtn">View History</button>}

                    </Grid>

                    {!IsSrcCustomerWhatsapp && isSrcCrossSell &&
                        <Grid item sm={12} md={12} xs={12} >
                            <p className="crossSellMsg">{'This Appointment is created for LeadId: ' + parentLeadId}</p>
                        </Grid>
                    }

                </Grid>

            </div>

            <Grid container className={disabled ? `${classes.root}` : ""}>
                {/* {productId == 2 && <Grid item sm={12} md={12} xs={12} >
                    <p className="Message">Customers will receive a link to confirm their address over WhatsApp and SMS after appointment is booked. Please ask your customers to confirm or edit the address using this link. This will help us serve our customers better and on time</p>
                </Grid>
                } */}
                <div className="scrollbar">
                    <CurrentAppointmentAction fnUpdateAppointmentData={fnUpdateAppointmentData} onClickAppDetails={onClickAppDetails} IsEditAppDetails={IsEditAppDetails} IsRescheduling={IsRescheduling} NewAddress={NewAddress} AssignmentTypeMaster={AssignmentTypeMaster} appointmentDateTime={appointmentDateTime} AppointmentDetails={AppointmentDetailsfromDB} ActiveReschedulebtn={ActiveReschedulebtn} />

                    {/***********   Appointment Address   **********/}
                    {!IsShowFields && <FormSectionHeading
                        icon={CONFIG.PUBLIC_URL + "/images/salesview/locationDetails.svg"}
                        title="Appointment Address"
                        showpara={true}
                        show={!IsSrcCustomerWhatsapp}

                    />}



                    {!IsShowFields && showGender &&
                        <Grid container spacing={3} className="pd25 gender">
                            <Grid item className="grid-item">
                                <FormLabel component="span" className="Label" required={isGenderMandatory()}>
                                    Gender
                                </FormLabel>
                                {(gendersMaster.map((gender) => (
                                    <FormControlLabel
                                        key={gender.value}
                                        value={gender.value}
                                        control={<Radio color="secondary" />}
                                        label={gender.label}
                                        onChange={handleChange}
                                        checked={NewAddress.Gender == gender.value}
                                        name="gender"
                                        disabled={disabled || isGenderDisabled || IsEditAppDetails || IsReschedulingAppoinment()}
                                    />
                                )))}
                            </Grid>
                        </Grid>

                    }
                    {/* {Array.isArray(AddressData) && AddressData.length > 0 && productId == 2 && !IsLeadRenewal && !IsReschedulingAppoinment() && <Grid container spacing={3} className="pd25">
                        <Grid item sm={12} md={12} xs={12}>
                            <div className="SaveAddress">
                                <img src={CONFIG.PUBLIC_URL + "/images/rescheduled/new.svg"} />
                                There are previously saved addresses for this customer.
                                <button onClick={() => { fn_SetOpenAddress() }}>View Saved Addresses</button>
                            </div>
                        </Grid>
                    </Grid>
                    } */}
                    {!IsSrcCustomerWhatsapp && !IsShowFields && <Grid container spacing={3} className="pd25" >
                        {UAEUser && <SelectDropdown
                            name="Country"
                            label="Country*"
                            // value={CurrentSelectedCountry}
                            value={NewAddress.CountryId}
                            handleChange={handleChange}
                            fullWidth={true}
                            sm={6} md={6} xs={12}
                            options={CountryMaster}
                            labelKeyInOptions='Country'
                            valueKeyInOptions='CountryId'
                            disabled={disabled || IsEditAppDetails}
                        // disabled={disabled || conditions.bindHomeCitiesOnly || IsEditAppDetails}
                        //show={!conditions.bindHomeCitiesOnly}
                        />
                        }
                        <Grid item sm={6} md={6} xs={12} >
                            <Autocomplete
                                onChange={(event, value) => handleChange({ target: { name: 'OfflineCity', value } })}
                                id="OfflineCity"
                                //  options={UAEUser ? DistinctCitiesMaster.filter(item => item.CountryId === NewAddress.CountryId) : DistinctCitiesMaster.filter(item => item.CountryId === CountryAsIndia)}
                                options={UAEUser ? DistinctCitiesMaster.filter(item => item.CountryId === NewAddress.CountryId) : DistinctCitiesMaster.filter(item => item.CountryId === CountryAsIndia)}
                                name="OfflineCity"
                                value={NewAddress.OfflineCity || null}
                                getOptionLabel={(option) => (option.CityName) || ''}
                                className="cityBox"
                                disabled={disabled || IsEditAppDetails}

                                renderInput={(params) =>
                                    <TextField {...params}
                                        label="Select City*"
                                        variant='outlined' />}

                            />
                        </Grid>
                        {(!UAEUser || (UAEUser && NewAddress.CountryId === CountryAsIndia)) && <Grid item sm={6} md={6} xs={12} >
                            <Autocomplete
                                onChange={(event, value) => handleChange({ target: { name: 'Pincode', value } })}
                                id="Pincode"
                                options={PincodeMaster}
                                name="Pincode"
                                value={String(NewAddress.Pincode) || null}
                                getOptionLabel={(option) => option}
                                className="pincodeBox"
                                disabled={disabled || IsEditAppDetails}
                                freeSolo={false}

                                loading={pincodeAPIstatus === API_STATUS.LOADING}
                                renderInput={(params) =>
                                    <TextField
                                        {...params}
                                        label={(!UAEUser || (UAEUser && NewAddress.CountryId == CountryAsIndia)) ? "Pincode*" : "Pincode"}
                                        variant="outlined"
                                        InputProps={{
                                            ...params.InputProps,
                                            endAdornment: (
                                                <InputAdornment position="end" className="LoadingAdorn">
                                                    {pincodeAPIstatus === API_STATUS.LOADING ? <CircularProgress size={20} /> : null}
                                                    {params.InputProps.endAdornment}
                                                </InputAdornment>
                                            ),
                                        }}
                                    />
                                }
                            />
                        </Grid>
                        }
                        {IsWhatsappLocationManualProductId && IsManualTriggerAllowed && !IsReschedulingAppoinment() && !IsAppointmentCreated && NewAddress && NewAddress.OfflineCity &&
                            <Grid item sm={12} md={12} xs={12} >
                                <div className={RestrictLandmarkIcon && IsRequestLocationcrieteria()  ? (WAOptblink?"GetCustomerLocation notblinklayout" :"GetCustomerLocation errorLayout") : "GetCustomerLocation"}>
                                    <p> Get Customer's location</p>
                                    <h4>Location responses lead to 5x more visits and bookings.</h4>
                                      {isCustomerNotOptedInWhatsapp && (
                                        <Box className="OptedWhatsapp">
                                            <img src={`${CONFIG.PUBLIC_URL}/images/salesview/whatsapp1.svg`} alt="Whatsapp" title="Whatsapp Available" />
                                            <Typography variant="body2" sx={{ flexGrow: 1 }}>
                                               Customer not opted in to WhatsApp
                                            </Typography>
                                            <Button
                                                variant="contained"
                                                color="primary"
                                                className={`${waOptInBtnDisabled ? 'waoptin-disabled' : WAOptblink?'waoptblink':''}`}
                                                onClick={handleWAOptInRequest}
                                            >
                                                Request WhatsApp Opt-In via SMS
                                            </Button>
                                            <LightTooltip
                                                title={
                                                    <div style={{ width: '169px', fontWeight: 500 }}>
                                                        To enable Whatsapp Communication, the customer will receive an SMS with a link to confirm their Whatsapp opt-in.
                                                    </div>
                                                }
                                                placement="top-end"
                                                PopperProps={{ container: document.body }}
                                            >
                                                <div className="i-icon">
                                                    <InfoOutlinedIcon style={{ height: '20px', color: 'white' }} />
                                                </div>
                                            </LightTooltip>
                                        </Box>
                                    )}
                                    <div className="buttonLayout">
                                        {
                                            IsManualTriggerAllowed && !IsReschedulingAppoinment() && !IsAppointmentCreated && NewAddress && NewAddress.OfflineCity && <button className={(AskLocationBtnDisabled || IsCustomerLocationAvailable) ? "disabled requestLocationBtn" : "requestLocationBtn"} disabled={AskLocationBtnDisabled || IsCustomerLocationAvailable} onClick={() => {
                                                AskLocation();
                                            }}>Request location
                                            </button>
                                        }
                                        {/* <button className="requestLocationBtn">Request location</button> */}
                                        {IsManualTriggerAllowed && !IsReschedulingAppoinment() && !IsAppointmentCreated && <button onClick={() => { GetCustomerDetails() }}>Check status</button>}
                                        {/* <button>Check status</button> */}
                                        <button onClick={() => { setHowItWorksOpen(true) }}><HelpOutlineIcon /> How it works</button>
                                    </div>
                                    <img src={CONFIG.PUBLIC_URL + "/images/rescheduled/map.svg"} />
                                  <p className="HeightLightMsg"><b>Important:</b> Don’t just click ‘Request Location’—remember to pitch location sharing for the appointment.</p>
                                </div>
                                </Grid>
                        }

                        {!IsWhatsappLocationManualProductId && IsManualTriggerAllowed && !IsReschedulingAppoinment() && !IsAppointmentCreated && NewAddress && NewAddress.OfflineCity &&
                            <Grid item sm={12} md={12} xs={12} container spacing={3} className="Message pd-0 leftRightMargin">
                                <Grid item sm={12} md={12} xs={12} >

                                    {IsAppointmentCreateRestriction && <h4 className="HighlightMsg"><div>New</div><b> Important: </b> To schedule an appointment, Customer has to share location, <br /> Please request location from customer via  <b className="Green">Request Customer Location Button</b> </h4>}
                                    <h3>Get Customer Location</h3>
                                    <p className="GetDetails">
                                        <div className="GetDetailstooltip">How It Works
                                            <div className="tooltiptext">

                                                <h3>Get your Customer's Location Online</h3>
                                                <h4><strong>Step 1: </strong>Click on <strong> REQUEST CUSTOMER LOCATION </strong>button and your customer will receive a</h4>
                                                <p>
                                                    <strong>1. Link on SMS - </strong> Customer can click on the link and land on share location page <br />
                                                    <strong> 2. A Share location option on whatsapp - </strong> Customer can click on the drop location button and will be redirected to share location page</p>

                                                <h4><strong>  Step 2:  </strong>Once the customer has provided the address click on the link at the end of this section to fetch the address directly to your appointment form</h4>

                                                <h5> Please try to get your customer's location online for accurate address. This helps our FOS advisor to reach the customer location on time</h5>
                                            </div>

                                        </div>
                                    </p>
                                    {
                                        IsManualTriggerAllowed && !IsReschedulingAppoinment() && !IsAppointmentCreated && NewAddress && NewAddress.OfflineCity && <button className={(AskLocationBtnDisabled || IsCustomerLocationAvailable) ? "disabled RequestCustomerBtn" : "RequestCustomerBtn"} disabled={AskLocationBtnDisabled || IsCustomerLocationAvailable} onClick={() => {
                                            AskLocation();
                                        }}>Request Customer's Location
                                        </button>
                                    }
                                    {IsManualTriggerAllowed && !IsReschedulingAppoinment() && !IsAppointmentCreated && <p className="previewCustomerBtn" onClick={() => { GetCustomerDetails() }}>You can check if your customer has shared the location from <b>here</b></p>}
                                </Grid>
                            </Grid>
                        }

                        {!IsShowFields && IsNewAppointmentAddress && <>
                            {!!Visible.Landmark && IsSrcCustomerWhatsapp === false && <Grid item sm={12} md={12} xs={12} >
                                <Autocomplete
                                    id="google-map-demo"
                                    className={RestrictLandmarkIcon && IsRequestLocationcrieteria() ? "Error" : ""}
                                    // style={{ width: 300 }}
                                    //getOptionLabel={(option) =>((option.main_text + ', ' + option.secondary_text)||'')} 
                                    getOptionLabel={(option) => (typeof option === 'string' ? option : (option.main_text + (!!option.secondary_text ? ', ' + option.secondary_text : '')))}
                                    filterOptions={(x) => x}
                                    options={options}
                                    autoComplete
                                    includeInputInList
                                    filterSelectedOptions
                                    value={(NewAddress.Landmark) || ''}
                                    disabled={disabled}
                                    onPaste={(e) => {

                                        CheckLandmarkRestriction(e)
                                    }}
                                    // onKeyPress={(e) => {
                                    //     alert("on key press")
                                    //      CheckLandmarkRestriction(e) }}
                                    onBeforeInput={(e) => {
                                        CheckLandmarkRestriction(e)
                                    }
                                    }
                                    onChange={(event, newValue) => {
                                        // alert("On change")
                                        setOptions(newValue ? [newValue, ...options] : options);
                                        setNewAddress(prevState => ({ ...prevState, Landmark: newValue && (newValue.main_text + (!!newValue.secondary_text ? ', ' + newValue.secondary_text : '')), place_id: newValue && newValue.place_id }))
                                    }}
                                    onInputChange={(event, newInputLandmarkValue) => {
                                        // alert("on Input chnage")
                                        setInputLandmarkValue(newInputLandmarkValue);
                                    }}
                                    renderInput={(params) => (
                                        //<TextField {...params} label="Society/Colony/Locality or Near By Temple/Mall/School/Hospital/Famous place*" variant="outlined" fullWidth />
                                        <TextField {...params} label={AutoSuggestValidationMessage + ' *'} variant="outlined" fullWidth />
                                    )}
                                />
                                <p className="notification" >Avoid selecting large areas selection like Sectors, Cities, etc.</p>
                                {/* {
                                    IsManualTriggerAllowed && !IsReschedulingAppoinment() && !IsAppointmentCreated && NewAddress && NewAddress.OfflineCity && <p className={IsCustomerLocationAvailable ? "GetDetails disabled" : "GetDetails"} disabled={IsCustomerLocationAvailable} onClick={() => {
                                        AskLocation();
                                    }}>Request Location from Customer<div className="GetDetailstooltip"><InfoIcon />
                                            {!IsCustomerLocationAvailable && <div className="tooltiptext">
                                                <p>A link will be sent to customer on WhatsApp and SMS to fill the above details</p>
                                            </div>
                                            }
                                        </div></p>
                                } */}
                            </Grid>
                            }
                            <Grid item sm={6} md={6} xs={12} >
                                <TextInput
                                    name="Address"
                                    // label="House No. /Building No. /Plot No. /Block No.*"
                                    label={FreeTextAddressValidationMessage + ' *'}
                                    handleChange={handleChange}
                                    value={NewAddress.Address}
                                    sm={12} md={12} xs={12}
                                    show={!!Visible.Address && IsSrcCustomerWhatsapp === false}
                                    disabled={disabled}
                                />
                                {/* {!!Visible.Address && IsSrcCustomerWhatsapp === false && <p className="notification" >Min 10 and Max 30 Characters</p>} */}
                                {!!Visible.Address && IsSrcCustomerWhatsapp === false && <p className="notification" >Warning: Customer Mobile Number and Comments should not be entered (Min 10 Characters)</p>}
                            </Grid>


                            <Grid item sm={6} md={6} xs={12} >
                                <TextInput
                                    name="NearBy"
                                    label="Directions to Reach"
                                    handleChange={handleChange}
                                    value={NewAddress.NearBy}
                                    sm={12} md={12} xs={12}
                                    show={!!Visible.NearBy && IsSrcCustomerWhatsapp === false}
                                    disabled={disabled}

                                />
                                {!!Visible.Address && IsSrcCustomerWhatsapp === false && <p className="notification" >e.g. take first left after SBI ATM</p>}
                            </Grid>
                        </>}
                        {!IsShowFields && !IsNewAppointmentAddress && <>
                            <Grid item sm={12} md={12} xs={12} >
                                <TextInput
                                    name="Address"
                                    // label="Address*"
                                    label={FreeTextAddressValidationMessage + ' *'}
                                    handleChange={handleChange}
                                    value={NewAddress.Address}
                                    sm={12} md={12} xs={12}
                                    show={!!Visible.Address && IsSrcCustomerWhatsapp === false}

                                    disabled={disabled}



                                />
                                {/* {!!Visible.Address && IsSrcCustomerWhatsapp === false && <p className="notification" >Min 10 and Max 30 Characters</p>} */}
                                {!!Visible.Address && IsSrcCustomerWhatsapp === false && <p className="notification" >(Min 10 Characters)</p>}


                            </Grid>

                            {!!Visible.Landmark && IsSrcCustomerWhatsapp === false && <Grid item sm={6} md={6} xs={12} >
                                <Autocomplete
                                    id="google-map-demo"
                                    // style={{ width: 300 }}
                                    //getOptionLabel={(option) =>((option.main_text + ', ' + option.secondary_text)||'')} 
                                    getOptionLabel={(option) => (typeof option === 'string' ? option : (option.main_text + (!!option.secondary_text ? ', ' + option.secondary_text : '')))}
                                    // filterOptions={(x) => x}
                                    options={options}
                                    autoComplete
                                    includeInputInList
                                    filterSelectedOptions
                                    value={(NewAddress.Landmark) || ''}
                                    disabled={disabled}
                                    onPaste={(e) => {

                                        CheckLandmarkRestriction(e)
                                    }}
                                    // onKeyPress={(e) => {
                                    //     alert("On key press")
                                    //     CheckLandmarkRestriction(e)
                                    // }}
                                    // onKeyDown={(e) => {

                                    //     CheckLandmarkRestriction(e)
                                    // }}
                                    onBeforeInput={(e) => {
                                        CheckLandmarkRestriction(e)
                                    }
                                    }

                                    onChange={(event, newValue) => {
                                        // alert("On chnage")
                                        setOptions(newValue ? [newValue, ...options] : options);
                                        setNewAddress(prevState => ({ ...prevState, Landmark: newValue && (newValue.main_text + (!!newValue.secondary_text ? ', ' + newValue.secondary_text : '')), place_id: newValue && newValue.place_id }))
                                    }}
                                    onInputChange={(event, newInputLandmarkValue) => {
                                        // alert("On input chnage")
                                        setInputLandmarkValue(newInputLandmarkValue);
                                    }}
                                    renderInput={(params) => (
                                        // <TextField {...params} label="Nearby popular area/Place/Street*" variant="outlined" fullWidth />
                                        <TextField {...params} label={AutoSuggestValidationMessage + ' *'} variant="outlined" fullWidth />
                                    )}
                                />
                                <p className="notification" >*Please check the auto suggestions with customer before selecting</p>
                            </Grid>
                            }
                            <Grid item sm={6} md={6} xs={12} >
                                <TextInput
                                    name="NearBy"
                                    label="Directions to Reach"
                                    handleChange={handleChange}
                                    value={NewAddress.NearBy}
                                    sm={12} md={12} xs={12}
                                    show={!!Visible.NearBy && IsSrcCustomerWhatsapp === false}
                                    disabled={disabled}

                                />
                                {!!Visible.Address && IsSrcCustomerWhatsapp === false && <p className="notification" >e.g. take first left after SBI ATM</p>}
                            </Grid>
                        </>}
                        <Grid container spacing={3}>
                            <Grid item sm={12} md={12} xs={12} >
                                {IsEditAppDetails ? '' : <p className="availableSlot pd_left">Assign to: </p>}

                                <ul className={IsEditAppDetails && !ShowNoCityServiceable ? "assignedToBox disableBox" : "assignedToBox"}>

                                    {AssignmentTypeMaster && Array.isArray(AssignmentTypeMaster) && AssignmentTypeMaster.length == 0 && ShowNoCityServiceable && !IsReschedulingAppoinment() ?
                                        // <h5 className="selectedCityMsg">The selected city can only be served by {AssignmentTypesForCity}.</h5>
                                        <h5 className="selectedCityMsg">The selected city can not be served by you.</h5>
                                        : ''}
                                    {!IsEditAppDetails && AssignmentTypeMaster && Array.isArray(AssignmentTypeMaster) && AssignmentTypeMaster.map((obj, i) => {
                                        return (
                                            <li className={obj.AssignmentId === NewAddress.AssignmentId ? 'active' : ''} onClick={(e) => {
                                                handleAssignmentChange(e, obj)
                                                // }}> {obj.AssignmentType}</li>
                                            }}> {GetAssignmentTypebyIdChips(obj.AssignmentId)}</li>
                                        )
                                    }
                                    )}
                                </ul>
                                {IsEditAppDetails ? (NewAddress.AssignmentId && NewAddress.AppointmentType
                                    && <div className="ScheduledMsg"><img alt="FOS" src={CONFIG.PUBLIC_URL + "/images/salesview/addressIcon.svg"} />
                                        {/* <p>Scheduling {fosAppointmentTypeMaster[0].value} to be fulfilled by {handleAssignmentAdvisor(NewAddress.AssignmentId)}</p> */}
                                        <p>This Appointment will be Served by {GetAssignmentTypebyId(NewAddress.AssignmentId)}</p></div>)

                                    : (!!NewAddress.AssignmentId && fosAppointmentTypeMaster && fosAppointmentTypeMaster[0] && !!fosAppointmentTypeMaster[0].value
                                        && !ShowNoCityServiceable && <div className="ScheduledMsg"><img alt="FOS" src={CONFIG.PUBLIC_URL + "/images/salesview/addressIcon.svg"} />
                                            {/* <p>Scheduling {fosAppointmentTypeMaster[0].value} to be fulfilled by {handleAssignmentAdvisor(CurrentAssignmentId)}</p> */}
                                            {/* <p>This Appointment will be Served by {GetAssignmentTypebyId(CurrentAssignmentId)}</p></div>)} */}
                                            <p>This Appointment will be Served by {GetAssignmentTypebyId(NewAddress.AssignmentId)}</p></div>)}

                            </Grid>
                        </Grid>

                        <SelectDropdown
                            name="zone"
                            label="Zone*"
                            options={zoneList}
                            fullWidth={true}
                            sm={6} md={6} xs={12}
                            disabled={disabled || !isZoneInputVisible || IsEditAppDetails}
                            labelKeyInOptions='Zone'
                            valueKeyInOptions='ZoneId'
                            handleChange={handleChange}
                            value={NewAddress.ZoneId}
                            show={isZoneInputVisible}

                        />

                    </Grid>
                    }

                    {/***********   Appointment date & available slots   **********/}
                    {!IsEditAppDetails && <AppointmentDateTimeSection

                        parentLeadId={parentLeadId}
                        NewAddress={NewAddress}
                        handleChange={handleChange}
                        disabled={disabled}
                        appointmentDateTime={appointmentDateTime}
                        setNewAddress={setNewAddress}
                        AppointmentSlotListBySlotId={AppointmentSlotListBySlotId}
                        setAppointmentSlotListBySlotId={setAppointmentSlotListBySlotId}
                        show={!!Visible.AppointMentDate && !!NewAddress.AssignmentId}
                        viewOnly={disabled}
                        IsLeadRenewal={IsLeadRenewal}
                        IsRescheduling={IsRescheduling}
                        productId={productId}
                        TotalAppointmentsByCityId={TotalAppointmentsByCityId}
                        Block8AMCityList={Block8AMCityList}
                        BlockSlotIds={BlockSlotIds}
                        isSrcReferralOrFOSApp={isSrcReferralOrFOSApp}
                    />}

                    {/***********   Other Info  **********/}
                    {!IsShowFields && <FormSectionHeading
                        icon={CONFIG.PUBLIC_URL + "/images/salesview/otherInfo.svg"}
                        title="Additional Details"
                        show={!IsSrcCustomerWhatsapp && (!!Visible.Comments || [7, 1000, 115].includes(productId))}
                    />}

                    {askPortability() && (
                        <Grid container spacing={2} className="policy-wrapper pd25">
                            <Grid item xs={12}>
                                <p className="policy-heading">What type of policy is the customer interested in?</p>
                            </Grid>

                            <Grid item sm={4} md={3} xs={12}>
                                <div className="policy-container">
                                    <div
                                        className={`policy-option ${NewAddress.HealthPolicyType === "Fresh" ? "selected" : ""}`}
                                        onClick={() => handleChange({ target: { name: "PolicyType", value: "Fresh" } })}
                                    >
                                        <input
                                            type="radio"
                                            id="fresh-policy"
                                            name="PolicyType"
                                            value="Fresh"
                                            checked={NewAddress.HealthPolicyType === "Fresh"}
                                            onChange={handleChange}
                                        />
                                        <label htmlFor="fresh-policy">Buy a fresh policy</label>
                                    </div>
                                </div>
                            </Grid>

                            <Grid item sm={8} md={8} xs={12}>
                                <div className="policy-container">
                                    <div
                                        className={`policy-option ${NewAddress.HealthPolicyType === "Portable" ? "selected" : ""}`}
                                        onClick={() => handleChange({ target: { name: "PolicyType", value: "Portable" } })}
                                    >
                                        <input
                                            type="radio"
                                            id="portable-policy"
                                            name="PolicyType"
                                            value="Portable"
                                            checked={NewAddress.HealthPolicyType === "Portable"}
                                            onChange={handleChange}
                                        />
                                        <label htmlFor="portable-policy">Port an existing policy</label>
                                    </div>

                                    {NewAddress.HealthPolicyType === "Portable" && (
                                        <LocalizationProvider dateAdapter={AdapterDayjs}>
                                            <div className="policy-date-picker">
                                                <DatePicker
                                                    name="PolicyExpiryDate"
                                                    label="Policy expiry date"
                                                    value={NewAddress.PolicyExpiryDate ? dayjs(NewAddress.PolicyExpiryDate) : null}
                                                    onChange={(newValue) => {
                                                        setNewAddress({
                                                            ...NewAddress,
                                                            PolicyExpiryDate: dayjs(newValue).format("YYYY-MM-DD"),
                                                        });
                                                    }}
                                                    minDate={dayjs()}
                                                    maxDate={dayjs().add(60, 'day')}
                                                    format="DD-MM-YYYY"
                                                    renderInput={(params) => (
                                                        <TextField {...params} variant="standard" fullWidth />
                                                    )}
                                                />
                                            </div>
                                        </LocalizationProvider>
                                    )}
                                </div>
                            </Grid>
                        </Grid>
                    )}

                    {!IsSrcCustomerWhatsapp && !IsShowFields && <Grid container spacing={3} className="pd25">
                        <SelectDropdown
                            name="Income"
                            // label={NewAddress.AssignmentId == "3" ? "Income" : "Income*"}
                            label={AdditionaldetailsMandatory() ? "Income*" : "Income"}
                            value={NewAddress.IncomeId}
                            options={Income}
                            labelKeyInOptions='Name'
                            valueKeyInOptions='Id'
                            handleChange={handleChange}
                            show={ShowAdditionalFields}
                            sm={6} md={6} xs={12}
                            disabled={disabled || IsReschedulingAppoinment()}
                        />
                        <SelectDropdown
                            name="IncomeDocs"
                            // label={NewAddress.AssignmentId == "3" ? "Income Docs" : "Income Docs*"}
                            label={AdditionaldetailsMandatory() ? "Income Docs*" : "Income Docs"}
                            value={NewAddress.IncomeDocsId}
                            options={IncomeDocs}
                            labelKeyInOptions='Name'
                            valueKeyInOptions='Id'
                            handleChange={handleChange}
                            show={ShowAdditionalFields}
                            sm={6} md={6} xs={12}
                            disabled={disabled || IsReschedulingAppoinment()}
                        />
                        <SelectDropdown
                            name="Education"
                            label={AdditionaldetailsMandatory() ? "Education*" : "Education"}
                            value={NewAddress.EducationId}
                            options={Education}
                            labelKeyInOptions='Name'
                            valueKeyInOptions='Id'
                            handleChange={handleChange}
                            show={ShowAdditionalFields}
                            sm={6} md={6} xs={12}
                            disabled={disabled || IsReschedulingAppoinment()}
                        />

                        <TextInput
                            name="Comments"
                            label={IsReschedulingAppoinment() ? "Customer Interest/Special Instructions" : "Customer Interest/Special Instructions*"}
                            handleChange={handleChange}
                            value={NewAddress.Comments}
                            sm={12} md={12} xs={12}
                            show={!!Visible.Comments}
                            disabled={disabled}
                        />

                    </Grid>
                    }

                    {/***********   Plan & supplier details   **********/}
                    {!IsShowFields && <FormSectionHeading
                        icon={CONFIG.PUBLIC_URL + "/images/salesview/planS.svg"}
                        title="Plan & supplier details"
                        show={!!Visible.Plan && !IsSrcCustomerWhatsapp}
                    />}

                    {!IsSrcCustomerWhatsapp && !IsShowFields && <Grid container spacing={3} className="pd25">


                        <SelectDropdown
                            name="InvestmentType"
                            label={IsReschedulingAppoinment() ? "Investment Type" : "Investment Type*"}
                            value={NewAddress.InvestmentTypeId}
                            options={InvestmentTypeMaster}
                            labelKeyInOptions='Name'
                            valueKeyInOptions='Id'
                            handleChange={handleChange}
                            sm={12} md={12} xs={12}
                            show={productId == 115 && !IsReschedulingAppoinment()}
                            disabled={disabled}
                        />
                        <SelectDropdown
                            name="Supplier"
                            label="Supplier"
                            value={NewAddress.Supplier}
                            options={supplierList}
                            labelKeyInOptions='SupplierDisplayName'
                            valueKeyInOptions='SupplierId'
                            handleChange={handleChange}
                            sm={5} md={5} xs={12}
                            show={!!Visible.Supplier}
                            disabled={disabled}
                        />
                        <SelectDropdown
                            name="Plan"
                            label="Plan"
                            value={NewAddress.Plan}
                            options={planList}
                            labelKeyInOptions='PlanName'
                            valueKeyInOptions='PlanId'
                            handleChange={handleChange}
                            // show={true}
                            sm={5} md={5} xs={12}
                            show={!!Visible.Plan}
                            disabled={disabled}
                        />
                        {Visible.Plan && !disabled && <Grid item sm={2} md={2} xs={12} ><button className="clearBtn" onClick={() => AddItem()}>Add new</button></Grid>}
                        {Visible.Plan && recommendationCardList && recommendationCardList.length > 0 && <Grid item sm={12} md={12} xs={12}>
                            <FOSCards
                                data={recommendationCardList}
                                RemoveItem={RemoveItem}
                                disabled={disabled}
                            />
                        </Grid>}
                    </Grid>
                    }

                </div>
                <Grid item sm={12} md={12} xs={12} >
                    <div className="spaceHight"></div>
                    <div className="footerBtn">
                        {!disabled && !IsRescheduling && !IsEditAppDetails &&
                            (IsShowSaveBtn ?
                                // <button className="editbtn" onClick={() => { SaveAppointmentData() }}
                                <button className="editbtn" onClick={() => { fnSaveAppointmentData() }}
                                    disabled={IsShowSaveBtn === false}>Schedule Appointment</button>
                                : <button className="editbtn disable" disabled>Please wait...</button>
                            )}
                        {(IsRescheduling) && <button onClick={() => fnOpenFosCancelReason(AppSubstatusMaster.find((d) => d.Id === 2004), IsCustConfirmed)} className="cancelbtn">Cancel Appointment</button>}
                        {(IsRescheduling) && <button className={!ActiveReschedulebtn ? "Reschedulebtn disable" : "Reschedulebtn"} disabled={!ActiveReschedulebtn} onClick={() => {

                            if (NewAddress && NewAddress.AppointmentType == 5) { enqueueSnackbar("Cancel the store appointment to schedule a Home appointment.", { variant: 'error', autoHideDuration: 5000 }); } else if (ActiveReschedulebtn) { fnOpenFosCancelReason(AppSubstatusMaster.find((d) => d.Id === 2005), IsCustConfirmed) }
                        }} >Reschedule Appointment</button>}

                        {(IsEditAppDetails) && <button onClick={() => fnUpdateAppointmentData()} className="Reschedulebtn">Update Appointment</button>}
                    </div>
                </Grid>
            </Grid >

            <AppointmentBookSuccessPopup
                open={openPopup === 'AppointmentBookSuccessPopup'}
                handleClose={() => setOpenPopup(null)}
                hideAnotherReferralOption={conditions.hideAnotherReferralOption}
                bookedAppointmentId={bookedAppointmentId}
                parentLeadId={parentLeadId}
                CustomerId={CustomerId}
                userId={userId}
            />
            <FosCancelReasonPopup
                AppointmentDetailsfromDB={AppointmentDetailsfromDB}
                SaveAppointmentData={SaveAppointmentData}
                handleClose={() => { setOpenFosCancelReasonPopup(false) }}
                open={OpenFosCancelReasonPopup}
                ParentLeadId={parentLeadId}
                SubStatusID={selectedSubStatusID}
                fnCloseFosPopUp={fnCloseFosPopUp}
            />

            <CommonModalPopUp
                open={OpenHistory}
                URL={url}
                className='AppointmentHistory'
                handleClose={() => {
                    setOpenHistory(false);
                }}
                parentId={parentLeadId}
                selectedSubStatus={selectedSubStatusID}
            />

            <PreviewCustomerPopUp
                open={OpenPreviewCustomer}
                NewAddress={NewAddress}
                setNewAddress={setNewAddress}
                PreviewCustomerResponse={PreviewCustomerResponse}
                handleClose={() => {
                    setOpenPreviewCustomer(false);
                }}

            />
            <SavedCustomerAddress
                open={OpenSavedCustomerAddress}
                parentLeadId={parentLeadId}
                AddressData={AddressData}
                NewAddress={NewAddress}
                setNewAddress={setNewAddress}
                handleClose={() => {
                    setOpenSavedCustomerAddress(false);
                }
                }

            />
            <AgentAvailabilityWarningPopUp
                open={showAgentAvailabilityWarningPopUp}
                HandleAgentAvailability={HandleAgentAvailability}
                setHandleAgentAvailability={setHandleAgentAvailability}
                handleClose={() => {
                    setshowAgentAvailabilityWarningPopUp(false)
                }}
            />

            <HowItWorksPopup
                open={HowItWorksOpen}
                parentLeadId={parentLeadId}
                handleClose={() => {
                    setHowItWorksOpen(false);
                }
                }

            />
        </>
    );
}