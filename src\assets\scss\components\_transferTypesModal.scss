/* Transfer Types Modal Styles */

.transfer-types-modal {

  *{
    font-family: Roboto;
  }

  .MuiDialog-paperWidthSm {
    width: 900px !important;
    max-width: 900px !important;
    box-shadow: 0px 0px 16px rgba(0, 0, 0, 0.1);
    border-radius: 12px;
    background-color: #F5F8FF;
    overflow: visible;
    padding-bottom: 0px !important;
    background: linear-gradient(180deg, #E5F0FF 0%, #FFFFFF 100%);
    height: auto;
  }

  .MuiDialogTitle-root {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 24px;

    h6 {
      padding-left: 5px;
      font-size: 20px;
      font-weight: 600;
      color: #253858E3;
    }


  }

  .open-tickets-btn {
    background-color: transparent;
    color: #0065FF;
    border: 1px solid #0065FF;
    border-radius: 4px;
    padding: 6px 12px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    display: flex;
    align-items: center;
    font-family: roboto;
    position: absolute;
    top: 16px;
    right: 67px;
    z-index: 10;
    text-transform: math-auto !important;
    letter-spacing: 0px;

    .ticket-count {
      background-color: #0076FF;
      color: white;
      border-radius: 50%;
      width: 20px;
      height: 20px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 12px;
      position: absolute;
      top: -8px;
      right: -8px;
      font-weight: bold;
    }
  }

  .form-container {
    display: flex;
    flex-direction: column;
    padding: 15px 30px 0px;
    margin-top: 0;
  }

  .form-row {
    display: flex;
    gap: 24px;
    margin-bottom: 20px;
  }

  .form-group {
    flex: 1;
    display: flex;
    flex-direction: column;
  }

  .form-label {
    font-family: Roboto;
    font-weight: 500;
    font-size: 16px;
    line-height: 40px;
    letter-spacing: 0px;
    vertical-align: middle;
    color: #303030;
  }




  .MuiOutlinedInput-notchedOutline {
    border-color: #B9CBFF !important;
    border-width: 2px !important;
  }

  .MuiOutlinedInput-input {
    font-family: Roboto !important;
    font-weight: 400 !important;
    font-size: 14px !important;
    line-height: 22px !important;
    letter-spacing: 0px;
    color: #303030 !important;
  }

  .MuiDialogContent-root {
    padding: 12px 0px 0px !important;
  }

  /* Warning section styling */
  .warning-section {
    padding: 31px 30px;
  }

  /* Overlap div styling */
  .overlap-div {
    position: relative;
    top: 0;
    left: 0;
    width: 100%;    
    background-color: #F7FBFF;
    box-shadow: 0px -2px 12px 0px #DDDDDD45;
    border-radius: 16px;
    padding: 20px 15px;
    z-index: 5;
 
  }

  .overlap-content {
    display: flex;
    flex-direction: column;
    height: 100%;

    h3.transfer-title {
      font-family: Roboto;
      font-weight: 600;
      font-size: 24px;
      line-height: 100%;
      color: #253858E3;
      margin-bottom: 16px;
    }

    .iframe-container {
      flex: 1;
      margin-bottom: 16px;
      border-radius: 8px;
      overflow: hidden;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

      iframe {
        border: none;
        width: 100%;
        height: 100%;
        min-height: 400px;
      }
    }

    .iframe-actions {
      display: flex;
      justify-content: flex-end;
      margin-top: 16px;
    }

    p {
      font-family: Roboto;
      font-weight: 400;
      font-size: 14px;
      line-height: 24px;
      color: #253858E3;
      margin-bottom: 24px;
    }

    .back-button {
      background: #0065FF;
      color: #fff;
      border: none;
      border-radius: 8px;
      padding: 10px 24px;
      cursor: pointer;
      font-family: Roboto;
      font-weight: 500;
      font-size: 14px;
      line-height: 100%;

      &:hover {
        background: #0052cc;
      }
    }
  }

  /* Warning box styling */
  .warning-box {
    display: flex;
    align-items: flex-start;
    gap: 16px;
    flex-direction: column;

    .warning-icon {
      svg {
        color: #0065FF;
        font-size: 55px;
      }
    }



    .warning-content {
      .warning-title {
        font-family: Roboto;
        font-weight: 600;
        font-size: 24px;
        line-height: 100%;
        letter-spacing: 0px;
        color: #253858E3;
      }

      .warning-text {
        font-family: Roboto;
        font-weight: 400;
        font-size: 14px;
        line-height: 38px;
        letter-spacing: 0px;
        color: #253858E3;

      }
    }
  }

  .action-buttons {
    display: flex;

    .submit-button {
      background: #0065FF;
      color: #fff;
      border: none;
      border-radius: 8px;
      padding: 14px 32px;
      cursor: pointer;
      font-family: Roboto;
      font-weight: 500;
      font-size: 14px;
      line-height: 100%;
      letter-spacing: 0px;

      &:hover {
        background: #0065DB;
      }
    }
  }

  /* Close button styling */
  .MuiIconButton-root {
    color: #9E9E9E;

    &:hover {
      background-color: rgba(0, 0, 0, 0.04);
    }
  }

  /* Ticket section styling */
  .tickets-section {
    padding: 0 24px 24px;
    background: transparent;
  }

  .tickets-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 0;
    margin-bottom: 16px;

    .header-left {
      display: flex;
      align-items: center;
    }

    .back-icon {
      margin-right: 8px;
      cursor: pointer;
      color: #333;
      font-size: 20px;
    }

    .close-icon {
      cursor: pointer;
      color: #333;
      font-size: 20px;
    }

    h3 {
      font-family: Roboto;
      font-weight: 600;
      font-size: 24px;
      line-height: 100%;
      letter-spacing: 0px;
      color: #253858E3;
    }

    .back-button {
      background-color: transparent;
      border: 1px solid #0076FF;
      color: #0076FF;
      padding: 6px 12px;
      border-radius: 4px;
      font-size: 14px;
      cursor: pointer;
      transition: all 0.2s ease;

      &:hover {
        background-color: rgba(0, 118, 255, 0.1);
      }
    }
  }

  .tickets-list {
    .ticket-item {
      background-color: #fff;
      border-radius: 8px;
      border: 1px solid  #E5F0FF;
      margin-bottom: 10px;
      overflow: hidden;
    }

    .ticket-title {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding:10px 15px;
      font-size: 14px;
      font-weight: 500;
      color: #253858E3;
      cursor: pointer;
      background-color: #fff;
    }

    .ticket-details {
      padding: 16px;
    }

    .ticket-info {
      margin-bottom: 16px;
      padding-bottom: 16px;

    }

    .info-grid {
      display: grid;
      grid-template-columns: 1fr 1fr 1fr;
      grid-template-rows: auto auto;
      gap: 2px 24px;

    }

    .info-label {
      font-size: 12px;
      color: #666;
      text-align: center;


    }
    .info-label, .info-value {
      &:nth-child(1), &:nth-child(4){
        text-align: left;
       }
       &:nth-child(3), &:nth-child(6){
        text-align: right;
       }
    }
    .info-value {
      font-size: 14px;
      font-weight: 500;
      color: #333;
      text-align: center;
    }

    .agent-info {
      display: flex;
      justify-content: space-between;
      margin-bottom: 8px;
      align-items: center;
    }

    .agent-icon {
      font-size: 20px;
      color: #253858E3;
      margin-right: 8px;

    }

    .agent-name {
      font-size: 14px;
      font-weight: 500;
      line-height: 14px;
      color: #253858E3;
      display: flex;
      align-items: center;
    }

    .message-timestamp {
      font-size: 12px;
      color:#25385899;
    }

    .message-content {
      font-size: 14px;
      color: #25385899;
      line-height: 20px;
      width:418px;
    }



    .escalate-button {
      background-color: #fff;
      color: #1962E1;
      border: 1px solid #1962E1;
      border-radius: 4px;
      padding: 5px 8px;
      font-size: 13px;
      margin-bottom: 15px;
      float: right;
      font-weight: 600;
      font-family: Roboto;
      cursor: pointer;
      transition: all 0.2s ease;

      &:hover {
        background-color: rgba(0, 118, 255, 0.1);
      }
    }
  }
}