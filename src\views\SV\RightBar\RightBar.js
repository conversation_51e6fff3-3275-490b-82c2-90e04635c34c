/* eslint-disable react-hooks/exhaustive-deps */
import React, { useEffect, useState, useCallback } from "react";
import Logs from './Logs.js'
import WhatsApp from "./WhatsApp/WhatsApp.js";
import Notes from "./Notes.js";
import CheckList from "./CheckList.js";
import Messages from "./Messages.js";
import SetCallback from "./SetCallback.js";
import User from "../../../services/user.service.js";
import { CONFIG, SV_CONFIG } from "../../../appconfig/index.js";
import ErrorBoundary from "../../../hoc/ErrorBoundary/ErrorBoundary.js";
import TicketPopUp from "./Modals/TicketPopUp.js";
import { connect, useDispatch, useSelector } from "react-redux";
import rootScopeService from "../../../services/rootScopeService.js";
import Chat from "./Chat/Chat.js";
import { setOpenRightBarMenu, setRefreshCallBackDetails, updateStateInRedux } from "../../../store/actions/SalesView/SalesView.js";
import PbTap from "./PbTap.js";
import { Badge, Drawer, Tooltip } from "@mui/material";
// import { CALL_API } from "../../../services/api.service.js";
import { useInterval } from "../Main/helpers/useInterval.js";
import { withStyles } from "@mui/styles";
import { gaEventNames, isNonProgressiveWfhCalling, pushNotification } from "../../../helpers/index.js";
import BMSInternalEmailPopup from "./Modals/BMSInternalEmailPopup.js";
import { GetBmsUrlService, IsValidUserGroup } from "../../../services/Common.js";
import Tickets from "./Tickets.js";
import { isNewTicketVisible } from "../RightBlock/RightBlock.js";
import HouseWifeInsurance from "./HouseWifeInsurance.js";
import CancelRoundedIcon from '@mui/icons-material/CancelRounded';
import ArrowBackIosIcon from '@mui/icons-material/ArrowBackIos';
import ChatBubbleOutlineOutlinedIcon from '@mui/icons-material/ChatBubbleOutlineOutlined';
import WhatsAppIcon from '@mui/icons-material/WhatsApp';
import DateRangeOutlinedIcon from '@mui/icons-material/DateRangeOutlined';
import UpdateOutlinedIcon from '@mui/icons-material/UpdateOutlined';
import PersonAddOutlinedIcon from '@mui/icons-material/PersonAddOutlined';
import DescriptionOutlinedIcon from '@mui/icons-material/DescriptionOutlined';
import ConfirmationNumberOutlinedIcon from '@mui/icons-material/ConfirmationNumberOutlined';
import PlaylistAddCheckOutlinedIcon from '@mui/icons-material/PlaylistAddCheckOutlined';
import PanToolIcon from '@mui/icons-material/PanTool';
import { gaEventTracker } from "../../../helpers";
import ModalPopup from "../../../components/Dialogs/ModalPopup";
import { IsHealthRenewalAgent } from '../../../layouts/SV/components/Sidebar/helper/sidebarHelper.js';
import CreateReferralLeadPopup from "./Modals/CreateReferralLeadPopup.js";
import { throttle } from "underscore";
import ChatNew from "./ChatNew.js";
import { checkShowNewChat } from "../../../helpers/commonHelper.js";
import CloseIcon from '@mui/icons-material/Close';
import RegionalSecondaryBanner from "./Modals/RegionalSecondaryBanner.js";
const StyledBadge = withStyles((theme) => ({
    badge: {
        right: 18,
        top: 6,
        border: `2px solid ${theme.palette.background.paper}`,
        padding: '0 4px',
        background: 'orangered',
        color: 'white'
    },
}))(Badge);

function RightBar(props) {
    let [AllLeads, PrimaryLeadId, ParentLeadId, OpenRightBarMenu, openRightbarMenuMobileOptions] = useSelector(state => {
        let { allLeads, primaryLeadId, parentLeadId, OpenRightBarMenu, openRightbarMenuMobileOptions } = state.salesview;
        return [allLeads, primaryLeadId, parentLeadId, OpenRightBarMenu, openRightbarMenuMobileOptions];
    });
    let [RefreshLead] = useSelector(({ salesview }) => [salesview.RefreshLead]);
    const ShowAssignCriticalComponents = useSelector(state => state.salesview.ShowAssignCriticalComponents);

    const [IsChatTrigger, setIsChatTrigger] = React.useState(true);
    const [style, setStyle] = React.useState({});
    const [content, setContent] = React.useState(null);
    const [open, setOpen] = React.useState(false);
    const [panel, _setPanel] = React.useState();
    const [openTicketPopUp, setopenTicketPopUp] = useState(false);
    const [PbTapIframeWidth, setPbTapIframeWidth] = useState(0);
    const [ChatIframeWidth, setChatIframeWidth] = useState(1);
    const [newMsgAvailablePbTap, setNewMsgAvailablePbTap] = useState(0);
    const [subscriptionList, setSubscriptionList] = useState([]);
    const [totalUnreadChatCount, setTotalUnreadChatCount] = useState(0);
    const [firstUnreadMessage, setFirstUnreadMessage] = useState({});
    const [showTooltip, setShowToolTip] = useState(false);
    const [currentPopup, setCurrentPopup] = useState(null);
    const [IsPbTapVisible, setIsPbTapVisible] = useState(false);
    const [IsHealthNRIGroup, setIsHealthNRIGroup] = useState(false);

    const [modal, setModal] = useState(false)
    const [BookingMessagedata, setBookingMessagedata] = useState()

    const [leadCardChatClick, setLeadCardChatClick] = useState(false);

    // Mobile
    const [showMenuOptions, setShowMenuOptions] = useState(false);
    const [showSelectedComponent, setShowSelectedComponent] = useState(false);
    const [LeadAssignedUser, setLeadAssignedUser] = useState(0);
    const [isRecentLead, setIsRecentLead] = useState(false);

    const isMobile = useSelector(state => state.common.isMobile);

    const dispatch = useDispatch();
    let pushNotification_throttled = useCallback(throttle(pushNotification, SV_CONFIG.PBTapNotifThrottle), []);
    var SMEFosGroups = IsValidUserGroup(SV_CONFIG["SMEFOSGroups"], [13]);

    useEffect(() => {
            if(AllLeads && AllLeads.length > 0) {
            const foundLead = AllLeads.find(lead => lead.LeadID === ParentLeadId);
            setLeadAssignedUser(foundLead?.LeadAssignedUser || 0);
            if (foundLead?.CreatedOn && new Date(foundLead.CreatedOn) > new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)) {
                setIsRecentLead(true);
            } else {
                setIsRecentLead(false);
            }
            }
        }, [AllLeads, ParentLeadId])

    const handlePopupOpen = (name) => () => {
        setCurrentPopup(name);
    };

    // = true  // show for all 
    // = rootScopeService.getProductId() === 7; // for term
    // = (SV_CONFIG.PbTapTL.includes(User.ManagerId) && User.ManagerName) 
    // || SV_CONFIG.PbTapTL.includes(User.UserId);
    const showNewChat = checkShowNewChat();

    const panelRef = React.useRef(panel);
    const setPanel = data => {
        panelRef.current = data;
        _setPanel(data);
    };

    const handleClose = () => {
        if (panel === "SetCallback") {
            props.setRefreshCallBackDetailsToRedux(true);
        }
        setStyle({});
        setPanel(null);
        setContent(null);
        setModal(false)
        setOpen(false);
        setPbTapIframeWidth(0);
        setChatIframeWidth(1);
        if(rootScopeService.getProductId() == 131)
            setLeadCardChatClick(false);
        // mobile
        setShowSelectedComponent(false)
    }
    const handleMenuDrawerClose = () => {
        setShowMenuOptions(false);
    }

    const handleChange = (panelOption, isOpen) => (event) => {

        setOpen(isOpen);
        let newstyle = { width: "50%" };

        if (panelOption === panel && open) {
            handleClose();
            return;
        }
        setPbTapIframeWidth(0);
        setChatIframeWidth(1);

        switch (panelOption) {
            case "Messages": {
                newstyle = { width: "85%" };
                setPanel(panelOption);
                setContent(<Messages />);
                break;
            }
            case "SetCallback": {
                newstyle = { width: "85%" };
                setPanel(panelOption);
                setContent(<SetCallback handleClose={handleClose} />);
                break;
            }
            case "Logs": {
                newstyle = { width: "90%" };
                setPanel(panelOption);
                setContent(<Logs />);
                break;
            }
            case "WhatsApp": {
                setPanel(panelOption);
                setContent(<WhatsApp />);
                break;
            }
            case "Chat": {
                newstyle = { width: "65%" };
                setPanel(panelOption);
                GetSubscription();
                setContent(<Chat subscriptionList={subscriptionList} />);
                break;
            }
            case 'ChatNew':
                newstyle = { width: "80%" };
                setPanel(panelOption);
                setChatIframeWidth(100);
                setContent(null);
                break;
            case 'PbTap':
                newstyle = { width: "30%" };
                setPanel(panelOption);
                setPbTapIframeWidth(100);
                setContent(null);
                setNewMsgAvailablePbTap(0);
                gaEventTracker(gaEventNames.PBTap, User.EmployeeId, rootScopeService.getLeadId());
                break;
            case 'Notes':
                newstyle = { width: "25%" };
                setPanel(panelOption);
                setContent(<Notes open={true} />);
                break;
            case 'Checklist':
                newstyle = { width: "30%" };
                setPanel(panelOption);
                setContent(<CheckList open={true} />);
                break;
            case 'Tickets':
                newstyle = { width: "60%" };
                setPanel(panelOption);
                setContent(<Tickets open={true} />);
                break;
            case 'HouseWife':
                newstyle = { width: "35%" };
                setPanel(panelOption);
                setContent(<HouseWifeInsurance open={true} />);
                break
            default:
                break;
        }
        if (isMobile) {
            newstyle = { width: "100%" };

            setShowSelectedComponent(true);
            setShowMenuOptions(false);
        }

        setStyle(newstyle);
    };


    const OpenfeedBackurl = () => {
        var cid = User.mToken == null ? User.UserId : User.mToken;
        window.open(SV_CONFIG["feedback"][SV_CONFIG["environment"]] + "/Landing.html#/matrix/LandingPage/" + cid, "_blank")
    }

    const CreateLead = () => {
        let CreateLeadUrl = SV_CONFIG["CreateLead"][SV_CONFIG["environment"]] + "?Referral=" + btoa(PrimaryLeadId) + "&Pid=" + rootScopeService.getProductId();
        window.open(CreateLeadUrl, "_blank");
    };

    const internalChatUrl = () => {
        let InternalChatUrl = SV_CONFIG["InternalChat"][SV_CONFIG["environment"]]
        window.open(InternalChatUrl, "internalchaturl");
    };

    const IsDisplayCreatelead = () => {
        let returnValue = true;
        let prdId = rootScopeService.getProductId();
        let intProducts = SV_CONFIG["IntProducts"][SV_CONFIG["environment"]];
        let agentproduct = []
        // return returnValue;


        // if ([13].indexOf(User.RoleId) !== -1 && [131, 101].indexOf(prdId) !== -1) {
        //     returnValue = true;
        // }

        if ([13].indexOf(User.RoleId) !== -1 && [131, 101, 115, 7, 1000].indexOf(prdId) !== -1) {
            // checking agent product
            for (let i = 0; i < User.ProductList.length; i++) {
                agentproduct.push(User.ProductList[i].ProductId);
            }
            for (let i = 0; i < agentproduct.length; i++) {
                if ([131, 101, 115, 7, 1000].indexOf(agentproduct[i]) !== -1) {
                    returnValue = true;
                    break;
                }
                else {
                    returnValue = false;
                }
            }
        }
        else if (User.RoleId === 13 && [2, 106, 118, 130].indexOf(prdId) !== -1 && !IsHealthRenewalAgent()) {
            returnValue = true;
        }
        else if (User.RoleId === 13 && intProducts.indexOf(prdId) === -1) {
            returnValue = false;
        }

        return returnValue;
    };
    const fnOpenBMS = function () {
        let ServiceLead = JSON.parse(localStorage.getItem('ServiceLead'))
        if (ServiceLead && ServiceLead.leadId && ServiceLead.callType === "Inbound") {

            const leadid = ServiceLead.leadId;

            // const requestData = { 'UserId': 8223, 'LeadId': 26159616, "Source": "Inbound" };
            GetBmsUrlService(leadid, "Inbound").then((result) => {
                if (result) {
                    var url = result.RedirectUrl;
                    window.open(url);
                }
            });

        }
    };

    const handleUserKeyPress = useCallback(event => {
        const { key, keyCode } = event;
        if (keyCode === 27) {
            handleClose();
        }
    }, []);

    useEffect(() => {
        if (OpenRightBarMenu !== null) {
            setLeadCardChatClick(true);
            handleChange(OpenRightBarMenu, true)();
            props.setOpenRightBarMenuToRedux(null);
        }
    }, [OpenRightBarMenu])

    useEffect(() => {
        if (openRightbarMenuMobileOptions) {
            if (!showMenuOptions) {
                setShowMenuOptions(true);
            }
            dispatch(updateStateInRedux({ key: 'openRightbarMenuMobileOptions', value: false }))
        }
    }, [openRightbarMenuMobileOptions])

    useEffect(() => {
        window.addEventListener("keydown", handleUserKeyPress);
        return () => {
            window.removeEventListener("keydown", handleUserKeyPress);
        };
    }, [handleUserKeyPress]);

    useEffect(() => {
        if (showTooltip) {
            const timer = setTimeout(() => {
                setShowToolTip(false);
            }, 5000);
            return () => clearTimeout(timer);
        }
    }, [showTooltip]);

    /************ PbTap ************/

    useEffect(() => {
        try {
            myGreeting();
        }
        catch (e) { }
    }, [])

    const myGreeting = () => {
        console.log("inside myGreeting");

        let myInterval = setInterval(function () {
            var onCall = window.localStorage.getItem("onCall") === "true" ? true : false;
            //console.log("myGreeting onCall", onCall)
            var bookingArr = []
            try {
                let BookingMessage = localStorage.getItem("BookingMessage");
                if (BookingMessage) {
                    bookingArr = JSON.parse(BookingMessage);
                }
            } catch (e) { console.log("myGreeting error", e) }

            // if (onCall == false && bookingArr && bookingArr.length > 0) {
            if (bookingArr && bookingArr.length > 0) {
                // if (bookingArr[0].BookingType == undefined || bookingArr[0].BookingType == 1) {
                setBookingMessagedata(bookingArr[0]);
                setModal(true);

                let newArray = [...bookingArr]
                newArray.shift();

                localStorage.setItem("BookingMessage", JSON.stringify(newArray));

                setTimeout(function () {
                    console.log("myGreeting modal", false);
                    setModal(false);
                }, 10000)
                // }
            }

        }, 20000);
    }
    useEffect(() => {
        //pbtap condition
        try {

            let usergrp = User.ProductList;
            let UserGroupList = User.UserGroupList;
            let _IsPbTapVisible = true;
            let IsHealthNRIGroup = false;
            let disablePbTapGrpIds = SV_CONFIG["disablePbTapGrpIds"];
            let disablePbTapPrdIds = SV_CONFIG["disablePbTapPrdIds"];
            let HealthNRIGroup = SV_CONFIG["HealthNRIGroups"];
            Array.isArray(usergrp) && usergrp.forEach(function (item, key) {
                if (Array.isArray(disablePbTapPrdIds) && disablePbTapPrdIds.indexOf(item.ProductId) > -1) {
                    _IsPbTapVisible = false;
                }
            });
            Array.isArray(UserGroupList) && UserGroupList.forEach(function (item, key) {
                if (Array.isArray(disablePbTapGrpIds) && disablePbTapGrpIds.indexOf(item.GroupId) > -1) {
                    _IsPbTapVisible = false;
                }
                if (Array.isArray(HealthNRIGroup) && HealthNRIGroup.indexOf(item.GroupId) > -1) {
                    IsHealthNRIGroup = true;
                }
            });

            if (rootScopeService.getProductId() === 131) {
                _IsPbTapVisible = false;
            }

            setIsPbTapVisible(_IsPbTapVisible);
            setIsHealthNRIGroup(IsHealthNRIGroup);
        } catch { }



        //listen to new-message event from PbTap iframe window
        let eventReceived = function (event) {

            if(event.data && event.data.action && event.data.action === 'totalUnreadChats'){
                setTotalUnreadChatCount(event.data.totalUnreadChats);
            }
            if(event.data && event.data.action && event.data.action === 'FirstUnreadMessage'){
                if(!SV_CONFIG['StopChatNotifSound']){
                    const audio = new Audio(CONFIG.PUBLIC_URL+"/sound/chat-notif.wav");
                    audio.play();
                }
                setFirstUnreadMessage({"customerName":event.data.customerName, "leadId":event.data.leadId, "msgContent":event.data.msgContent})
                setShowToolTip(true);
            }
            
            //myGreeting()
            if (event.data && event.data.type === 'pbtap-newMessage') {
                if (panelRef.current !== "PbTap") {
                    setNewMsgAvailablePbTap(prev => prev + 1);
                    if (SV_CONFIG.showPBTapNotification) {
                        pushNotification_throttled('New Message received on PB Tap', 'PB Tap Notification');
                    }
                }
            }
            if (event.data && event.data.type === 'pbtap-newBooking') {
                console.log("pbtap-newBooking ", event)
                try {
                    let BookingMessage = localStorage.getItem("BookingMessage");
                    let newArray = []
                    if (BookingMessage) {
                        let bookingArr = JSON.parse(BookingMessage)
                        newArray = [...bookingArr]
                    }

                    const found = newArray.find(element => element._id == event.data.data.body._id);
                    if (found == undefined) {
                        newArray.push(event.data.data.body);
                    }
                    console.log("pbtap-newBooking newArray", newArray)
                    localStorage.setItem("BookingMessage", JSON.stringify(newArray));

                    //if (([117].indexOf(rootScopeService.getProductId()) === -1) && panelRef.current !== "PbTap") handleChange("PbTap", true)();
                    //shNotification('New booking done by your team Mate', 'PB Tap: New Booking', `${CONFIG.PUBLIC_URL}/images/booking_stamping.svg`);
                }
                catch (e) { }
            }
        }
        window.addEventListener('message', eventReceived);

        return () => { window.removeEventListener("message", eventReceived) }
    }, [])

    /************ MatrixChat  ************/
    const GetSubscription = () => {

        if (User && parseInt(User.RoleId) === 13) {
            window.getSubscription && window.getSubscription(User.EmployeeId).then(function (result) {
                if (result && Array.isArray(result)) {
                    setSubscriptionList(result);
                    let unreadCount = 0;

                    if (result && result.length > 0) {
                        setIsChatTrigger(true);
                    }
                    else {
                        setIsChatTrigger(false);
                    }

                    for (let i = 0; i < result.length; i++) {
                        if (result[i].unread) {
                            unreadCount += result[i].unread;
                        }
                    }
                    setTotalUnreadChatCount(unreadCount);
                }
                else {
                    setIsChatTrigger(false);
                    gaEventTracker('GetSubscription', User.EmployeeId, rootScopeService.getLeadId());
                }

            });
        }
        else {
            setIsChatTrigger(false);
        }
    }
    useInterval(() => {
        if (!showNewChat && (IsChatTrigger || IsHealthNRIGroup)) {
            try {
                gaEventTracker('GetSubscriptionIsChatTrigger', User.EmployeeId, rootScopeService.getLeadId());
                GetSubscription();
            } catch (e) {
                console.warn("Err GETSUBSCRIPTION", e);
                try {
                    let error = JSON.stringify(e);
                    gaEventTracker('GetSubscriptionIsChatTriggerErr', error, rootScopeService.getLeadId());
                }
                catch (e) { console.log("Error in GetSubscription err conversion ", e); }
            }
        }
    }, 3000);

    useEffect(() => {
        if (!showNewChat && RefreshLead) {
            setIsChatTrigger(true);
            try {
                gaEventTracker('GetSubscriptionRefreshLead', User.EmployeeId, rootScopeService.getLeadId());
                GetSubscription();
            } catch (e) {
                console.warn("Err GETSUBSCRIPTION", e);
                try {
                    let error = JSON.stringify(e);
                    gaEventTracker('GetSubscriptionRefreshLeadException', error, rootScopeService.getLeadId());
                }
                catch (e) { console.log("Error in GetSubscriptionRefreshLeadException err conversion ", e); }
            }
        }
        // else {
        //     setIsChatTrigger(false);
        // }

    }, [RefreshLead]);

    useEffect(() => {
        if (!open && !showNewChat && (subscriptionList.length || totalUnreadChatCount)) {
            handleChange("Chat", true)();
        }
    }, [subscriptionList.length, totalUnreadChatCount])
    const handleMessagePanelWidth = () => {

        if (panel === "Messages") {
            let w = style.width === "60%" ? "85%" : "60%"
            setStyle({ width: w })
        }
    }

    const isInternalChatVisible = () => {
        let productId = 0;
        if (User.RoleId === 13) {
            var usergrp = User.ProductList;
            Array.isArray(usergrp) && usergrp.forEach(function (item, key) {
                if (SV_CONFIG["InternalChatProduct"].indexOf(item.ProductId) > -1) {
                    productId = item.ProductId;
                }
            });
        }
        return productId;
    }
    const formatAmount = function (value) {
        return value.length > 0 ?
            Number(value).toLocaleString('en-IN', {
                maximumSignificantDigits: 15,
            }) :
            '';
    };

    const viewChatMessage = (leadId) => {
        dispatch(updateStateInRedux({ key: "AgentChatLeadId", value: leadId }));
        setShowToolTip(false);
        setLeadCardChatClick(true);
        handleChange("ChatNew", true)();
    }

    const RightBarMenuOptions = <>
        {!props.leadViewOnly && !props.LeadOnlyView ?
            <div className="menu">
                <ul>
                    {!isNonProgressiveWfhCalling() && ShowAssignCriticalComponents && <li onClick={handleChange("Messages", true)} className={(panel === "Messages" ? "active" : "")}>
                        <p><ChatBubbleOutlineOutlinedIcon /></p>
                        {/* <i className="rhs-icon msg"></i> */}
                        Messages
                    </li>
                    }
                    {ShowAssignCriticalComponents && !showNewChat && 
                        <li onClick={handleChange("WhatsApp", true)}>
                            <p><WhatsAppIcon /></p>
                            {/* <i className="rhs-icon whatsapp"></i> */}
                            Whats App
                        </li>
                    }
                    {ShowAssignCriticalComponents && <li onClick={handleChange("SetCallback", true)} className={(panel === "SetCallback" ? "active" : "")}>
                        <p><DateRangeOutlinedIcon /></p>
                        {/* <i className="rhs-icon callback"></i> */}

                        Set Callbacks
                    </li>}
                    <li onClick={handleChange("Logs", true)} className={(panel === "Logs" ? "active" : "")}>
                        <p><UpdateOutlinedIcon /></p>
                        {/* <i className="rhs-icon logs"></i> */}
                        Logs
                    </li>

                    {/* {[13].indexOf(User.RoleId) !== -1 && [7, 1000].indexOf(rootScopeService.getProductId()) !== -1 && <li onClick={handleChange("HouseWife", true)} className={(panel === "Logs" ? "active" : "")}>
                        <p><CachedIcon /></p>
                        Response
                    </li>} */}
                    {
                        // (User && User.IsSOSGroup) && parseInt(User.RoleId) !== 2 &&
                        // <li onClick={fnOpenBMS}>
                        //     {/* <i className="rhs-icon logs"></i> */}
                        //     <p><img src={CONFIG.PUBLIC_URL + '/images/salesview/BMS.svg'} width="100%" height="24px" alt="BMS" /></p>
                        //     Open BMS
                        // </li>
                    }
                    {Array.isArray(SV_CONFIG["InternalChatProduct"]) && isInternalChatVisible() > 0 && <li onClick={internalChatUrl}>
                        {/* <Badge
                            badgeContent={newMsgAvailablePbTap}
                            color="secondary"
                            max={99}
                        > */}
                        <p className="handicon"><img src={CONFIG.PUBLIC_URL + '/images/salesview/InternalChat.jpeg'} alt="" width="24px" /></p>
                        {/* </Badge> */}
                        <span>
                            Internal Chat
                        </span>
                    </li>}
                    {User.IsEnableChat && !showNewChat &&
                        <li onClick={handleChange("Chat", true)}>
                            <StyledBadge
                                badgeContent={totalUnreadChatCount}
                                max={99}
                            >
                                <p className="handicon"><img src={CONFIG.PUBLIC_URL + '/images/salesview/whatsappChat.svg'} alt="" width="24px" /></p>
                            </StyledBadge>
                            <span style={{ lineHeight: 3 }}>
                                Chat
                            </span>
                        </li>
                    }
                    {showNewChat &&
                        <Tooltip
                            title={Object.keys(firstUnreadMessage).length > 0 &&
                                <>
                                    <p className="whatsappIcon"><WhatsAppIcon/> Whatsapp</p>
                                    <h4>{firstUnreadMessage.customerName}</h4>
                                    <span>Lead ID: <b> {firstUnreadMessage.leadId} </b></span>
                                    <p className="message">{firstUnreadMessage.msgContent}</p>
                                    <button onClick={()=> viewChatMessage(firstUnreadMessage.leadId)}>View Message</button> <CloseIcon className="closeButton" onClick={() => setShowToolTip(false)}/>
                                </>
                            }
                            arrow
                            placement="left"
                            open={showTooltip}
                            classes={{popper: "unreadChatNotif"}}
                        >
                            <li onClick={() => { handleChange("ChatNew", true)(); setLeadCardChatClick(false) }}>
                                    <StyledBadge
                                        badgeContent={totalUnreadChatCount}
                                        max={99}
                                    >
                                        <p className="handicon"><img src={CONFIG.PUBLIC_URL + '/images/salesview/whatsappChat.svg'} alt="" width="24px" /></p>
                                    </StyledBadge>
                                <span style={{ lineHeight: 3 }}>
                                    Chat
                                </span>
                            </li>
                        </Tooltip>
                    }
                    {(User.RoleId !== 13) &&
                        <li onClick={handlePopupOpen("BMSInternalEmailPopup")}>
                            {/* <i className="rhs-icon logs"></i> */}
                            <img src={CONFIG.PUBLIC_URL + "/images/salesview/menuicon/InternalEmail.svg"} alt="Internal Email" />
                            Internal Email
                        </li>
                    }
                    {/* {(User.RoleId !== 13) &&
                        <li onClick={OpenfeedBackurl} >
                            <i className="rhs-icon feedback"></i>
                            <br />
                            Feedback
                        </li>
                    } */}
                    {/* { User.RoleId === 13 && panel==="Messages" && <li onClick={()=>{handleMessagePanelWidth()}} className="widthResize"> Set Size</li>} */}
                    {!isMobile && User.RoleId === 13 && panel === "Messages" && <li onClick={() => { handleMessagePanelWidth() }} className="widthResize">  <img src={CONFIG.PUBLIC_URL + '/images/salesview/Dragicon.svg'} alt="dragicon" /></li>}
                    {/* </ul>

                <ul> */}
                    {IsPbTapVisible && <li onClick={handleChange("PbTap", true)}>
                        <Badge
                            badgeContent={newMsgAvailablePbTap}
                            color="secondary"
                            max={99}
                        >
                            <p className="handicon"><PanToolIcon /></p>
                        </Badge>
                        <span style={{ lineHeight: 3 }}>
                            PBTap
                        </span>
                    </li>}

                    {/* <li onClick={OpenfeedBackurl} >
                            <i className="rhs-icon feedback"></i>
                            <br />
                               Feedback
                        </li> */}
                    {ShowAssignCriticalComponents && ((SV_CONFIG["ShowCreateReferralLead"] && [7, 115, 1000, 2, 117, 130, 106].includes(rootScopeService.getProductId()) && User.RoleId === 13) ||
                        rootScopeService.getProductId() == 131) && !SMEFosGroups ?
                        <li onClick={handlePopupOpen("CreateReferralLeadPopup")} >
                            <p><PersonAddOutlinedIcon /></p>
                            Create Lead
                        </li> :
                        (ShowAssignCriticalComponents && IsDisplayCreatelead() && [12,19].includes(User.RoleId) && SV_CONFIG["CreateReferralSupervisorProducts"].includes(rootScopeService.getProductId())) ?
                        <li onClick={handlePopupOpen("CreateReferralLeadPopup")} >
                        <p><PersonAddOutlinedIcon /></p>
                        Create Lead
                        </li> :
                        (ShowAssignCriticalComponents && IsDisplayCreatelead() && [11,2].includes(User.RoleId) && SV_CONFIG["CreateReferralAdminProducts"].includes(rootScopeService.getProductId())) ?
                        <li onClick={handlePopupOpen("CreateReferralLeadPopup")} >
                        <p><PersonAddOutlinedIcon /></p>
                        Create Lead
                        </li> :
                        (ShowAssignCriticalComponents) && !SMEFosGroups && IsDisplayCreatelead() &&
                        <li onClick={CreateLead} >
                             <p><PersonAddOutlinedIcon /></p>
                             Create Lead
                        </li>
                        // ShowAssignCriticalComponents && SV_CONFIG["ShowCreateReferralLead"] && [7, 115, 1000, 2, 117].includes(rootScopeService.getProductId()) && IsDisplayCreatelead() &&
                        // <li onClick={handlePopupOpen("CreateReferralLeadPopup")} >                     
                        //     <p><PersonAddOutlinedIcon /></p>
                        //     Create Lead
                        // </li>
                    }
                    <li onClick={handleChange("Notes", true)} className={(panel === "Notes" ? "active" : "")}>
                        {/* <EventNoteIcon /> */}
                        {/* <i className="rhs-icon notes"></i> */}
                        <p><DescriptionOutlinedIcon /></p>
                        Notes
                    </li>
                    {/* <li onClick={handleChange("Tickets", true)} className={(panel === "Tickets" ? "active" : "")} > */}

                    <li onClick={isNewTicketVisible() ? handleChange("Tickets", true) : () => { setopenTicketPopUp(true) }} >
                        {/* <ConfirmationNumberIcon /> */}
                        {/* <i className="rhs-icon tickets"></i> */}
                        <p><ConfirmationNumberOutlinedIcon /></p>
                        Tickets
                    </li>
                    {(SV_CONFIG && Array.isArray(SV_CONFIG['ChecklistProductConfig']) && SV_CONFIG['ChecklistProductConfig'].includes(rootScopeService.getProductId())) &&
                        <li onClick={handleChange("Checklist", true)} className={(panel === "Checklist" ? "active" : "")}>
                            {/* <FormatListNumberedIcon /> */}
                            {/* <i className="rhs-icon checklist"></i> */}
                            <p><PlaylistAddCheckOutlinedIcon /></p>
                            Checklist
                        </li>
                    }
                </ul>
            </div>
            : 
            props.LeadOnlyView && props.LeadOnlyView == true ? 
            <div className="menu">
                <ul>
                    <li onClick={handleChange("Logs", true)} className={(panel === "Logs" ? "active" : "")}>
                        {/* <UpdateIcon /> */}
                        {/* <i className="rhs-icon logs"></i> */}
                        <p><UpdateOutlinedIcon /></p>
                        Logs
                    </li>
                </ul>
            </div>
            :
            <div className="menu">
                <ul>
                    {ShowAssignCriticalComponents && <li onClick={handleChange("SetCallback", true)} className={(panel === "SetCallback" ? "active" : "")}>
                        <p><DateRangeOutlinedIcon /></p>
                        {/* <i className="rhs-icon callback"></i> */}
                        Set Callbacks
                    </li>}
                    <li onClick={handleChange("Logs", true)} className={(panel === "Logs" ? "active" : "")}>
                        {/* <UpdateIcon /> */}
                        {/* <i className="rhs-icon logs"></i> */}
                        <p><UpdateOutlinedIcon /></p>
                        Logs
                    </li>
                </ul>
            </div>}
    </>
    const SelectedComponent = <>
        <div className="content">
            {content}
            {(IsPbTapVisible && !props.leadViewOnly) &&
                <ErrorBoundary name="PbTap">
                    <PbTap width={PbTapIframeWidth} />
                </ErrorBoundary>
            }
            {showNewChat &&
                <ErrorBoundary name="ChatNew">
                    <ChatNew width={ChatIframeWidth} leadCardChatClick={leadCardChatClick}/>
                </ErrorBoundary>
            }
        </div>
    </>

    const BookingBanner = <>
        {
            window && window.SV_CONFIG_UNCACHED && window.SV_CONFIG_UNCACHED.BookingCelebrationPopup && modal && BookingMessagedata && BookingMessagedata.BookingType == 1 && <ModalPopup className="BookingBanner" open={true}  >
                <div class="BookingPopup">
                    <div class="popUpInner">
                        <button class="closeBtn" onClick={handleClose}>
                            <img class="img-fluid" src={CONFIG.PUBLIC_URL + "/images/close.svg"} />
                        </button>
                        <div class="bgMainLogo">
                            <img class="img-fluid" src={CONFIG.PUBLIC_URL + "/images/logo.svg"} />
                        </div>
                        <div class="contentSide">
                            <img class="img-fluid" src={CONFIG.PUBLIC_URL + "/images/congrats.svg"} />
                            {BookingMessagedata && BookingMessagedata.UserName && BookingMessagedata.EmployeeId && <p class="nameMain">{BookingMessagedata.UserName} ({BookingMessagedata.EmployeeId}) for booking</p>}
                            {BookingMessagedata && !isNaN(BookingMessagedata.APE) && <h1>APE of ₹{formatAmount(
                                `${Math.round(BookingMessagedata.APE)}`
                            )}
                            </h1>}
                            <p class="subPara">with</p>
                            <p class="subPara">Sum Assured of</p>
                            {BookingMessagedata && BookingMessagedata.SumInsured && <h2>₹{BookingMessagedata.SumInsured / 10000000} Crore</h2>}
                            {BookingMessagedata && <div class="yellowBg">
                                <p>If {BookingMessagedata.UserName && BookingMessagedata.UserName.split(' ').length > 0 && BookingMessagedata.UserName.split(' ')[0]} can do it, <span>You can do it!</span></p>
                            </div>}
                        </div>
                    </div>
                </div>
            </ModalPopup >}
    </>

    const SecondaryBanner = <>
        {
            window && window.SV_CONFIG_UNCACHED && window.SV_CONFIG_UNCACHED.SecondaryBookingPopup && modal && BookingMessagedata && BookingMessagedata.BookingType == 2 && ['FOS', 'STORE', 'STORES'].includes(BookingMessagedata.Category?.toUpperCase()) && <ModalPopup open={true}  >
                <div class="SecondaryBookingPopup">
                    <div class="popUpInner">
                        <button class="closeBtn" onClick={handleClose}>
                            <img class="img-fluid" src={CONFIG.PUBLIC_URL + "/images/close.svg"} />
                        </button>
                        <div class="bgMainLogo">
                            <img class="img-fluid" src={CONFIG.PUBLIC_URL + "/images/FosLogo.svg"} />
                        </div>
                        <div class="contentSide">
                            <img class="img-fluid" src={CONFIG.PUBLIC_URL + "/images/congrats.svg"} />
                            {BookingMessagedata && <p class="nameMain">{BookingMessagedata.UserName && BookingMessagedata.UserName.split(' ').length > 0 && BookingMessagedata.UserName.split(' ')[0]} for earning secondary</p>}
                            {BookingMessagedata && !isNaN(BookingMessagedata.APE) && <h2>APE of ₹{formatAmount(
                                `${Math.round(BookingMessagedata.APE)}`
                            )}</h2>}
                            <p class="subPara">on an appointment created by you</p>
                            {BookingMessagedata && BookingMessagedata.LEADID && <p class="subPara1">Lead ID: {BookingMessagedata.LEADID}</p>}
                            <div class="yellowBg">
                                <img src={CONFIG.PUBLIC_URL + "/images/yellowBG.svg"} />
                                <p>Keep creating more FOS appointments</p>
                            </div>
                        </div>
                    </div>
                </div>
            </ModalPopup >}
    </>


    return (
        <>
            {(!isMobile)
                ?
                <div>
                    <div className="fade" onClick={open ? handleClose : null} style={open ? { display: "Block" } : {}}></div>
                    <div className="rightmenu" style={style}>
                        {RightBarMenuOptions}
                        {SelectedComponent}
                        {BookingBanner}
                        {SecondaryBanner}

                        {SV_CONFIG && !SV_CONFIG.DisableRegSecBanner && modal && 
                        BookingMessagedata && BookingMessagedata.BookingType === '2' && 
                        BookingMessagedata.Category?.toLowerCase() === 'transfer' && (
                        <RegionalSecondaryBanner
                            name={BookingMessagedata.UserName}
                            amount={BookingMessagedata.APE}
                            leadId={BookingMessagedata.LeadId}
                            isOpen={modal}
                            handleClose={()=> setModal(false)}
                        />
                        )}

                    </div>
                </div >
                :
                <>
                    <Drawer anchor='bottom' open={showMenuOptions} onClose={handleMenuDrawerClose} className="RightBar-mobileview">
                        <p class="text-right" onClick={handleMenuDrawerClose}><CancelRoundedIcon /></p>
                        {RightBarMenuOptions}
                    </Drawer>
                    <Drawer
                        anchor='right'
                        open={showSelectedComponent}
                        onClose={handleClose}
                        className="logPopup-Mobileview"
                        ModalProps={{
                            keepMounted: true // for pbtap to remain open
                        }}
                    >
                        <span class="closeArrow" onClick={handleClose}><ArrowBackIosIcon /></span>
                        {SelectedComponent}
                    </Drawer>
                </>
            }

            <ErrorBoundary name="BMSInternalEmailPopup">
                <BMSInternalEmailPopup open={currentPopup === "BMSInternalEmailPopup"} handleClose={() => { setCurrentPopup(null) }} />
            </ErrorBoundary>
            <ErrorBoundary name="CreateReferralLeadPopup">
                <CreateReferralLeadPopup open={currentPopup === "CreateReferralLeadPopup"} parentId={ParentLeadId} LeadAssignedUser={LeadAssignedUser} isRecentLead={isRecentLead} handleClose={() => { setCurrentPopup(null) }} />
            </ErrorBoundary>
            <ErrorBoundary name="OpenTicketUrl"><TicketPopUp open={openTicketPopUp} handleClose={() => { setopenTicketPopUp(false) }} /></ErrorBoundary>
        </>

    )

}


const mapStateToProps = state => {
    return {

    };
};

const mapDispatchToProps = dispatch => {
    return {
        setOpenRightBarMenuToRedux: (value) => dispatch(setOpenRightBarMenu({ OpenRightBarMenu: value })),
        setRefreshCallBackDetailsToRedux: (value) => dispatch(setRefreshCallBackDetails({ RefreshCallBackDetails: value }))
    };
};
export default connect(mapStateToProps, mapDispatchToProps)(RightBar);
