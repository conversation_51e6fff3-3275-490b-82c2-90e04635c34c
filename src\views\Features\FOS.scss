.MuiInputLabel-outlined {
  z-index: 1;
  transform: translate(14px, 9px) scale(1) !important;
  pointer-events: none;
  max-width: calc(100% - 45px);
}

.MuiInputLabel-shrink {
  max-width: calc(133% - 32px);
}

.MuiInputLabel-outlined.MuiInputLabel-shrink {
  transform: translate(12px, -8px) scale(0.75) !important;
  background-color: #fff;
}

.Error {
  .MuiOutlinedInput-notchedOutline {
    border-color: #E44A4A !important;
    border-width: 1.5px;
  }

  .MuiOutlinedInput-root.Mui-focused .MuiOutlinedInput-notchedOutline {
    border-color: #E44A4A !important;
  }
}

.heading {
  width: 100%;
  background: #f2f7ff 0% 0% no-repeat padding-box;
  display: flex;
  justify-content: space-between;
  padding: 6px 12px;
  margin-bottom: 16px;
  margin-top: 16px;

  .IconTitle {
    display: flex;
  }

  h3 {
    font: normal normal normal 14px/19px Roboto;
    letter-spacing: 0px;
    color: #2e2e2e;
    opacity: 1;
    padding: 6px 15px;
    display: flex;
    align-items: center;
  }
}

.pd25 {
  padding: 15px 25px 0px;
}

.tooltip {
  position: relative;
  display: flex;
  align-items: center;
  color: #6fbdf7;
}

.AlertMsg {
  color: #e06666;
  padding: 10px 30px;
  box-shadow: 0px 6px 16px #3469cb29;
  border-radius: 8px;
  opacity: 0;
  text-align: justify;
  margin: 0px 15px 20px;
  font: normal normal normal 12px/20px Roboto;
  display: block;
  visibility: hidden;
  position: absolute;
  width: 410px;
  background-color: #fff;
  z-index: 99;
  transition: opacity .6s;

  h3 {
    font: normal normal normal 14px/20px Roboto;
    margin-bottom: 7px;
    padding: 6px 0px;
  }
}

.tooltip-left {
  top: -5px;
  bottom: auto;
  right: 100%;
}

.tooltip .AlertMsg::after {
  content: "";
  position: absolute;
  top: 10%;
  left: 100%;
  margin-top: -5px;
  border-width: 5px;
  border-style: solid;
  border-color: transparent transparent transparent #fff;
}

.tooltip:hover .AlertMsg {
  visibility: visible;
  opacity: 1;
}

.notification {
  font: normal normal normal 12px/20px Roboto;
  color: #808080;
  display: inline-block;
}

.GetDetails {
  letter-spacing: 0px;
  opacity: 1;
  text-align: left;
  margin-left: 5px;
  width: auto;
  color: #0065FF;
  font-size: 14px;
  font-weight: 500;
  text-decoration: underline;
  display: flex;
  cursor: pointer;
  align-items: center;
  justify-content: center;


  .GetDetailstooltip {
    position: relative;
    display: flex;

    .tooltiptext {
      visibility: hidden;
      width: 470px;
      background-color: #fff;
      text-align: left;
      border-radius: 6px;
      padding: 10px 15px;
      box-shadow: 0px 6px 16px #3469cb29;
      position: absolute;
      z-index: 9999;
      top: 110%;
      right: -212%;
      margin-right: 0px;

      h3 {
        margin-bottom: 15px;
        margin-top: 3px;
      }

      h4 {
        color: #7d7d82;
        margin: 8px 0px;
        font-family: roboto;
        font-weight: 400;

        strong {
          color: #253858;
        }
      }

      h5 {
        color: #253858;
        font-style: italic;
        font-size: 14px;
        font-weight: 400;
      }

      p {
        text-align: left;
        font: normal normal 500 13px/20px Roboto;
        letter-spacing: 0px;
        color: #7d7d82;
        opacity: 1;
        padding: 3px 10px;

        strong {
          color: #253858;
        }
      }

      &::after {
        content: "";
        position: absolute;
        bottom: 100%;
        right: 42%;
        margin-left: -5px;
        border-width: 5px;
        border-style: solid;
        border-color: transparent transparent white transparent;
      }
    }

    &:hover .tooltiptext {
      visibility: visible;
    }
  }

}

.disabled {
  color: #25385873;
  opacity: 0.3;
  cursor: default !important;

}

.continueBtn {
  display: flex;
  margin: auto;
  width: 240px;
  justify-content: center;
  background: #0065ff;
  border-radius: 8px;
  color: #fff;
  padding: 15px;
  font: normal normal 12px Roboto;
  letter-spacing: 0.17px;
  border: none;
  outline: none;
  margin-bottom: 20px;
  cursor: pointer;
}

.continueBtn.disable {
  color: #fff;
  opacity: 0.5;
  position: relative;
}

.saveButton {
  background: #0065ff;
  border-radius: 8px;
  color: #fff;
  padding: 15px 65px;
  font: normal normal 12px Roboto;
  letter-spacing: 0.17px;
  border: none;
  outline: none;
  float: left;
  margin-right: 18px;
}

.scheduledDateTime {
  text-align: left;
  font: normal normal 600 12px/24px Roboto;
  letter-spacing: 0.19px;
  color: #253858;
  opacity: 1;

  span {
    font: normal normal 500 14px/25px Roboto;
  }
}

.availableSlot {
  text-align: left;
  font: normal normal 600 12px/24px Roboto;
  letter-spacing: 0.19px;
  color: #253858;
  opacity: 1;
}

.TimeSlotCalendar {
  width: 100%;
  display: flex;
  list-style-type: none;
  justify-content: flex-start;
  overflow-x: auto;

  .disable {
    color: #25385873;
    opacity: 0.5;
    position: relative;
  }

  li {
    color: #000000;
    background-color: #fff;
    border: 1px solid #5e6c84;
    border-radius: 12px;
    font: normal normal 14px/24px Roboto;
    text-align: center;
    width: 71px;
    height: 71px;
    display: flex;
    justify-content: center;
    flex-direction: column;
    cursor: pointer;
    flex: none;
    margin: 5px;

    h4 {
      font: normal bold 18px/24px Roboto;
      text-align: center;
    }
  }

  .active {
    box-shadow: #00000029 0px 3px 12px;
    background: #3886ff;
    border: none;
    color: #fff;
  }
}

.TimeSlotCalendar::-webkit-scrollbar,
.TimeSlotCalendar::-webkit-scrollbar-thumb {
  width: 1px;
  height: 6px;
  border-radius: 13px;
  background-clip: padding-box;
  border: 1px solid transparent;
  color: #d3d2d2;
}

.TimeSlotCalendar::-webkit-scrollbar-thumb {
  box-shadow: inset 0 0 0 10px;

  :hover {
    color: rgba(0, 0, 0, 0.3);
  }
}

.timeSlot {
  display: flex;
  width: 100%;
  list-style-type: none;
  flex-wrap: wrap;
  justify-content: flex-start;
  margin-top: 18px;

  li {
    width: 23%;
    margin: 5px 6px 38px;
    height: 40px;

    p {
      padding: 10px;
      background: #ffffff 0% 0% no-repeat padding-box;
      box-shadow: 0px 6px 16px #3469cb29;
      border-radius: 8px;
      opacity: 1;
      text-align: center;
      font: normal normal normal 14px/19px Roboto;
      cursor: pointer;
      color: #253858;
    }

    .BookingDetails {
      opacity: 1;
      display: flex;
      flex-wrap: wrap;
      flex-direction: column;
      margin-top: 0px;
      border: 1px solid #D9DCE2;
      border-radius: 4px;
      text-align: center;
      border-top: none;
      padding: 4px;
      font: normal normal 600 12px/16px Roboto;
      letter-spacing: 0px;
      margin: 0px 12px;
    }

    .green {
      color: #097447;
      opacity: 0.8;
    }

    .orange {
      color: #D07214;
      opacity: 0.8;
    }

    .red {
      color: #E6151D !important;

    }
  }

  span {
    display: none;
  }

  .active {
    border: 1px solid #0065ff !important;
    color: #0065ff;
  }

  .disable {
    color: #25385873;
    opacity: 0.5;
    position: relative;

    span {
      font-size: 12px;
      display: flex;
      position: absolute;
      align-items: center;
      right: 25px;
      top: 9px;
      color: #f190a7;

      svg {
        width: 14px;
        margin-right: 5px;
      }
    }
  }
}

.mt-0 {
  margin-top: 0px !important;
}

// .MuiInputLabel-outlined{
//   transform: translate(14px, 12px) scale(1) !important;
// }
.MuiAutocomplete-inputRoot[class*="MuiOutlinedInput-root"] {
  padding: 2px !important;
}

.cityBox {
  .MuiAutocomplete-inputRoot[class*="MuiOutlinedInput-root"] .MuiAutocomplete-input {
    padding: 4.5px 4px !important;
  }

  .MuiOutlinedInput-root {
    position: relative;
    border-radius: 4px;
    height: 43px !important;
  }

  .MuiOutlinedInput-notchedOutline {
    border-color: #808080 !important;
  }

  .MuiFormLabel-root {
    color: #808080 !important;
    letter-spacing: 0.22px !important;
    top: 0px !important;
  }
}

.pincodeBox {
  .MuiAutocomplete-inputRoot[class*="MuiOutlinedInput-root"] .MuiAutocomplete-input {
    padding: 4.5px 4px !important;
  }

  .MuiOutlinedInput-root {
    position: relative;
    border-radius: 4px;
    height: 43px !important;
  }

  .MuiOutlinedInput-notchedOutline {
    border-color: #808080 !important;
  }

  .MuiFormLabel-root {
    color: #808080 !important;
    letter-spacing: 0.22px !important;
    top: 0px !important;
  }

  .LoadingAdorn {
    margin-right: 10px;
  }
}

.clearBtn {
  text-align: center;
  border: 1px solid #0065ff;
  border-radius: 8px;
  color: #0065ff;
  font: normal normal 12px/21px Roboto;
  padding: 10px;
  background-color: #fff;
  outline: none;
  width: 100%;
}

.recomend {
  .addnew {
    margin: -15px 20px 0 0;
  }
}

.list-items {
  max-height: 418px;
  overflow: auto;
  width: 99%;
  padding: 0 1% 0 0;
}

.recomend-block {
  display: block;

  .customer-selection {
    display: inline-block;
    background: #c1f6ff;
    display: inline-block;
    text-transform: uppercase;
    padding: 5px 10px;
    font-size: 10px;
    font-weight: 600;
    color: #00b8d9;
    border-radius: 14px;
  }

  .Other-selection {
    display: inline-block;
    background: #dedaf5;
    display: inline-block;
    text-transform: uppercase;
    padding: 5px 10px;
    font-size: 10px;
    font-weight: 600;
    color: #a79ce3;
    border-radius: 14px;
  }

  .row {
    display: flex;
    align-items: flex-end;
    border-bottom: 1px solid #d5d5d5;
    padding: 0 0 20px;
    margin-bottom: 20px;

    .inner-block {
      width: 20%;

      &.plan {
        color: #253858;
        font-size: 14px;
        line-height: 21px;
        font-weight: 600;
        margin-right: calc(20% - 90px);
      }

      &.select {
        width: 20%;

        p.label {
          color: #808080;
          font-size: 12px;
        }

        p.date {
          color: #253858;
          font-size: 14px;
          font-weight: 600;
        }
      }

      &.interest {
        margin-right: 5%;
        width: 35%;

        p.heading {
          color: #808080;
          font-weight: 12px;
          font-weight: 600;
          text-align: center;
          padding-bottom: 5px;
        }

        .grey-block {
          background: #f4f4f4;
          border-radius: 16px;
          display: block;
          padding: 8px 0;
          text-align: center;

          .icon {
            width: 40px;
            height: 32px;
            background: url("/public/images/face.svg") no-repeat;
            background-size: 100%;
            margin-right: 5px;
            display: inline-block;
            cursor: pointer;
            background-position: 0 0;

            &.one {
              background-position: 0px 0px;
              filter: grayscale(100%);

              &.active {
                filter: grayscale(0%);
              }
            }

            &.two {
              background-position: 0 -42px;
              filter: grayscale(100%);

              &.active {
                filter: grayscale(0%);
              }
            }

            &.three {
              background-position: 0 -82px;
              filter: grayscale(100%);

              &.active {
                filter: grayscale(0%);
              }
            }

            &.four {
              background-position: 0 -120px;
              filter: grayscale(100%);

              &.active {
                filter: grayscale(0%);
              }
            }

            &.five {
              background-position: 0 -159px;
              filter: grayscale(100%);

              &.active {
                filter: grayscale(0%);
              }
            }
          }
        }
      }

      &.booking {
        p.heading {
          color: #808080;
          font-size: 12px;
          text-align: center;
          padding: 0;
          margin: 0;

          span.premium {
            color: #0065ff;
            font-size: 16px;
            font-weight: 600;
            padding-left: 7px;
            display: inline-block;
            line-height: 0;
          }
        }

        a.book {
          color: #0065ff;
          display: block;
          padding: 8px;
          font-weight: 600;
          font-size: 12px;
          text-align: center;
          border: 1px solid #0065ff;
          border-radius: 8px;
          margin-top: 10px;
        }
      }
    }
  }
}

.reassigned-table {
  max-height: 250px;
  overflow: auto;
}

.AddSupliertable {
  max-height: 250px;
  overflow: auto;
  box-shadow: none;

  .MuiTableHead-root {
    background-color: transparent;

    .Fosheader {
      font-size: 12px !important;
      font-weight: 600;
      padding: 5px 12px;
      background-color: #f2f7ff;
      color: #808080;
      border: none;
      position: relative;
      width: auto;
      z-index: 0;

      &:first-child {
        border-radius: 12px 0px 0px 12px;
      }

      &:last-child {
        border-radius: 0px 12px 12px 0px;
      }
    }
  }

  .MuiTableCell-body {
    padding: 5px 12px;
    line-height: 21px;
  }

  .red {
    color: #bf5959;
  }
}

.Fosheader {
  font-size: 12px !important;
  font-weight: 600;
}

.mt12 {
  margin-top: 12px !important;
}

.datedisble {
  input {
    color: #253858 !important;
  }
}

#root {
  overflow-y: scroll;
  overflow-x: hidden;
  // height: 500px;
}

#root::-webkit-scrollbar,
#root::-webkit-scrollbar-thumb {
  width: 0px !important;
  // border-radius: 13px;
  background-clip: padding-box;
  border: 0px solid transparent !important;
}

#root::-webkit-scrollbar-thumb {
  box-shadow: none !important;

  :hover {
    color: rgba(0, 0, 0, 0.3);
  }
}

.AppointmentBookSuccessPopup {
  .MuiDialog-paperWidthSm {
    max-width: 1250px;
    box-shadow: 0px 0px 16px #00000014;
  }

  .goToHomeBtn {
    cursor: pointer;
    color: #253858;
    border-radius: 8px;
    background-color: rgb(233, 230, 230);
    padding: 14px 42px;
    font: normal normal 12px Roboto;
    letter-spacing: 0.17px;
    border: none;
    outline: none;
    margin-top: 13px;
    width: 100%;
  }

  .goToAppointmentBtn {
    cursor: pointer;
    color: #fff;
    border-radius: 8px;
    background-color: #0065ff;
    padding: 14px 42px;
    font: normal normal 12px Roboto;
    letter-spacing: 0.17px;
    border: none;
    outline: none;
    margin-top: 13px;
    width: 100%;
  }
}

.appointmentAddress {
  background: #f2f7ff;
  border-radius: 8px;
  margin: 10px 20px 20px;
  padding: 10px 15px 20px;
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-end;
  position: relative;

  label {
    font: normal normal 600 13px Roboto;
    color: #2e2e2e;
  }

  p {
    font: normal normal 13px/16px Roboto;
    color: #2e2e2e;
    margin-top: 4px;
    word-break: break-word;
  }

  .editbtn {
    background-color: #0065ff;
    outline: none;
    border: none;
    padding: 5px 11px;
    color: #fff;
    border-radius: 6px;
    font: normal normal 12px Roboto;
    cursor: pointer;
    position: absolute;
    right: 10px;
  }
}

.appointmentStatus {
  background: #f2f7ff;
  border-radius: 8px;
  margin: 5px 20px 20px;
  padding: 20px 15px;
  display: flex;
  align-items: center;

  .leftSide {
    padding-left: 10px;
    width: 66%;

    h3 {
      font-family: "Roboto";
      font-style: normal;
      font-weight: 600;
      font-size: 16px;
      line-height: 19px;
      color: #2e2e2e;
      margin-bottom: 5px;
    }

    p {
      font-family: "Roboto";
      font-style: normal;
      font-weight: 400;
      font-size: 12px;
      line-height: 14px;
      color: #2e2e2e;
    }
  }

  .actionBtn {
    width: 25%;
    box-shadow: none;
    height: 38px;

    .MuiButtonGroup-groupedContainedPrimary:not(:last-child) {
      width: 100%;
      border-radius: 8px 0px 0px 8px;
      border-color: #0155d8;
      background-color: #0065ff;
      box-shadow: none;
    }

    .MuiButtonGroup-groupedHorizontal:not(:first-child) {
      border-radius: 0px 8px 8px 0px;
      box-shadow: none;
    }
  }

  .actionItem {
    z-index: 999;

    .MuiListItem-root {
      color: #253858;
      font: normal normal 14px/25px Roboto;
    }

    .MuiListItem-root.Mui-selected,
    .MuiListItem-root.Mui-selected:hover {
      border-bottom: 1px solid rgba(37, 56, 88, 0.16);
      border-top: 1px solid rgba(37, 56, 88, 0.16);
      background-color: #fff;
    }
  }
}

.FosCancelReasonPopup {
  width: 510px;

  .MuiFormControlLabel-root {
    margin-bottom: 23px;
  }

  .MuiRadio-root {
    margin-right: 15px;
    position: relative;
    top: 0;
    right: 0;
    background-color: #fff;

    svg {
      font-size: 1.5rem;
    }
  }

  .MuiFormControlLabel-label {
    font: normal normal 14px/20px Roboto;
    letter-spacing: 0.25px;
    color: #0f172a;
  }
}

.blueBg {
  background: #f2f7ff;
  border-radius: 8px;
  padding: 15px;
  width: 100%;
}

.Fosheader {
  font-weight: 600;
  background-color: #fff;
  padding-bottom: 12px;
  width: 100%;
  z-index: 9;

  h6 {
    text-align: left;
    font: normal normal 18px/24px Roboto;
    letter-spacing: 0px;
    color: #2e2e2e;
    font-weight: 600;
    padding: 12px 20px 0px;
  }

  .viewHistoryBtn {
    outline: none;
    border: none;
    font: normal normal 11px/12px Roboto;
    background: #deebff;
    border-radius: 8px;
    padding: 8px 15px;
    margin: 6px auto;
    cursor: pointer;

    left: 243px;
    color: #0065ff;
  }

  .savechangeBtn {
    border: none;
    position: absolute;
    top: 7px;
    background: #0065ff;
    border-radius: 8px;
    right: 30px;
    outline: none;
    font: normal normal 14px/16px Roboto;
    color: #ffffff;
    padding: 10px 15px;
    cursor: pointer;
  }
}

.footerBtn {
  position: fixed;
  bottom: 0;
  width: 100%;
  z-index: 99;
  background-color: #fff;
  display: flex;
  justify-content: center;

  button {
    padding: 11px;
    width: 180px;
    margin: 5px 5px 5px;
    outline: none;
    border: none;
    color: #fff;
    border-radius: 6px;
    font: normal normal 12px Roboto;
    cursor: pointer;
  }

  .editbtn {
    background-color: #0065ff;
  }

  .cancelbtn {
    background-color: #e06666;
  }

  .Reschedulebtn {
    background-color: #fff;
    border: 1px solid #0065ff;
    font-weight: 600;
    color: #0065ff;
  }

  .disable {
    color: #25385873;
    opacity: 0.5;
    cursor: default;
  }
}

.scrollbar {
  height: 100%;
  overflow-y: auto;
  overflow-x: hidden;
  padding-bottom: 20px;
  width: 100%;
}

.spaceHight {
  height: 60px;
}

.assignedToBox {
  display: flex;
  letter-spacing: 0px;
  color: #253858;
  list-style-type: none;
  margin: 6px 11px 5px;
  cursor: pointer;

  li {
    background: #ffffff 0% 0% no-repeat padding-box;
    border: 1px solid #5e6c84;
    border-radius: 40px;
    font: normal normal 600 12px/14px Roboto;
    letter-spacing: 0px;
    color: #253858;
    margin-right: 20px;
    padding: 10px 20px;
    height: 34px;

  }

  // button {
  //   background: #ffffff 0% 0% no-repeat padding-box;
  //   border: 1px solid #5e6c84;
  //   border-radius: 40px;
  //   font: normal normal 600 12px/14px Roboto;
  //   letter-spacing: 0px;
  //   color: #253858;
  //   margin-right: 20px;
  //   padding: 10px 20px;
  //   height: 34px;

  // }
  .active {
    color: #0065ff;
    background: #f2f7ff 0% 0% no-repeat padding-box;
    border: 1px solid #0065ff;
  }
}

.selectedCityMsg {
  color: #d72828;
  font-weight: 600;
  background: #ffeaef 0% 0% no-repeat padding-box;
  border: 1px solid #ffafaf;
  padding: 10px;
  margin-top: 6px;
  border-radius: 4px;
  width: 100%;
}

.disableBox {
  opacity: 0.2;
  cursor: default;

  .active {
    color: #000;
    background: #828a99 0% 0% no-repeat padding-box;
    border: 1px solid #808080;
  }
}

.pd_left {
  padding-left: 15px;
}

.ScheduledMsg {
  background-color: #FFF0D5;
  padding: 12px;
  border-radius: 8px;
  text-align: left;
  font: normal normal 600 12px/16px Roboto;
  letter-spacing: 0px;
  color: #BE851E;
  margin: 17px 10px 5px;
  display: flex;

  img {
    margin-right: 5px;
  }
}


  .GetPrioritySection{
   background: #002C4F;
  padding: 12px;
  border-radius: 8px;
  text-align: center;
  font: normal normal 600 12px/16px Roboto;
  letter-spacing: 0px;
  color: white;
  margin: 17px 10px 5px;
  h3{
    margin-bottom: 5px;
  }

  img {
    margin-right: 5px;
  }
 
}

.mt-4 {
  margin-top: 50px;
}

.mt-3 {
  margin-top: 25px;
}

@media only screen and (min-width: 361px) and (max-width: 700px) {
  .timeSlot {
    li {
      width: 46%;
    }
  }

  .AlertMsg {
    width: 290px;
  }

  .tooltip .AlertMsg::after {
    top: 7%;
  }

  .FosCancelReasonPopup {
    width: 100%;
  }

  .GetCustomerLocation {
    img {
      display: none;
    }

    .buttonLayout {
      flex-wrap: wrap;
      justify-content: center;

      button {
        margin: 15px 8px 0px 8px !important;

        &:last-child {
          border: none;
        }
      }
    }
  }
}

@media only screen and (min-width:320px)and (max-width:360px) {
  .AlertMsg {
    width: 260px;
  }

  .tooltip .AlertMsg::after {
    top: 5%;
  }

  .timeSlot {
    li {
      width: 45%;
    }
  }

  .FosCancelReasonPopup {
    width: 100%;
  }

  .GetCustomerLocation {
    img {
      display: none;
    }

    .buttonLayout {
      flex-wrap: wrap;
      justify-content: center;

      button {
        margin: 15px 6px 0px 6px !important;

        &:last-child {
          border: none;
        }
      }
    }
  }
}

.crossSellMsg {
  font: normal normal normal 12px/20px Roboto;
  color: #808080;
  margin: 0px 20px;
}

.gender {
  padding-top: 0px;
  padding-bottom: 0px;
  display: flex;
  justify-content: space-between;

  .grid-item {
    padding-bottom: 0px;
    padding-top: 10px;
  }

  .Label {
    margin-right: 10px;
  }


}

.leftRightMargin {
  margin: 12px 0px 12px 25px !important;
}

.RequestCustomerBtn {
  background: rgb(238, 208, 86);
  background: linear-gradient(176deg, rgba(238, 208, 86, 1) 18%, rgb(171 128 37) 91%) !important;
  box-shadow: 3px 3px 1px #dcdcdc;
  height: 42px !important;
  border: none !important;
  color: #fff !important;
  margin: 10px auto 0px;
  text-decoration: none !important;
  padding: 0px 12px;
  height: auto;
  font: 500 14px / 17px Roboto;
  border-radius: 8px;
  cursor: pointer;
}

.previewCustomerBtn {
  padding: 0px 12px;
  font: normal normal bold 12px/17px Roboto;
  letter-spacing: 0px;
  cursor: pointer;
  display: block;
  position: relative;
  text-align: center;
  margin: 8px auto 8px;


  b {
    text-decoration: underline;
    color: #0065FF !important;
    font-size: 14px;
  }
}

.Message {
  background-color: #95d1fd2b;
  border: 1px solid #95d1fd63;
  border-radius: 6px;
  color: #828283;
  font: normal normal 500 12px/20px Roboto;
  text-align: center;

  b {
    color: #253858;
  }

  h3 {
    text-align: center;
    margin-bottom: 10px;
    font-size: 18px;
    color: #253858 !important;
    margin-top: 10px;
  }
}

.PreviewCustomerPopup {
  .popupWrapper {
    width: 450px;

    p {
      text-align: left;
      font: normal normal normal 12px/16px Roboto;
      letter-spacing: 0px;
      color: #808080;
      opacity: 1;
      margin-top: 10px;
    }

    h3 {
      text-align: left;
      font: normal normal normal 14px/19px Roboto;
      letter-spacing: 0px;
      color: #253858;
      opacity: 1;
      margin-bottom: 15px;
    }

    .fileInputBtn {
      cursor: pointer;
      color: #fff;
      border-radius: 8px;
      background-color: #0065ff;
      width: 180px;
      height: 46px;
      text-align: center;
      font: normal normal 500 14px/21px Roboto;
      letter-spacing: 0.2px;
      color: #FFFFFF;
      border: none;
      outline: none;
      margin-top: 15px;
    }
  }
}

.HighlightMsg {
  // display: flex;
  // align-items: center;
  text-align: center;
  justify-content: center;
  font-size: 14px;

  div {
    padding: 0px 5px 0px 2px;
    display: inline-block;
    background-color: #f01818;
    color: #fff;
    border-radius: 1px 10px 10px 1px;
    border-left: 4px solid #ba1c1c;
    font-size: 11px;
    font-weight: 500;
    margin-right: 5px;
  }

  .Green {
    color: green;
  }
}

.SaveAddress {
  background-color: #0046BF;
  font-family: Roboto;
  font-size: 13px;
  position: relative;
  font-weight: 400;
  line-height: 15.23px;
  display: flex;
  justify-content: space-between;
  border-radius: 4px;
  padding: 12px 18px;
  width: 100%;
  color: #FFFFFF;
  align-items: center;
  margin-bottom: 15px;

  img {
    position: absolute;
    left: -11px;
    top: -11px;
  }

  button {
    background-color: transparent;
    border: none;
    font-family: 'Roboto';
    font-size: 12px;
    font-weight: 400;
    color: #fff;
    line-height: 14.52px;
    cursor: pointer;
    text-align: left;
    text-decoration: underline;
  }
}

.NewBadge {
  width: 100%;
}

.SaveAddressPopup {
  .MuiDialog-paperWidthSm {
    height: 430px;
    width: 670px;
  }

  h3 {
    font-family: Roboto;
    font-size: 22px;
    font-weight: 600;
    line-height: 25.78px;
    text-align: center;
    color: #253858;
    margin-top: 10px;

    svg {
      float: left;
      cursor: pointer;
      color: #253858;
    }
  }

  ul {
    display: flex;
    list-style-type: none;
    margin-top: 10px;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;

    li {
      width: 60%;
      font-family: Roboto;
      line-height: 20px;
      font-size: 14px;
      color: #253858;
      height: 75px;
      border-bottom: 1px solid #0000001F;
      display: flex;
      text-align: left;
      font-weight: 500;
      align-items: center;

      &:nth-child(even) {
        width: 40%;
        justify-content: right;

        button {
          cursor: pointer;
          border-radius: 4px;
          background-color: #0065ff;
          width: 167px;
          height: 35px;
          text-align: center;
          font: normal normal 400 13px/21px Roboto;
          letter-spacing: 0.2px;
          color: #FFFFFF;
          border: none;
          line-height: 16.4px;
          outline: none;
        }
      }
    }
  }
}

.HowItWorkPopup {
  .MuiDialog-paperWidthSm {
    height: 480px;
    width: 90%;

    .Back {
      float: left;
      cursor: pointer;
    }

    h2 {
      font-family: Roboto;
      font-size: 22px;
      font-weight: 600;
      line-height: 25.78px;
      text-align: center;
      color: #253858E3;
      padding-top: 5px;
    }

    .TabBtn {
      display: flex;
      justify-content: center;

      button {
        box-shadow: 0px 0px 16px 0px #0065FF1F;
        font-family: Roboto;
        font-size: 14px;
        font-weight: 500;
        height: 48px;
        width: 160px;
        margin: 20px 0px;
        border-radius: 4px;
        line-height: 16.41px;
        text-align: center;
        color: #25385899;
        border: none;
        background-color: #fff;
        display: flex;
        justify-content: center;
        cursor: pointer;
        align-items: center;

        img {
          width: 22px;
          margin-right: 7px;
        }
      }

      .active {
        background-color: #0065ff;
        color: #fff;
      }
    }

    .Gray {
      background-color: #F9F9F9;
      height: 321px;

      span {
        color: #00A3FF;
        font-weight: 600;
      }

    }

    img {
      width: 100%;


    }

    h4 {
      background-color: #EBEBEB;
      border-radius: 24px;
      font-family: Roboto;
      width: 48px;
      font-size: 10px;
      font-weight: 600;
      line-height: 9px;
      text-align: center;
      color: #253858e3;
      padding: 5px 7px 4px;
      margin-left: 10px;
    }

    p {
      font-family: Roboto;
      font-size: 12px;
      font-weight: 400;
      line-height: 14.06px;
      text-align: left;
      padding: 12px;
      color: #253858E3;
    }

  }
}

.importantMsg {
  background: #e2f6ff 0% 0% no-repeat padding-box;
  color: #2665ad;
  border-left: 2px solid #0899dd;
  font-weight: 400;
  font-family: Roboto;
  font-size: 13px;
  line-height: 15.23px;
  border-radius: 3px;
  padding: 10px;
  width: 100%;

  strong {
    color: #1c589c;
  }
}

.GetCustomerLocation {
  border-radius: 4px;
  background: #002C4F;
  padding: 12px 20px 12px;
  position: relative;

  p {
    font-family: Roboto;
    font-size: 13px;
    font-weight: 600;
    line-height: 15.23px;
    text-align: left;
    color: #fff;
    margin-bottom: 7px;
  }

  h4 {
    font-family: Roboto;
    font-size: 20px;
    font-weight: 700;
    line-height: 23.44px;
    text-align: left;
    background: linear-gradient(90deg, #00FF0A 0%, #FBFF2A 38.91%, #68D2FF 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;

  }

  img {
    right: 4px;
    position: absolute;
    top: 54px;
  }

  .HeightLightMsg {
      width: 81%;
    font-size: 12px;
    border-left: 3px solid #75de24;
    border-right: 3px solid #75de24;
    margin-bottom: 0px;
    color: #ffffff;
    font-weight: 500;
    padding: 4px 10px;
    border-radius: 7px;
    margin-top: 12px;
    b{
      color:#75de24;
    }
  }

  .buttonLayout {
    display: flex;

    button {
         border: 1px solid #f7fbff;
    outline: none;
    background-color: rgba(0, 0, 0, 0);
    font-family: Roboto;
    font-size: 14px;
    border-radius: 4px;
    font-weight: 600;
    display: flex;
    align-items: center;
    line-height: 16.41px;
    text-align: center;
    cursor: pointer;
    color: #f7fbff;
    height: 33px;
    padding: 0px 26px;
    margin: 12px 15px 0px 0px;

      svg {
        margin-right: 5px;
        font-size: 20px;
      }
    }

    .requestLocationBtn {
      background-color: #fff;
      color: #0065FF;
      animation: none;
    }

  }
}
.notblinklayout{
  background-color: #E44A4A !important;
  .requestLocationBtn {
    animation: none;
     background-color: #fff;
    color: #0065FF;
  }
}
.errorLayout {
  background-color: #E44A4A !important;

  .requestLocationBtn {
    background-color: #fff;
    color: #0065FF;
    -webkit-animation: blink 0.6s infinite !important;
    /* Safari 4+ */
    -moz-animation: blink 0.6s infinite !important;
    /* Fx 5+ */
    -o-animation: blink 0.6s infinite !important;
    /* Opera 12+ */
    animation: blink 0.6s infinite !important;
    /* IE 10+, Fx 29+ */
  }

  @-webkit-keyframes blink {

    0%,
    49% {
      background-color: #0065FF;
      color: #fff;
      border-color: #0065FF;
    }

    50%,
    100% {
      background-color: #fff;
      color: #0065FF;

    }
  }

  @-moz-keyframes blink {

    0%,
    49% {
      background-color: #0065FF;
      color: #fff;
      border-color: #0065FF;
    }

    50%,
    100% {
      background-color: #fff;

    }
  }

  @-o-keyframes blink {

    0%,
    49% {
      background-color: #0065FF;
      color: #fff;
      border-color: #0065FF;
    }

    50%,
    100% {
      background-color: #fff;

    }
  }

  @keyframes blink {

    0%,
    49% {
      background-color: #0065FF;
      color: #fff;
      border-color: #0065FF;
    }

    50%,
    100% {
      background-color: #fff;

    }
  }
}



.Re-ScheduledreasonPopup {
  .MuiDialog-paperWidthSm {
    width: 640px;
  }
}

.OptedWhatsapp {
     display: flex;
    align-items: center;
    border-radius: 4px;
    background: rgba(255, 255, 255, 0.2);
    max-width: -webkit-fit-content;
    max-width: -moz-fit-content;
    max-width: fit-content;
    padding: 2px 8px;
    height: auto;
    margin-top: 10px;

  .waoptin-disabled {
    opacity: 0.5;
    cursor: not-allowed;
    background-color: #ccc !important;
    color: #666 !important;
    border: 1px solid #bbb !important;
  }

  button {
    margin-left: 12px;
    font-family: Roboto;
    font-size: 12px;
    font-weight: 600;
    line-height: 14.06px;
    border-radius: 4px;
   
    color: #fff;
  }
  .waoptblink
  {
     -webkit-animation: blink1 0.6s infinite !important;
    /* Safari 4+ */
    -moz-animation: blink1 0.6s infinite !important;
    /* Fx 5+ */
    -o-animation: blink1  0.6s infinite !important;
    /* Opera 12+ */
    animation: blink1 0.6s infinite !important;
   
  }

  @-webkit-keyframes blink1 {

    0%,
    49% {
      background-color: #fff;
      color: #0065FF;
      border-color: #0065FF;
    }

    50%,
    100% {
      background-color: #0065FF;
      color: #fff;

    }
  }
  @keyframes blink1 {

    0%,
    49% {
      background-color: #fff;
      color: #0065FF;
      border-color: #0065FF;
    }

    50%,
    100% {
      background-color: #0065FF;
      color: #fff;

    }
  }


  p {
    margin-bottom: 0px;
    font-family: Roboto;
    font-size: 12px;
    font-weight: 600;
    line-height: 14.06px;
    text-align: left;
  }

  img {
    position: static;
    margin-right: 6px;
    mix-blend-mode: luminosity;
  }

  .i-icon {
    margin-left: 3px;
    margin-right: -2px;
    display: flex;
    justify-content: center;
    align-items: center;
  }
}

.policy-wrapper {
  // margin: 15px;
  font-size: 16px;

  .policy-heading {
    font-weight: bold;
    margin-bottom: 10px;
    font-size: 16px;
    color: #253858E3;
  }

  .policy-container {
    display: flex;
    align-items: center;
    gap: 14px;
    font-size: 16px;
  }

  .policy-option {
    display: flex;
    align-items: center;
    border: 1px solid #0000003D;
    padding: 8px 15px;
    cursor: pointer;
    transition: all 0.2s ease-in-out;
    border-radius: 4px;
    min-width: 220px;

    &.selected {
      border-color: blue;
      color: blue;
      font-weight: bold;
    }

    input {
      accent-color: blue;
      margin-right: 8px;
    }

    label {
      cursor: pointer;
      font: normal normal normal 14px/24px Roboto !important;
      color: #253858E3;
    }
  }

  .policy-date-picker {
    display: flex;
    align-items: center;
    border-radius: 5px;
    position: relative;
    min-width: 200px;

    input {
      border: none;
      outline: none;
      font-size: 16px;
      background: none;
      cursor: pointer;
      width: 100%;
    }
  }

  @media only screen and (max-width: 1024px) {


    .policy-option {
      min-width: 225px;
    }

    .policy-date-picker {
      min-width: 180px;
    }
  }

  @media only screen and (max-width: 768px) {


    .policy-heading {
      font-size: 14px;
      margin-bottom: 15px;
    }

    .policy-container {
      gap: 12px;
      margin-left: 0;
      width: 100%;
    }

    .policy-option {
      width: 91%;
      min-width: unset;
    }

    .policy-date-picker {
      width: 90%;
      min-width: unset;
    }
  }

  @media only screen and (max-width: 600px) {
    .policy-option {
      width: 86%;
    }

    .policy-date-picker {
      width: 86%;
    }
  }

  @media only screen and (max-width: 480px) {


    .policy-heading {
      font-size: 13px;
      margin-bottom: 12px;
    }

    .policy-container {
      gap: 10px;
      width: 100%;
      flex-direction: column;
    }

    .policy-option {
      width: 100%;
      padding: 8px 12px;

      label {
        font-size: 13px !important;
        line-height: 20px !important;
      }
    }

    .policy-date-picker {
      width: 100%;

      input {
        font-size: 14px;
      }
    }
  }

  @media only screen and (max-width: 320px) {


    .policy-heading {
      font-size: 12px;
      margin-bottom: 10px;
    }

    .policy-option {
      padding: 6px 10px;

      label {
        font-size: 12px !important;
      }
    }

    .policy-date-picker input {
      font-size: 13px;
    }
  }


}