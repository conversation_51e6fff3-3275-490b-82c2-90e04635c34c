import React, { useEffect, useState } from "react";
// import { Grid } from "@mui/material";
import TransferTypesModal from "./Modals/TransferTypesModal";
// import SyncAltIcon from '@mui/icons-material/SyncAlt';
import { useDispatch, useSelector } from "react-redux";
import { CONFIG } from "../../../appconfig";
import User from "../../../services/user.service";
import { setIsCallTransferVisible } from "../../../store/actions/SalesView/SalesView";
import { IsCustomerAccess } from "../../../services/Common";


export default function CallTransfer(props) {
    const [currentPopup, setCurrentPopup] = useState("");
    let [IsCallTransferVisible] = useSelector(({ salesview }) => [salesview.IsCallTransferVisible]);
    let [AllLeads] = useSelector((state) => { let { allLeads } = state.salesview; return [allLeads]; });
    let dispatch = useDispatch();
    const OpenTransferType = () => {
        setCurrentPopup("TransferTypes");
    }
    useEffect(() => {
        const interval = setInterval(() => {
            if (User.RoleId === 13) {
                ShowTransferButton();
            }
        }, 8000);
        return () => clearInterval(interval);

    }, [AllLeads]);

    const ShowTransferButton = () => {
        if (User.IsProgressive == true || IsCustomerAccess()) {
            var dialerAPI_getLeadId = localStorage.getItem("dialerAPI_getLeadId") || null;
            if (localStorage.getItem("dialerAPI_getLeadId") != null && User.IsWFH == true) {
                dialerAPI_getLeadId = JSON.parse(dialerAPI_getLeadId);
                if (dialerAPI_getLeadId.status == 200 && dialerAPI_getLeadId.data && dialerAPI_getLeadId.data.customerAnswered == true) {
                    // setShowOtpSection(true)
                    dispatch(setIsCallTransferVisible({ IsCallTransferVisible: true }));
                }
                else {
                    // setShowOtpSection(false)
                    dispatch(setIsCallTransferVisible({ IsCallTransferVisible: false }))
                }
            }
            else if (window.AgentCall && window.AgentCall.popwin && window.AgentCall.popwin.oSipSessionCall != null && User.IsWFH == false) {
                // setShowOtpSection(true)
                dispatch(setIsCallTransferVisible({ IsCallTransferVisible: true }))
            }
            else {
                // setShowOtpSection(false)
                dispatch(setIsCallTransferVisible({ IsCallTransferVisible: false }))
            }
        }

    }

    return (
        <>
            {(IsCallTransferVisible || props.isVisible) &&
                <>
                    <span className="cltransfer" onClick={() => { OpenTransferType() }}> <img src={CONFIG.PUBLIC_URL + "/images/salesview/calltransfer.svg"} alt="transfer" /></span>
                    {currentPopup === "TransferTypes" && <TransferTypesModal
                        open={currentPopup === "TransferTypes"}
                        handleClose={() => { setCurrentPopup(null) }}
                    />}
                </>
            } 
        </>

    )

}

{/* {IsCallTransferVisible && */ }
{/* <Grid item sm={12} md={12} xs={12}> */ }
{/* <div className="callTransferSection"> */ }
// <span className="cltransfer" onClick={() => { OpenTransferType() }}> <img src="/images/salesview/calltransfer.svg" alt="transfer" /></span>
// <TransferTypesModal
//     open={currentPopup === "TransferTypes"}
//     handleClose={() => { setCurrentPopup(null) }}
// />
{/* <button className="transferbtn" onClick={() => { OpenTransferType() }}> <SyncAltIcon></SyncAltIcon> Transfer Call</button> */ }

{/* </div> */ }

{/* </Grid> */ }
{/* } */ }
