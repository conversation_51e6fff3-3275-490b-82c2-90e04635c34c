import { Container, <PERSON>rid, IconButton,Table, TableHead, TableRow, TableBody, TableCell, TableContainer, TablePagination, Tooltip, <PERSON>lapse, Button } from "@mui/material";
import React,{useEffect, useState} from "react";
import withStyles from '@mui/styles/withStyles';
import makeStyles from '@mui/styles/makeStyles';
import Paper from '@mui/material/Paper';
import { CONFIG } from "../../../appconfig";
import { GetHierarchialInforcementRatingData,FetchBHRBookingDataTLWise} from "../../../../src/services/Common";
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import ExpandLessIcon from '@mui/icons-material/ExpandLess';
import { v4 as uuid } from 'uuid';
import InfoIcon from '@mui/icons-material/Info';
import SaveAltIcon from '@mui/icons-material/SaveAlt';
import { enqueueSnackbar } from "notistack";
import ExcelJS from 'exceljs';
import { saveAs } from "file-saver";

const StyledTableCell = withStyles((theme) => ({
    head: {
        backgroundColor: '#e8e8e8',
        color: '#000000e3',
        font: 'normal normal 600 12px/16px Roboto',
        padding:'12px'
    },

}))(TableCell);

const StyledTableRow = withStyles((theme) => ({
    root: {
        '&:nth-of-type(even)': {
            backgroundColor: '#fff',
        },
    },
}))(TableRow);


const useStyles = makeStyles({
    table: {
        minWidth: 700,
    },
});

//const [ExpandedRows, setExpandedRows] = useState([]);

export const DownLevelBizEnforcementHierarchialData = (props) => {
    let [row, setrow] = useState([]);
    const classes = useStyles();
    const [page, setPage] = useState(0);
    const [rowsPerPage, setRowsPerPage] = useState(10);
    let [Downrows, setDownrows] = useState([]);
    let [isVisible, setIsVisible] = useState(false);
    let [ExpandedRows, setExpandedRows] = useState([]);
    let [productid, setproductid] = useState(0);
    useEffect(() => {
        if (props.row) 
        {setrow(props.row)}
        if (props.expandeddata) 
        {setExpandedRows(props.expandeddata)}
        else setrow([]);
        let productId = getProductIdFromCurrentUrl();
        setproductid(productId);
    }, [props.row]);
    
      const expand = (ID,UniqueId,userId,Role) => {
        let productId = getProductIdFromCurrentUrl();
        setproductid(productId);
        GetHierarchialInforcementRatingData(userId,Role,productId).then((res) => {
            if(Array.isArray(res)){
                let consData = JSON.parse(JSON.stringify(res));
                if(Array.isArray(consData) && consData.length>=2)
                      {
                        if(Array.isArray(res[0]))
                        {
                        let TotalAgentCount = 0;
                        let RedAgentCount = 0;
                        let AmberAgentCount = 0;
                        res[0].forEach((item) =>
                        {
                            TotalAgentCount += item.TotalAgentCount || 0;
                            RedAgentCount += item.RedCount || 0;
                            AmberAgentCount += item.AMBERCOUNT || 0;
                        });

                        // Find the row in your data structure that corresponds to UniqueId
                        if (row.uuid === UniqueId && row.EmployeeRole != 'TL') {
                            row.TotalAgentCount = TotalAgentCount;
                            row.RedCount = RedAgentCount;
                            row.AMBERCOUNT = AmberAgentCount;

                            // Update state with the modified row
                            setrow(row);
                        }

                        res.forEach((row) => {
                            row.uuid = uuid();
                        });
                        setDownrows(res[0]);
                    }
                        // if a row is expanded, collapses it, otherwise expands it
                        (ExpandedRows.includes(UniqueId)?
                            ExpandedRows = ExpandedRows.filter(item => item !== UniqueId)
                            :
                            setExpandedRows([...ExpandedRows, UniqueId]));
                        setIsVisible(!isVisible);
                      }
                      }
                      else
                      {
                        setDownrows([]);
                      }
        });
      };

      function getProductIdFromCurrentUrl() {
        const url = new URL(window.location.href);
        const params = new URLSearchParams(url.search);
        return params.get('ProductID');
    }

      const OpenAgentBusinessRatingDashboard=(UserId,UserName,Ecode) => {
        let productId = getProductIdFromCurrentUrl();
        setproductid(productId);
        var url = '../newsv/BusinessHealthRatingPopup?UserId=' + btoa(UserId) + '&UserName='+ btoa(UserName) + '&ECode='+ btoa(Ecode) + '&ProductID=' + productId;
        window.open(url, "_blank");
    }

    const handleFetchBHRBookingDataTLWise = (EmployeeCode,EmployeeRole,ProductID) => {
        FetchBHRBookingDataTLWise(EmployeeCode,EmployeeRole,ProductID).then(async (result) => {
            if(result && result[0] &&  Array.isArray(result[0]) && result[0].length > 0)
            {
                var data = result[0];
                const workbook = new ExcelJS.Workbook();
                const worksheet = workbook.addWorksheet('Sheet1');

                worksheet.columns = [
                  { header: "EmployeeId", key: 'EmployeeId', width: 12 },
                  { header: "Booking Id", key: 'LeadID', width: 18 },
                  { header: "Booking Date", key: "BookingDate", width: 12 },
                  { header: "Final Status", key: "Remark", width: 28 },
                ];
            
                data.forEach((item) => {
                  worksheet.addRow(item);
                });
            
                const buffer = await workbook.xlsx.writeBuffer();
                saveAs(new Blob([buffer], { type: 'application/octet-stream' }), `Pending For Approval.xlsx`);
            }
            else{
                enqueueSnackbar("Data not found.", { variant: 'error', autoHideDuration: 3000 });
            }
        });
    }


    return (
        <React.Fragment>
        {row.length === 0 ? <h5 className="Headingstyle">No Data Found</h5> :
        <>
            <StyledTableRow>
            {(row.EmployeeRole !== 'Agent')  &&
                <StyledTableCell align="center">
                    <IconButton aria-label="expand row" color="primary" size="small" onClick={() => { expand(row.ID,row.uuid,row.UserId,row.EmployeeRole) }}>
                            {isVisible && (ExpandedRows).includes(row.uuid) ? <ExpandLessIcon /> : <ExpandMoreIcon />}
                    </IconButton>
                </StyledTableCell>
            }
            <StyledTableCell component="th" scope="row" align="center">
                {rowsPerPage * ((page + 1)-1)+ row.ID}
            </StyledTableCell>
            {(row.EmployeeRole !== 'Agent')  &&
                <StyledTableCell align="center">{row.EmployeeName !== undefined ? row.EmployeeName : ""}</StyledTableCell>
            }
            {(row.EmployeeRole === 'Agent')  &&
                <StyledTableCell align="center"><a onClick={() => { OpenAgentBusinessRatingDashboard(row.UserId,row.EmployeeName,row.EmployeeCode) }}>{row.EmployeeName !== undefined ? row.EmployeeName : ""}</a></StyledTableCell>
            }
            {((row.EmployeeRole !== 'Agent') && productid == 7) &&
                <StyledTableCell align="center">{(row.TotalAgentCount !== undefined && row.TotalAgentCount !== null && row.RedCount !=undefined && row.RedCount !=null) ? row.RedCount + '/' + row.TotalAgentCount  : "_/_"}</StyledTableCell>
            }
            {((row.EmployeeRole !== 'Agent') && productid == 7) &&
                <StyledTableCell align="center">{(row.TotalAgentCount !== undefined && row.TotalAgentCount !== null && row.AMBERCOUNT !=undefined && row.AMBERCOUNT !=null) ? row.AMBERCOUNT + '/' + row.TotalAgentCount  : "_/_"}</StyledTableCell>
            }
            {(row.EmployeeRole === 'Agent' || productid == 115)  &&
                <StyledTableCell align="center" className={productid == 7 ? (row.Colour == 'Red' ? 'Red' : ((row.Colour == 'Amber') ? 'Amber' : 'Green')) : ''}>{row.BizRating !== undefined ? row.BizRating : ""}</StyledTableCell>
            }  
            {(row.EmployeeRole === 'Agent' || productid == 115)  &&
                <StyledTableCell align="center">{row.TotalPolicies !== undefined ? row.TotalPolicies : ""}</StyledTableCell>   
            }
            {(row.EmployeeRole === 'Agent' && productid == 7)  &&
                <StyledTableCell align="center">{row.SuperGroup !== undefined ? row.SuperGroup : ""}</StyledTableCell>   
            }    
            {(row.EmployeeRole && row.EmployeeRole == 'TL') && 
                <StyledTableCell align="center">
                    <IconButton onClick={() => handleFetchBHRBookingDataTLWise(row.EmployeeCode, row.EmployeeRole, productid)}>
                        <SaveAltIcon/>
                    </IconButton>
                </StyledTableCell>
            }                        
            </StyledTableRow>
            
            {(isVisible === true && (ExpandedRows).includes(row.uuid)) &&
            <StyledTableRow>
                <StyledTableCell colSpan={6}>
                    <DownLevelBizEnforcementData data={Downrows} expandeddata={ExpandedRows} />
                </StyledTableCell> 
            </StyledTableRow>
            }
            </>
        }
        </React.Fragment >
    );

}

export const DownLevelBizEnforcementData = (props) => {
    const classes = useStyles();
    const [page, setPage] = useState(0);
    const [rowsPerPage, setRowsPerPage] = useState(10);
    let [rows, setrows] = useState([]);
    let [ExpandedRows, setExpandedRows] = useState([]);
    let [productid, setproductid] = useState(0);
    
    useEffect(() => {
        if (props.data) 
        {setrows(props.data)}
        if (props.expandeddata) {
        setExpandedRows(props.expandeddata)}
        else setExpandedRows([]);
        let productId = getProductIdFromCurrentUrl();
        setproductid(productId);
    }, [props.data]);

    function getProductIdFromCurrentUrl() {
        const url = new URL(window.location.href);
        const params = new URLSearchParams(url.search);
        return params.get('ProductID');
    }

    const handleChangeRowsPerPage = event => {
        setRowsPerPage(parseInt(event.target.value, 10));
        setPage(0);
      };
    const handleChangePage = (event, newPage) => {
        //setExpandedRows([]);
        setPage(newPage);
      };

      return (
        <>
                <Grid container>
                    <Grid item sm={12} md={12} xs={12}>
                            <TableContainer component={Paper} className="tabledata">
                                <Table className={classes.table} aria-label="customized table">
                                    <TableHead>
                                        <TableRow>
                                            {(rows.length > 0 && rows[0].EmployeeRole !== 'Agent')  &&
                                                <StyledTableCell align="center">Action &nbsp;
                                                    {(rows.length > 0 && rows[0].EmployeeRole == 'Manager' && productid == 7) &&
                                                        <Tooltip className="forceRatingTooltip" classes={{ popper: "forceRatingTooltipPopup" }} title={
                                                        <div><p className="msg">Click<ExpandMoreIcon/>to check advisors in 'Red' and 'Amber' category</p></div>} arrow>
                                                            <InfoIcon className="RedinfoIcon"/>
                                                        </Tooltip>
                                                    } 
                                                </StyledTableCell>
                                            }
                                            <StyledTableCell align="center">S. No</StyledTableCell>
                                            {(rows.length > 0)  &&
                                                <StyledTableCell align="center">{rows[0].EmployeeRole}</StyledTableCell>
                                            }
                                            {((rows.length > 0 && productid == 115) || rows.length > 0 && rows[0].EmployeeRole == 'Agent' && productid == 7)  ?
                                                <StyledTableCell align="center">Business Health Rating</StyledTableCell>
                                                :
                                                <StyledTableCell align="center">Red Advisors/Total Advisors</StyledTableCell>
                                            }
                                            {((rows.length > 0 && productid == 115) || rows.length > 0 && rows[0].EmployeeRole == 'Agent' && productid == 7)  ?
                                                <StyledTableCell align="center">Total Policies</StyledTableCell>
                                                :
                                                <StyledTableCell align="center">Amber Advisors/Total Advisors</StyledTableCell>
                                            }
                                            {(rows.length > 0 && rows[0].EmployeeRole == 'Agent' && productid == 7)  &&
                                                <StyledTableCell align="center">BHR SuperGroup</StyledTableCell>
                                            }
                                            {
                                                rows.length > 0 && rows[0].EmployeeRole && rows[0].EmployeeRole == 'TL' && 
                                                <StyledTableCell align="center">
                                                    Export
                                                </StyledTableCell>
                                            }
                                        </TableRow>
                                    </TableHead>
                                    <TableBody>
                                    <>
                                    {rows.length === 0 ? <h5 className="Headingstyle">No Data Found</h5> :
                                    (rowsPerPage > 0
                                            ? rows.slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
                                            : rows
                                        ).map((row,index) => (
                                            <DownLevelBizEnforcementHierarchialData row={row} expandeddata = {ExpandedRows}/>
                                        ))} 
                                         
                                    </>
                                    </TableBody>
                                </Table>
                            </TableContainer>
                        <TablePagination
                            component="div"
                            count={rows.length}
                            page={page}
                            onPageChange={handleChangePage}
                            rowsPerPage={rowsPerPage}
                            rowsPerPageOptions={[10, 20, 50,100, { label: 'All', value: -1 }]}
                            onRowsPerPageChange={handleChangeRowsPerPage}
                            hidden={rows.length<10}
                        />
                    </Grid>
                    </Grid>
             

            
        </>
    )

}
