import React from "react";
import { AvailDiscountPopup } from "./AvailDiscountPopup";
import { TermAvailDiscountPopup } from "./TermAvailDiscountPopup";

const AvailDiscounts = (props) => {

    return <>
                  
                {props.ProductID == 2 &&
                 <AvailDiscountPopup
                 open={props.open}
                 handleClose={() => { props.handleClose(); }}
                 LeadID={props.LeadID}
                 ProductID={props.ProductID}
               />
                }
                {props.ProductID == 130 &&
                 <AvailDiscountPopup
                 open={props.open}
                 handleClose={() => { props.handleClose(); }}
                 LeadID={props.LeadID}
                 ProductID={props.ProductID}
               />
                }
                {props.ProductID == 7 &&
                 <TermAvailDiscountPopup
                 open={props.open}
                 handleClose={() => { props.handleClose(); }}
                 LeadID={props.LeadID}
                 ProductID={props.ProductID}
                 LeadAssignedUser={props.LeadAssignedUser}
               />
                }
                                                                   
    </>
}

export default AvailDiscounts;