<svg xmlns="http://www.w3.org/2000/svg" width="27.577" height="780.116" viewBox="0 0 27.577 780.116">
  <g id="Group_35" data-name="Group 35" transform="translate(-279.646 -128)">
    <g id="add-user" transform="translate(280 569.897)">
      <g id="Group_7" data-name="Group 7">
        <g id="Group_6" data-name="Group 6">
          <path id="Path_1" data-name="Path 1" d="M16.954,12.043a11.89,11.89,0,0,0-1.388-.566,6.4,6.4,0,0,0,2.515-5.1,6.273,6.273,0,1,0-12.547,0,6.4,6.4,0,0,0,2.522,5.1A11.932,11.932,0,0,0,4.19,13.64,12.266,12.266,0,0,0,.06,20.565a2.882,2.882,0,0,0,.57,2.386A2.768,2.768,0,0,0,2.8,24H14.16a.938.938,0,0,0,0-1.875H2.8a.933.933,0,0,1-.738-.357.991.991,0,0,1-.2-.821,10.237,10.237,0,0,1,9.7-8.2h.492a9.916,9.916,0,0,1,4.107.991.916.916,0,0,0,1.229-.444.944.944,0,0,0-.436-1.251ZM12.033,10.87h-.448a4.428,4.428,0,1,1,.448,0Z" fill="#253858" stroke="#fff" stroke-width="0.4"/>
        </g>
      </g>
      <g id="Group_8" data-name="Group 8" transform="translate(14.627 14.627)">
        <path id="Path_2" data-name="Path 2" d="M330.387,325.7h-2.713v-2.713a.987.987,0,0,0-1.973,0V325.7h-2.713a.987.987,0,0,0,0,1.973H325.7v2.713a.987.987,0,1,0,1.973,0v-2.713h2.713a.987.987,0,0,0,0-1.973Z" transform="translate(-322 -322)" fill="#253858" stroke="#fff" stroke-width="0.4"/>
      </g>
    </g>
    <g id="calendar" transform="translate(280 305.969)">
      <circle id="Ellipse_1" data-name="Ellipse 1" cx="1" cy="1" r="1" transform="translate(17 8.544)" fill="#253858"/>
      <path id="Path_3" data-name="Path 3" d="M20.25,1.875H19.031V.938a.938.938,0,0,0-1.875,0v.938H12.891V.938a.938.938,0,0,0-1.875,0v.938H6.8V.938a.938.938,0,0,0-1.875,0v.938H3.75A3.754,3.754,0,0,0,0,5.625V20.25A3.754,3.754,0,0,0,3.75,24h7.172a.938.938,0,0,0,0-1.875H3.75A1.877,1.877,0,0,1,1.875,20.25V5.625A1.877,1.877,0,0,1,3.75,3.75H4.922v.938a.938.938,0,0,0,1.875,0V3.75h4.219v.938a.938.938,0,0,0,1.875,0V3.75h4.266v.938a.938.938,0,0,0,1.875,0V3.75H20.25a1.877,1.877,0,0,1,1.875,1.875v5.344a.938.938,0,0,0,1.875,0V5.625A3.754,3.754,0,0,0,20.25,1.875Z" fill="#253858"/>
      <path id="Path_4" data-name="Path 4" d="M275.672,270a5.672,5.672,0,1,0,5.672,5.672A5.672,5.672,0,0,0,275.672,270Zm0,9.469a3.8,3.8,0,1,1,3.8-3.8A3.8,3.8,0,0,1,275.672,279.469Z" transform="translate(-257.344 -257.344)" fill="#253858"/>
      <path id="Path_5" data-name="Path 5" d="M373.3,331.922h-.422v-.984a.938.938,0,0,0-1.875,0v1.922a.937.937,0,0,0,.938.938H373.3a.938.938,0,0,0,0-1.875Z" transform="translate(-353.609 -314.531)" fill="#253858"/>
      <circle id="Ellipse_2" data-name="Ellipse 2" cx="1" cy="1" r="1" transform="translate(13 8.544)" fill="#253858"/>
      <circle id="Ellipse_3" data-name="Ellipse 3" cx="1" cy="1" r="1" transform="translate(9 12.544)" fill="#253858"/>
      <circle id="Ellipse_4" data-name="Ellipse 4" cx="1" cy="1" r="1" transform="translate(5 8.544)" fill="#253858"/>
      <circle id="Ellipse_5" data-name="Ellipse 5" cx="1" cy="1" r="1" transform="translate(5 12.544)" fill="#253858"/>
      <circle id="Ellipse_6" data-name="Ellipse 6" cx="1" cy="1" r="1" transform="translate(5 16.544)" fill="#253858"/>
      <circle id="Ellipse_7" data-name="Ellipse 7" cx="1" cy="1" r="1" transform="translate(9 16.544)" fill="#253858"/>
      <circle id="Ellipse_8" data-name="Ellipse 8" cx="1" cy="1" r="1" transform="translate(9 8.544)" fill="#253858"/>
    </g>
    <g id="feedback_1_" data-name="feedback (1)" transform="translate(280.002 460.667)">
      <g id="Group_9" data-name="Group 9" transform="translate(0 21.333)">
        <path id="Path_6" data-name="Path 6" d="M107.256,129.18h12.988a.59.59,0,1,0,0-1.181H107.256a.59.59,0,1,0,0,1.181Z" transform="translate(-102.569 -122.566)" fill="#253858" stroke="#253858" stroke-width="0.3"/>
        <path id="Path_7" data-name="Path 7" d="M114.34,256h-7.084a.59.59,0,1,0,0,1.181h7.084a.591.591,0,0,0,0-1.181Z" transform="translate(-102.038 -244.047)" fill="#253858" stroke="#253858" stroke-width="0.3"/>
        <path id="Path_8" data-name="Path 8" d="M22.086,23.515c-.029,0-.057.008-.086.009v-.009a2.1,2.1,0,0,0-2-2.182H2a2.1,2.1,0,0,0-2,2.182V44.788a.548.548,0,0,0,.309.5A.459.459,0,0,0,.5,45.33a.48.48,0,0,0,.354-.16l4.854-5.3H20A2.1,2.1,0,0,0,22,37.7V28.649l1.439-1.57h0A2.2,2.2,0,0,0,24,25.6a2.009,2.009,0,0,0-1.914-2.085ZM21,37.7a1.049,1.049,0,0,1-1,1.091H5.5a.48.48,0,0,0-.354.16L1,43.471V23.515a1.049,1.049,0,0,1,1-1.091H20a1.049,1.049,0,0,1,1,1.091v.373a1.925,1.925,0,0,0-.268.239l-5.44,5.934H5.5a.548.548,0,0,0,0,1.091h8.99l-.48,2.62a.577.577,0,0,0,.137.493.48.48,0,0,0,.354.16.469.469,0,0,0,.1-.011l2.5-.545a.486.486,0,0,0,.256-.149L21,29.74Zm-4.247-4.866-1.615.353.323-1.764L20,26.468l1.293,1.411Zm5.979-6.523-.732.8L20.707,25.7l.733-.8a.884.884,0,0,1,.646-.291.96.96,0,0,1,.914,1,1.055,1.055,0,0,1-.268.7Z" transform="translate(0 -21.333)" fill="#253858" stroke="#253858" stroke-width="0.3"/>
      </g>
    </g>
    <g id="list" transform="translate(232.96 840.005)">
      <g id="Group_11" data-name="Group 11" transform="translate(47.037)">
        <g id="Group_10" data-name="Group 10">
          <path id="Path_9" data-name="Path 9" d="M64.492,3.145h-2.3V2.173c0-.314-.364-.457-.7-.457H59.7A2.714,2.714,0,0,0,57.007,0a2.761,2.761,0,0,0-2.7,1.714H52.552c-.333,0-.667.143-.667.457v.971h-2.3A2.512,2.512,0,0,0,47.04,5.456v16.37A2.387,2.387,0,0,0,49.582,24h14.91a2.387,2.387,0,0,0,2.545-2.171V5.459a2.512,2.512,0,0,0-2.545-2.314ZM53.1,2.859h1.667a.651.651,0,0,0,.576-.514A1.736,1.736,0,0,1,57.01,1.059a1.7,1.7,0,0,1,1.636,1.286.654.654,0,0,0,.606.514h1.727V5.145H53.1Zm12.727,18.97c0,.629-.667,1.028-1.333,1.028H49.582c-.667,0-1.333-.4-1.333-1.028V5.459a1.333,1.333,0,0,1,1.333-1.171h2.3V5.744a.626.626,0,0,0,.667.543h8.939a.657.657,0,0,0,.7-.543V4.287h2.3a1.333,1.333,0,0,1,1.333,1.171v16.37Z" transform="translate(-47.037)" fill="#253858" stroke="#253858" stroke-width="0.2"/>
          <path id="Path_10" data-name="Path 10" d="M104.025,230.5a.608.608,0,0,0-.851-.03l-1.945,1.854-.821-.851a.608.608,0,0,0-.851-.03.638.638,0,0,0,0,.881L100.8,233.6a.547.547,0,0,0,.425.182.608.608,0,0,0,.425-.182l2.371-2.249a.577.577,0,0,0,.035-.816A.355.355,0,0,0,104.025,230.5Z" transform="translate(-96.348 -217.844)" fill="#253858" stroke="#253858" stroke-width="0.2"/>
          <path id="Path_11" data-name="Path 11" d="M206.146,256.034h-6.99a.608.608,0,1,0,0,1.216h6.99a.608.608,0,1,0,0-1.216Z" transform="translate(-189.778 -242.078)" fill="#253858" stroke="#253858" stroke-width="0.2"/>
          <path id="Path_12" data-name="Path 12" d="M104.025,146.909a.608.608,0,0,0-.851-.03l-1.945,1.854-.821-.851a.608.608,0,0,0-.851-.03.638.638,0,0,0,0,.881l1.246,1.276a.547.547,0,0,0,.425.182.608.608,0,0,0,.425-.182l2.371-2.249a.577.577,0,0,0,.035-.816Q104.043,146.926,104.025,146.909Z" transform="translate(-96.348 -138.777)" fill="#253858" stroke="#253858" stroke-width="0.2"/>
          <path id="Path_13" data-name="Path 13" d="M206.146,172.442h-6.99a.608.608,0,1,0,0,1.216h6.99a.608.608,0,0,0,0-1.216Z" transform="translate(-189.778 -163.042)" fill="#253858" stroke="#253858" stroke-width="0.2"/>
          <path id="Path_14" data-name="Path 14" d="M104.025,314.092a.608.608,0,0,0-.851-.03l-1.945,1.854-.821-.851a.608.608,0,0,0-.851-.03.638.638,0,0,0,0,.881l1.246,1.276a.547.547,0,0,0,.425.182.608.608,0,0,0,.425-.182l2.371-2.249a.577.577,0,0,0,.035-.816Q104.043,314.109,104.025,314.092Z" transform="translate(-96.348 -297.174)" fill="#253858" stroke="#253858" stroke-width="0.2"/>
          <path id="Path_15" data-name="Path 15" d="M206.146,339.626h-6.99a.608.608,0,1,0,0,1.216h6.99a.608.608,0,1,0,0-1.216Z" transform="translate(-189.778 -321.113)" fill="#253858" stroke="#253858" stroke-width="0.2"/>
        </g>
      </g>
    </g>
    <g id="messge" transform="translate(280 122.002)">
      <g id="Group_12" data-name="Group 12" transform="translate(0 6)">
        <path id="Path_16" data-name="Path 16" d="M21.429,0H2.571A2.571,2.571,0,0,0,0,2.573V16.288a2.571,2.571,0,0,0,2.571,2.571H5.614L5.148,23.05a.857.857,0,0,0,1.425.731l5.47-4.923h9.385A2.571,2.571,0,0,0,24,16.288V2.573A2.571,2.571,0,0,0,21.429,0Zm.857,16.286a.857.857,0,0,1-.857.857H11.715a.857.857,0,0,0-.573.22L7.1,21l.323-2.9a.857.857,0,0,0-.852-.952h-4a.857.857,0,0,1-.857-.857V2.573a.857.857,0,0,1,.857-.857H21.429a.857.857,0,0,1,.857.857V16.288Z" transform="translate(0 -0.002)" fill="#253858"/>
      </g>
    </g>
    <g id="note" transform="translate(280.001 642)">
      <path id="Path_17" data-name="Path 17" d="M20.906,16H3.094A3.205,3.205,0,0,0,0,19.3V36.7A3.205,3.205,0,0,0,3.094,40H18.75a3.338,3.338,0,0,0,2.45-1.083l1.784-1.9A3.8,3.8,0,0,0,24,34.4V19.3A3.2,3.2,0,0,0,20.906,16ZM3.094,17.6H20.906A1.651,1.651,0,0,1,22.5,19.3v2.3H1.5V19.3a1.651,1.651,0,0,1,1.594-1.7ZM1.5,36.7V23.2h21V33.6H20.156A2.234,2.234,0,0,0,18,35.9v2.5H3.094A1.651,1.651,0,0,1,1.5,36.7Zm20.424-.817-1.784,1.9a1.96,1.96,0,0,1-.64.457V35.9a.68.68,0,0,1,.656-.7h2.2a2.109,2.109,0,0,1-.432.683Z" fill="#253858" stroke="#fff" stroke-width="0.2"/>
      <path id="Path_18" data-name="Path 18" d="M124.333,208.833a.77.77,0,0,0-.685-.833H112.685a.849.849,0,0,0,0,1.667h10.963a.77.77,0,0,0,.685-.834Z" transform="translate(-106.167 -182.429)" fill="#253858" stroke="#fff" stroke-width="0.2"/>
      <path id="Path_19" data-name="Path 19" d="M154.833,288h-10a.834.834,0,0,0,0,1.667h10a.833.833,0,1,0,0-1.667Z" transform="translate(-137.833 -258.44)" fill="#253858" stroke="#fff" stroke-width="0.2"/>
    </g>
    <g id="ticket_2_" data-name="ticket (2)" transform="translate(206.461 689.431) rotate(-45)">
      <g id="Group_14" data-name="Group 14" transform="translate(14.028 106.848)">
        <g id="Group_13" data-name="Group 13">
          <rect id="Rectangle_1" data-name="Rectangle 1" width="1" height="1" fill="#253858" stroke="#253858" stroke-width="0.5"/>
        </g>
      </g>
      <g id="Group_15" data-name="Group 15" transform="translate(14.028 105.728)">
        <rect id="Rectangle_2" data-name="Rectangle 2" width="1" height="1" fill="#253858" stroke="#253858" stroke-width="0.5"/>
      </g>
      <g id="Group_17" data-name="Group 17" transform="translate(14.028 108.934)">
        <g id="Group_16" data-name="Group 16">
          <rect id="Rectangle_3" data-name="Rectangle 3" width="1" height="1" fill="#253858" stroke="#253858" stroke-width="0.5"/>
        </g>
      </g>
      <g id="Group_19" data-name="Group 19" transform="translate(14.028 111.001)">
        <g id="Group_18" data-name="Group 18">
          <rect id="Rectangle_4" data-name="Rectangle 4" width="1" fill="#253858" stroke="#253858" stroke-width="0.5"/>
        </g>
      </g>
      <g id="Group_21" data-name="Group 21" transform="translate(14.028 112.065)">
        <g id="Group_20" data-name="Group 20">
          <rect id="Rectangle_5" data-name="Rectangle 5" width="1" height="1" fill="#253858" stroke="#253858" stroke-width="0.5"/>
        </g>
      </g>
      <g id="Group_23" data-name="Group 23" transform="translate(14.028 114.152)">
        <g id="Group_22" data-name="Group 22">
          <rect id="Rectangle_6" data-name="Rectangle 6" width="1" height="1" fill="#253858" stroke="#253858" stroke-width="0.5"/>
        </g>
      </g>
      <g id="Group_24" data-name="Group 24" transform="translate(14.028 115.272)">
        <rect id="Rectangle_7" data-name="Rectangle 7" width="1" height="1" fill="#253858" stroke="#253858" stroke-width="0.5"/>
      </g>
      <g id="Group_26" data-name="Group 26" transform="translate(2.874 105.729)">
        <g id="Group_25" data-name="Group 25">
          <rect id="Rectangle_8" data-name="Rectangle 8" width="10" height="1" fill="#253858" stroke="#253858" stroke-width="0.5"/>
        </g>
      </g>
      <g id="Group_28" data-name="Group 28" transform="translate(2.874 115.271)">
        <g id="Group_27" data-name="Group 27">
          <rect id="Rectangle_9" data-name="Rectangle 9" width="10" height="1" fill="#253858" stroke="#253858" stroke-width="0.5"/>
        </g>
      </g>
      <g id="Group_30" data-name="Group 30" transform="translate(16.126 105.729)">
        <g id="Group_29" data-name="Group 29">
          <rect id="Rectangle_10" data-name="Rectangle 10" width="5" height="1" fill="#253858" stroke="#253858" stroke-width="0.5"/>
        </g>
      </g>
      <g id="Group_32" data-name="Group 32" transform="translate(16.126 115.271)">
        <g id="Group_31" data-name="Group 31">
          <rect id="Rectangle_11" data-name="Rectangle 11" width="5" height="1" fill="#253858" stroke="#253858" stroke-width="0.5"/>
        </g>
      </g>
      <g id="Group_34" data-name="Group 34" transform="translate(0 104)">
        <g id="Group_33" data-name="Group 33">
          <path id="Path_20" data-name="Path 20" d="M23.6,108.118a.406.406,0,0,0,.4-.412v-3.294a.406.406,0,0,0-.4-.412H.4a.406.406,0,0,0-.4.412v3.294a.406.406,0,0,0,.4.412,2.884,2.884,0,0,1,0,5.765.406.406,0,0,0-.4.412v3.294A.406.406,0,0,0,.4,118H23.6a.406.406,0,0,0,.4-.412v-3.294a.406.406,0,0,0-.4-.412,2.884,2.884,0,0,1,0-5.765Zm-3.577,3.3a3.654,3.654,0,0,0,3.177,3.27v2.494H.8v-2.494a3.682,3.682,0,0,0,3.177-4.1A3.654,3.654,0,0,0,.8,107.317v-2.494H23.2v2.494a3.682,3.682,0,0,0-3.177,4.1Z" transform="translate(0 -104)" fill="#253858" stroke="#253858" stroke-width="0.5"/>
        </g>
      </g>
    </g>
    <g id="time_1_" data-name="time (1)" transform="translate(280 394.005)">
      <path id="Path_21" data-name="Path 21" d="M254.167,423.186c-.214.053-.432.1-.648.14a1.042,1.042,0,1,0,.38,2.049c.258-.048.517-.1.772-.168a1.042,1.042,0,0,0-.5-2.022Z" transform="translate(-239.758 -401.404)" fill="#253858"/>
      <path id="Path_22" data-name="Path 22" d="M412.762,138.263a1.042,1.042,0,1,0,1.978-.655c-.082-.249-.174-.5-.271-.742a1.042,1.042,0,1,0-1.935.774C412.616,137.844,412.693,138.053,412.762,138.263Z" transform="translate(-391.398 -129.28)" fill="#253858"/>
      <path id="Path_23" data-name="Path 23" d="M321.953,394.084c-.184.121-.374.239-.565.349a1.042,1.042,0,1,0,1.041,1.805c.227-.131.453-.27.672-.415a1.042,1.042,0,0,0-1.149-1.739Z" transform="translate(-304.467 -373.789)" fill="#253858"/>
      <path id="Path_24" data-name="Path 24" d="M429.718,208.313a1.042,1.042,0,1,0-2.082.082c.009.22.011.443.006.663a1.042,1.042,0,1,0,2.083.046C429.73,208.841,429.728,208.575,429.718,208.313Z" transform="translate(-405.727 -196.72)" fill="#253858"/>
      <path id="Path_25" data-name="Path 25" d="M378.878,344.277a1.042,1.042,0,0,0-1.459.208c-.132.176-.272.35-.414.518a1.042,1.042,0,0,0,.118,1.469c.025.021.05.041.076.059a1.042,1.042,0,0,0,1.393-.177c.17-.2.336-.408.494-.619A1.042,1.042,0,0,0,378.878,344.277Z" transform="translate(-357.684 -326.423)" fill="#253858"/>
      <path id="Path_26" data-name="Path 26" d="M415.344,279.4a1.042,1.042,0,0,0-1.306.683c-.066.21-.139.421-.218.627a1.042,1.042,0,1,0,1.947.742c.094-.245.18-.5.259-.746A1.042,1.042,0,0,0,415.344,279.4Z" transform="translate(-392.618 -265.048)" fill="#253858"/>
      <path id="Path_27" data-name="Path 27" d="M10.094,23.759a9.71,9.71,0,0,1-2.445-.792l-.027-.015c-.183-.088-.365-.182-.542-.28h0a10.141,10.141,0,0,1-.949-.6A10.279,10.279,0,0,1,3.731,7.94,10,10,0,0,1,6.17,5.455l.03-.024A9.809,9.809,0,0,1,17.369,5.34l-.745,1.1c-.207.3-.08.528.283.495l3.235-.3a.542.542,0,0,0,.482-.711l-.864-3.19c-.1-.358-.346-.4-.554-.1l-.747,1.1a11.594,11.594,0,0,0-8.627-1.87q-.458.081-.9.2H8.924l-.034.01A11.747,11.747,0,0,0,2.371,6.529c-.014.017-.028.033-.041.051-.054.074-.108.15-.16.226-.086.125-.171.252-.252.38-.01.015-.018.031-.027.047A12.158,12.158,0,0,0,.013,14.3c0,.008,0,.016,0,.025.011.244.03.491.055.734a.1.1,0,0,0,.007.046q.041.367.1.735a12.073,12.073,0,0,0,3.318,6.5l.012.013h0a12.07,12.07,0,0,0,1.525,1.3,11.636,11.636,0,0,0,4.727,2.017.955.955,0,0,0,1.106-.785A.97.97,0,0,0,10.094,23.759Z" transform="translate(0 -1.688)" fill="#253858"/>
      <path id="Path_28" data-name="Path 28" d="M206.83,83.2a.843.843,0,0,0-.843.843v8.4l7.682,3.971a.844.844,0,0,0,.774-1.5l-6.77-3.5V84.041a.843.843,0,0,0-.843-.841Z" transform="translate(-195.805 -79.29)" fill="#253858"/>
    </g>
    <g id="whatsapp_1_" data-name="whatsapp (1)" transform="translate(280 218)">
      <path id="Path_29" data-name="Path 29" d="M17.507,14.307l-.009.075c-2.2-1.1-2.429-1.242-2.713-.816-.2.3-.771.964-.944,1.162s-.349.21-.646.075a8.116,8.116,0,0,1-2.4-1.485,9.073,9.073,0,0,1-1.66-2.07c-.293-.506.32-.578.878-1.634a.55.55,0,0,0-.025-.524c-.075-.15-.672-1.62-.922-2.206s-.487-.51-.672-.51a1.488,1.488,0,0,0-1.368.344c-1.614,1.774-1.207,3.6.174,5.55,2.714,3.552,4.16,4.206,6.8,5.114a4.137,4.137,0,0,0,1.88.121,3.077,3.077,0,0,0,2.02-1.426,2.475,2.475,0,0,0,.18-1.425c-.074-.135-.27-.21-.57-.345Z" fill="#253858" stroke="#fff" stroke-width="0.3"/>
      <path id="Path_30" data-name="Path 30" d="M20.52,3.449C12.831-3.984.106,1.407.1,11.893A11.838,11.838,0,0,0,1.7,17.84L0,24l6.335-1.652A11.971,11.971,0,0,0,24,11.9a11.794,11.794,0,0,0-3.495-8.411ZM22,11.866a9.956,9.956,0,0,1-15.01,8.5l-.36-.214-3.75.975,1.005-3.645-.239-.375A9.918,9.918,0,0,1,19.093,4.876,9.788,9.788,0,0,1,22,11.866Z" fill="#253858" stroke="#fff" stroke-width="0.3"/>
    </g>
    <g id="messge-2" data-name="messge" transform="translate(280 166.002)">
      <g id="Group_12-2" data-name="Group 12" transform="translate(0 6)">
        <path id="Path_16-2" data-name="Path 16" d="M21.429,0H2.571A2.571,2.571,0,0,0,0,2.573V16.288a2.571,2.571,0,0,0,2.571,2.571H5.614L5.148,23.05a.857.857,0,0,0,1.425.731l5.47-4.923h9.385A2.571,2.571,0,0,0,24,16.288V2.573A2.571,2.571,0,0,0,21.429,0Zm.857,16.286a.857.857,0,0,1-.857.857H11.715a.857.857,0,0,0-.573.22L7.1,21l.323-2.9a.857.857,0,0,0-.852-.952h-4a.857.857,0,0,1-.857-.857V2.573a.857.857,0,0,1,.857-.857H21.429a.857.857,0,0,1,.857.857V16.288Z" transform="translate(0 -0.002)" fill="#0065ff"/>
      </g>
    </g>
    <g id="whatsapp_1_2" data-name="whatsapp (1)" transform="translate(280 262)">
      <path id="Path_29-2" data-name="Path 29" d="M17.507,14.307l-.009.075c-2.2-1.1-2.429-1.242-2.713-.816-.2.3-.771.964-.944,1.162s-.349.21-.646.075a8.116,8.116,0,0,1-2.4-1.485,9.073,9.073,0,0,1-1.66-2.07c-.293-.506.32-.578.878-1.634a.55.55,0,0,0-.025-.524c-.075-.15-.672-1.62-.922-2.206s-.487-.51-.672-.51a1.488,1.488,0,0,0-1.368.344c-1.614,1.774-1.207,3.6.174,5.55,2.714,3.552,4.16,4.206,6.8,5.114a4.137,4.137,0,0,0,1.88.121,3.077,3.077,0,0,0,2.02-1.426,2.475,2.475,0,0,0,.18-1.425c-.074-.135-.27-.21-.57-.345Z" fill="#0065ff" stroke="#fff" stroke-width="0.3"/>
      <path id="Path_30-2" data-name="Path 30" d="M20.52,3.449C12.831-3.984.106,1.407.1,11.893A11.838,11.838,0,0,0,1.7,17.84L0,24l6.335-1.652A11.971,11.971,0,0,0,24,11.9a11.794,11.794,0,0,0-3.495-8.411ZM22,11.866a9.956,9.956,0,0,1-15.01,8.5l-.36-.214-3.75.975,1.005-3.645-.239-.375A9.918,9.918,0,0,1,19.093,4.876,9.788,9.788,0,0,1,22,11.866Z" fill="#0065ff" stroke="#fff" stroke-width="0.3"/>
    </g>
    <g id="calendar-2" data-name="calendar" transform="translate(280 349.969)">
      <circle id="Ellipse_1-2" data-name="Ellipse 1" cx="1" cy="1" r="1" transform="translate(17 8.544)" fill="#0065ff"/>
      <path id="Path_3-2" data-name="Path 3" d="M20.25,1.875H19.031V.938a.938.938,0,0,0-1.875,0v.938H12.891V.938a.938.938,0,0,0-1.875,0v.938H6.8V.938a.938.938,0,0,0-1.875,0v.938H3.75A3.754,3.754,0,0,0,0,5.625V20.25A3.754,3.754,0,0,0,3.75,24h7.172a.938.938,0,0,0,0-1.875H3.75A1.877,1.877,0,0,1,1.875,20.25V5.625A1.877,1.877,0,0,1,3.75,3.75H4.922v.938a.938.938,0,0,0,1.875,0V3.75h4.219v.938a.938.938,0,0,0,1.875,0V3.75h4.266v.938a.938.938,0,0,0,1.875,0V3.75H20.25a1.877,1.877,0,0,1,1.875,1.875v5.344a.938.938,0,0,0,1.875,0V5.625A3.754,3.754,0,0,0,20.25,1.875Z" fill="#0065ff"/>
      <path id="Path_4-2" data-name="Path 4" d="M275.672,270a5.672,5.672,0,1,0,5.672,5.672A5.672,5.672,0,0,0,275.672,270Zm0,9.469a3.8,3.8,0,1,1,3.8-3.8A3.8,3.8,0,0,1,275.672,279.469Z" transform="translate(-257.344 -257.344)" fill="#0065ff"/>
      <path id="Path_5-2" data-name="Path 5" d="M373.3,331.922h-.422v-.984a.938.938,0,0,0-1.875,0v1.922a.937.937,0,0,0,.938.938H373.3a.938.938,0,0,0,0-1.875Z" transform="translate(-353.609 -314.531)" fill="#0065ff"/>
      <circle id="Ellipse_2-2" data-name="Ellipse 2" cx="1" cy="1" r="1" transform="translate(13 8.544)" fill="#0065ff"/>
      <circle id="Ellipse_3-2" data-name="Ellipse 3" cx="1" cy="1" r="1" transform="translate(9 12.544)" fill="#0065ff"/>
      <circle id="Ellipse_4-2" data-name="Ellipse 4" cx="1" cy="1" r="1" transform="translate(5 8.544)" fill="#0065ff"/>
      <circle id="Ellipse_5-2" data-name="Ellipse 5" cx="1" cy="1" r="1" transform="translate(5 12.544)" fill="#0065ff"/>
      <circle id="Ellipse_6-2" data-name="Ellipse 6" cx="1" cy="1" r="1" transform="translate(5 16.544)" fill="#0065ff"/>
      <circle id="Ellipse_7-2" data-name="Ellipse 7" cx="1" cy="1" r="1" transform="translate(9 16.544)" fill="#0065ff"/>
      <circle id="Ellipse_8-2" data-name="Ellipse 8" cx="1" cy="1" r="1" transform="translate(9 8.544)" fill="#0065ff"/>
    </g>
    <g id="time_1_2" data-name="time (1)" transform="translate(280 438.005)">
      <path id="Path_21-2" data-name="Path 21" d="M254.167,423.186c-.214.053-.432.1-.648.14a1.042,1.042,0,1,0,.38,2.049c.258-.048.517-.1.772-.168a1.042,1.042,0,0,0-.5-2.022Z" transform="translate(-239.758 -401.404)" fill="#0065ff"/>
      <path id="Path_22-2" data-name="Path 22" d="M412.762,138.263a1.042,1.042,0,1,0,1.978-.655c-.082-.249-.174-.5-.271-.742a1.042,1.042,0,1,0-1.935.774C412.616,137.844,412.693,138.053,412.762,138.263Z" transform="translate(-391.398 -129.28)" fill="#0065ff"/>
      <path id="Path_23-2" data-name="Path 23" d="M321.953,394.084c-.184.121-.374.239-.565.349a1.042,1.042,0,1,0,1.041,1.805c.227-.131.453-.27.672-.415a1.042,1.042,0,0,0-1.149-1.739Z" transform="translate(-304.467 -373.789)" fill="#0065ff"/>
      <path id="Path_24-2" data-name="Path 24" d="M429.718,208.313a1.042,1.042,0,1,0-2.082.082c.009.22.011.443.006.663a1.042,1.042,0,1,0,2.083.046C429.73,208.841,429.728,208.575,429.718,208.313Z" transform="translate(-405.727 -196.72)" fill="#0065ff"/>
      <path id="Path_25-2" data-name="Path 25" d="M378.878,344.277a1.042,1.042,0,0,0-1.459.208c-.132.176-.272.35-.414.518a1.042,1.042,0,0,0,.118,1.469c.025.021.05.041.076.059a1.042,1.042,0,0,0,1.393-.177c.17-.2.336-.408.494-.619A1.042,1.042,0,0,0,378.878,344.277Z" transform="translate(-357.684 -326.423)" fill="#0065ff"/>
      <path id="Path_26-2" data-name="Path 26" d="M415.344,279.4a1.042,1.042,0,0,0-1.306.683c-.066.21-.139.421-.218.627a1.042,1.042,0,1,0,1.947.742c.094-.245.18-.5.259-.746A1.042,1.042,0,0,0,415.344,279.4Z" transform="translate(-392.618 -265.048)" fill="#0065ff"/>
      <path id="Path_27-2" data-name="Path 27" d="M10.094,23.759a9.71,9.71,0,0,1-2.445-.792l-.027-.015c-.183-.088-.365-.182-.542-.28h0a10.141,10.141,0,0,1-.949-.6A10.279,10.279,0,0,1,3.731,7.94,10,10,0,0,1,6.17,5.455l.03-.024A9.809,9.809,0,0,1,17.369,5.34l-.745,1.1c-.207.3-.08.528.283.495l3.235-.3a.542.542,0,0,0,.482-.711l-.864-3.19c-.1-.358-.346-.4-.554-.1l-.747,1.1a11.594,11.594,0,0,0-8.627-1.87q-.458.081-.9.2H8.924l-.034.01A11.747,11.747,0,0,0,2.371,6.529c-.014.017-.028.033-.041.051-.054.074-.108.15-.16.226-.086.125-.171.252-.252.38-.01.015-.018.031-.027.047A12.158,12.158,0,0,0,.013,14.3c0,.008,0,.016,0,.025.011.244.03.491.055.734a.1.1,0,0,0,.007.046q.041.367.1.735a12.073,12.073,0,0,0,3.318,6.5l.012.013h0a12.07,12.07,0,0,0,1.525,1.3,11.636,11.636,0,0,0,4.727,2.017.955.955,0,0,0,1.106-.785A.97.97,0,0,0,10.094,23.759Z" transform="translate(0 -1.688)" fill="#0065ff"/>
      <path id="Path_28-2" data-name="Path 28" d="M206.83,83.2a.843.843,0,0,0-.843.843v8.4l7.682,3.971a.844.844,0,0,0,.774-1.5l-6.77-3.5V84.041a.843.843,0,0,0-.843-.841Z" transform="translate(-195.805 -79.29)" fill="#0065ff"/>
    </g>
    <g id="feedback_1_2" data-name="feedback (1)" transform="translate(280.002 504.667)">
      <g id="Group_9-2" data-name="Group 9" transform="translate(0 21.333)">
        <path id="Path_6-2" data-name="Path 6" d="M107.256,129.18h12.988a.59.59,0,1,0,0-1.181H107.256a.59.59,0,1,0,0,1.181Z" transform="translate(-102.569 -122.566)" fill="#0065ff" stroke="#253858" stroke-width="0.3"/>
        <path id="Path_7-2" data-name="Path 7" d="M114.34,256h-7.084a.59.59,0,1,0,0,1.181h7.084a.591.591,0,0,0,0-1.181Z" transform="translate(-102.038 -244.047)" fill="#0065ff" stroke="#253858" stroke-width="0.3"/>
        <path id="Path_8-2" data-name="Path 8" d="M22.086,23.515c-.029,0-.057.008-.086.009v-.009a2.1,2.1,0,0,0-2-2.182H2a2.1,2.1,0,0,0-2,2.182V44.788a.548.548,0,0,0,.309.5A.459.459,0,0,0,.5,45.33a.48.48,0,0,0,.354-.16l4.854-5.3H20A2.1,2.1,0,0,0,22,37.7V28.649l1.439-1.57h0A2.2,2.2,0,0,0,24,25.6a2.009,2.009,0,0,0-1.914-2.085ZM21,37.7a1.049,1.049,0,0,1-1,1.091H5.5a.48.48,0,0,0-.354.16L1,43.471V23.515a1.049,1.049,0,0,1,1-1.091H20a1.049,1.049,0,0,1,1,1.091v.373a1.925,1.925,0,0,0-.268.239l-5.44,5.934H5.5a.548.548,0,0,0,0,1.091h8.99l-.48,2.62a.577.577,0,0,0,.137.493.48.48,0,0,0,.354.16.469.469,0,0,0,.1-.011l2.5-.545a.486.486,0,0,0,.256-.149L21,29.74Zm-4.247-4.866-1.615.353.323-1.764L20,26.468l1.293,1.411Zm5.979-6.523-.732.8L20.707,25.7l.733-.8a.884.884,0,0,1,.646-.291.96.96,0,0,1,.914,1,1.055,1.055,0,0,1-.268.7Z" transform="translate(0 -21.333)" fill="#0065ff" stroke="#253858" stroke-width="0.3"/>
      </g>
    </g>
    <g id="add-user-2" data-name="add-user" transform="translate(280 613.897)">
      <g id="Group_7-2" data-name="Group 7">
        <g id="Group_6-2" data-name="Group 6">
          <path id="Path_1-2" data-name="Path 1" d="M16.954,12.043a11.89,11.89,0,0,0-1.388-.566,6.4,6.4,0,0,0,2.515-5.1,6.273,6.273,0,1,0-12.547,0,6.4,6.4,0,0,0,2.522,5.1A11.932,11.932,0,0,0,4.19,13.64,12.266,12.266,0,0,0,.06,20.565a2.882,2.882,0,0,0,.57,2.386A2.768,2.768,0,0,0,2.8,24H14.16a.938.938,0,0,0,0-1.875H2.8a.933.933,0,0,1-.738-.357.991.991,0,0,1-.2-.821,10.237,10.237,0,0,1,9.7-8.2h.492a9.916,9.916,0,0,1,4.107.991.916.916,0,0,0,1.229-.444.944.944,0,0,0-.436-1.251ZM12.033,10.87h-.448a4.428,4.428,0,1,1,.448,0Z" fill="#0065ff" stroke="#fff" stroke-width="0.4"/>
        </g>
      </g>
      <g id="Group_8-2" data-name="Group 8" transform="translate(14.627 14.627)">
        <path id="Path_2-2" data-name="Path 2" d="M330.387,325.7h-2.713v-2.713a.987.987,0,0,0-1.973,0V325.7h-2.713a.987.987,0,0,0,0,1.973H325.7v2.713a.987.987,0,1,0,1.973,0v-2.713h2.713a.987.987,0,0,0,0-1.973Z" transform="translate(-322 -322)" fill="#0065ff" stroke="#fff" stroke-width="0.4"/>
      </g>
    </g>
    <g id="note-2" data-name="note" transform="translate(280.001 686)">
      <path id="Path_17-2" data-name="Path 17" d="M20.906,16H3.094A3.205,3.205,0,0,0,0,19.3V36.7A3.205,3.205,0,0,0,3.094,40H18.75a3.338,3.338,0,0,0,2.45-1.083l1.784-1.9A3.8,3.8,0,0,0,24,34.4V19.3A3.2,3.2,0,0,0,20.906,16ZM3.094,17.6H20.906A1.651,1.651,0,0,1,22.5,19.3v2.3H1.5V19.3a1.651,1.651,0,0,1,1.594-1.7ZM1.5,36.7V23.2h21V33.6H20.156A2.234,2.234,0,0,0,18,35.9v2.5H3.094A1.651,1.651,0,0,1,1.5,36.7Zm20.424-.817-1.784,1.9a1.96,1.96,0,0,1-.64.457V35.9a.68.68,0,0,1,.656-.7h2.2a2.109,2.109,0,0,1-.432.683Z" fill="#0065ff" stroke="#fff" stroke-width="0.2"/>
      <path id="Path_18-2" data-name="Path 18" d="M124.333,208.833a.77.77,0,0,0-.685-.833H112.685a.849.849,0,0,0,0,1.667h10.963a.77.77,0,0,0,.685-.834Z" transform="translate(-106.167 -182.429)" fill="#0065ff" stroke="#fff" stroke-width="0.2"/>
      <path id="Path_19-2" data-name="Path 19" d="M154.833,288h-10a.834.834,0,0,0,0,1.667h10a.833.833,0,1,0,0-1.667Z" transform="translate(-137.833 -258.44)" fill="#0065ff" stroke="#fff" stroke-width="0.2"/>
    </g>
    <g id="ticket_2_2" data-name="ticket (2)" transform="translate(206.461 736.431) rotate(-45)">
      <g id="Group_14-2" data-name="Group 14" transform="translate(14.028 106.848)">
        <g id="Group_13-2" data-name="Group 13">
          <rect id="Rectangle_1-2" data-name="Rectangle 1" width="1" height="1" fill="#0065ff" stroke="#0065ff" stroke-width="0.5"/>
        </g>
      </g>
      <g id="Group_15-2" data-name="Group 15" transform="translate(14.028 105.728)">
        <rect id="Rectangle_2-2" data-name="Rectangle 2" width="1" height="1" fill="#0065ff" stroke="#0065ff" stroke-width="0.5"/>
      </g>
      <g id="Group_17-2" data-name="Group 17" transform="translate(14.028 108.934)">
        <g id="Group_16-2" data-name="Group 16">
          <rect id="Rectangle_3-2" data-name="Rectangle 3" width="1" height="1" fill="#0065ff" stroke="#0065ff" stroke-width="0.5"/>
        </g>
      </g>
      <g id="Group_19-2" data-name="Group 19" transform="translate(14.028 111.001)">
        <g id="Group_18-2" data-name="Group 18">
          <rect id="Rectangle_4-2" data-name="Rectangle 4" width="1" fill="#0065ff" stroke="#0065ff" stroke-width="0.5"/>
        </g>
      </g>
      <g id="Group_21-2" data-name="Group 21" transform="translate(14.028 112.065)">
        <g id="Group_20-2" data-name="Group 20">
          <rect id="Rectangle_5-2" data-name="Rectangle 5" width="1" height="1" fill="#0065ff" stroke="#0065ff" stroke-width="0.5"/>
        </g>
      </g>
      <g id="Group_23-2" data-name="Group 23" transform="translate(14.028 114.152)">
        <g id="Group_22-2" data-name="Group 22">
          <rect id="Rectangle_6-2" data-name="Rectangle 6" width="1" height="1" fill="#0065ff" stroke="#0065ff" stroke-width="0.5"/>
        </g>
      </g>
      <g id="Group_24-2" data-name="Group 24" transform="translate(14.028 115.272)">
        <rect id="Rectangle_7-2" data-name="Rectangle 7" width="1" height="1" fill="#0065ff" stroke="#0065ff" stroke-width="0.5"/>
      </g>
      <g id="Group_26-2" data-name="Group 26" transform="translate(2.874 105.729)">
        <g id="Group_25-2" data-name="Group 25">
          <rect id="Rectangle_8-2" data-name="Rectangle 8" width="10" height="1" fill="#0065ff" stroke="#0065ff" stroke-width="0.5"/>
        </g>
      </g>
      <g id="Group_28-2" data-name="Group 28" transform="translate(2.874 115.271)">
        <g id="Group_27-2" data-name="Group 27">
          <rect id="Rectangle_9-2" data-name="Rectangle 9" width="10" height="1" fill="#0065ff" stroke="#0065ff" stroke-width="0.5"/>
        </g>
      </g>
      <g id="Group_30-2" data-name="Group 30" transform="translate(16.126 105.729)">
        <g id="Group_29-2" data-name="Group 29">
          <rect id="Rectangle_10-2" data-name="Rectangle 10" width="5" height="1" fill="#0065ff" stroke="#0065ff" stroke-width="0.5"/>
        </g>
      </g>
      <g id="Group_32-2" data-name="Group 32" transform="translate(16.126 115.271)">
        <g id="Group_31-2" data-name="Group 31">
          <rect id="Rectangle_11-2" data-name="Rectangle 11" width="5" height="1" fill="#0065ff" stroke="#0065ff" stroke-width="0.5"/>
        </g>
      </g>
      <g id="Group_34-2" data-name="Group 34" transform="translate(0 104)">
        <g id="Group_33-2" data-name="Group 33">
          <path id="Path_20-2" data-name="Path 20" d="M23.6,108.118a.406.406,0,0,0,.4-.412v-3.294a.406.406,0,0,0-.4-.412H.4a.406.406,0,0,0-.4.412v3.294a.406.406,0,0,0,.4.412,2.884,2.884,0,0,1,0,5.765.406.406,0,0,0-.4.412v3.294A.406.406,0,0,0,.4,118H23.6a.406.406,0,0,0,.4-.412v-3.294a.406.406,0,0,0-.4-.412,2.884,2.884,0,0,1,0-5.765Zm-3.577,3.3a3.654,3.654,0,0,0,3.177,3.27v2.494H.8v-2.494a3.682,3.682,0,0,0,3.177-4.1A3.654,3.654,0,0,0,.8,107.317v-2.494H23.2v2.494a3.682,3.682,0,0,0-3.177,4.1Z" transform="translate(0 -104)" fill="#0065ff" stroke="#0065ff" stroke-width="0.5"/>
        </g>
      </g>
    </g>
    <g id="list-2" data-name="list" transform="translate(232.96 884.005)">
      <g id="Group_11-2" data-name="Group 11" transform="translate(47.037)">
        <g id="Group_10-2" data-name="Group 10">
          <path id="Path_9-2" data-name="Path 9" d="M64.492,3.145h-2.3V2.173c0-.314-.364-.457-.7-.457H59.7A2.714,2.714,0,0,0,57.007,0a2.761,2.761,0,0,0-2.7,1.714H52.552c-.333,0-.667.143-.667.457v.971h-2.3A2.512,2.512,0,0,0,47.04,5.456v16.37A2.387,2.387,0,0,0,49.582,24h14.91a2.387,2.387,0,0,0,2.545-2.171V5.459a2.512,2.512,0,0,0-2.545-2.314ZM53.1,2.859h1.667a.651.651,0,0,0,.576-.514A1.736,1.736,0,0,1,57.01,1.059a1.7,1.7,0,0,1,1.636,1.286.654.654,0,0,0,.606.514h1.727V5.145H53.1Zm12.727,18.97c0,.629-.667,1.028-1.333,1.028H49.582c-.667,0-1.333-.4-1.333-1.028V5.459a1.333,1.333,0,0,1,1.333-1.171h2.3V5.744a.626.626,0,0,0,.667.543h8.939a.657.657,0,0,0,.7-.543V4.287h2.3a1.333,1.333,0,0,1,1.333,1.171v16.37Z" transform="translate(-47.037)" fill="#0065ff" stroke="#253858" stroke-width="0.2"/>
          <path id="Path_10-2" data-name="Path 10" d="M104.025,230.5a.608.608,0,0,0-.851-.03l-1.945,1.854-.821-.851a.608.608,0,0,0-.851-.03.638.638,0,0,0,0,.881L100.8,233.6a.547.547,0,0,0,.425.182.608.608,0,0,0,.425-.182l2.371-2.249a.577.577,0,0,0,.035-.816A.355.355,0,0,0,104.025,230.5Z" transform="translate(-96.348 -217.844)" fill="#0065ff" stroke="#253858" stroke-width="0.2"/>
          <path id="Path_11-2" data-name="Path 11" d="M206.146,256.034h-6.99a.608.608,0,1,0,0,1.216h6.99a.608.608,0,1,0,0-1.216Z" transform="translate(-189.778 -242.078)" fill="#0065ff" stroke="#253858" stroke-width="0.2"/>
          <path id="Path_12-2" data-name="Path 12" d="M104.025,146.909a.608.608,0,0,0-.851-.03l-1.945,1.854-.821-.851a.608.608,0,0,0-.851-.03.638.638,0,0,0,0,.881l1.246,1.276a.547.547,0,0,0,.425.182.608.608,0,0,0,.425-.182l2.371-2.249a.577.577,0,0,0,.035-.816Q104.043,146.926,104.025,146.909Z" transform="translate(-96.348 -138.777)" fill="#0065ff" stroke="#253858" stroke-width="0.2"/>
          <path id="Path_13-2" data-name="Path 13" d="M206.146,172.442h-6.99a.608.608,0,1,0,0,1.216h6.99a.608.608,0,0,0,0-1.216Z" transform="translate(-189.778 -163.042)" fill="#0065ff" stroke="#253858" stroke-width="0.2"/>
          <path id="Path_14-2" data-name="Path 14" d="M104.025,314.092a.608.608,0,0,0-.851-.03l-1.945,1.854-.821-.851a.608.608,0,0,0-.851-.03.638.638,0,0,0,0,.881l1.246,1.276a.547.547,0,0,0,.425.182.608.608,0,0,0,.425-.182l2.371-2.249a.577.577,0,0,0,.035-.816Q104.043,314.109,104.025,314.092Z" transform="translate(-96.348 -297.174)" fill="#0065ff" stroke="#253858" stroke-width="0.2"/>
          <path id="Path_15-2" data-name="Path 15" d="M206.146,339.626h-6.99a.608.608,0,1,0,0,1.216h6.99a.608.608,0,1,0,0-1.216Z" transform="translate(-189.778 -321.113)" fill="#0065ff" stroke="#253858" stroke-width="0.2"/>
        </g>
      </g>
    </g>
  </g>
</svg>
