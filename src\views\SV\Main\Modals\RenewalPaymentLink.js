import { Grid, Button, Input, Typography } from "@mui/material";
import React, { useEffect, useState } from "react";
import { useSnackbar } from "notistack";
import { SelectDropdown, TextInput } from '../../../../components';
import { useSelector } from "react-redux";
import { InsertRenewalPaymentDetails, GetRenewalDetails } from "../../../../services/Common";
import rootScopeService from "../../../../services/rootScopeService";
import { CALL_API } from "../../../../services/api.service";
import MultipleSelectDropdown from "../../../../components/MultipleSelectDropdown";
import './RenewalPaymentLink.scss';
import './UploadButton.scss';

let RenewalInfo = {
    InsurerName: '',
    ProposerName: '',
    PolicyNo: '',
    PremiumAmount: 0,
    PehchanID: '',
    PaymentReason: ''
};

const Reasons = [
    { "Id": 1, "Name": "1 Year" }
    , { "Id": 2, "Name": "2 Year" }
    , { "Id": 3, "Name": "3 Year" }
    , { "Id": 4, "Name": "SI Increase" }
    , { "Id": 5, "Name": "Member Addition" }
    , { "Id": 6, "Name": "Member Deletion" }
    , { "Id": 7, "Name": "Proposer Change" }
    , { "Id": 8, "Name": "Rider Addition" }
    , { "Id": 9, "Name": "Rider Removed" }
    , { "Id": 10, "Name": "DOB Correction" }
    , { "Id": 11, "Name": "CJ Not Available" }
    , { "Id": 12, "Name": "CJ Not Working" }
    , { "Id": 13, "Name": "SI Decrease" }
    , { "Id": 14, "Name": "Address Change" }
    , { "Id": 15, "Name": "Name Correction" }
    , { "Id": 16, "Name": "PED Addition" }
    , { "Id": 17, "Name": "Wellness Discount" }
    , { "Id": 18, "Name": "Member Split" }
    , { "Id": 19, "Name": "Split Payment" }
    , { "Id": 20, "Name": "Contact Change" }
    , { "Id": 21, "Name": "Policy Merge" }
    , { "Id": 22, "Name": "Website Discount" }
    , { "Id": 23, "Name": "Others" }
];

const MultipleReasonsType = [

    ...Reasons
];

const RenewalPaymentLink = (props) => {
    const { enqueueSnackbar } = useSnackbar();
    const [RenewalLead, setRenewalLead] = useState(0);
    var allLeads = useSelector(state => state.salesview.allLeads);
    const [RenewDetails, setRenewDetails] = useState(undefined);
    const [RenewDetailsList, setRenewDetailsList] = useState(RenewalInfo);
    const [visibleLeads, setvisibleLeads] = useState([]);
    const [reasonvisible, setreasonvisible] = useState(false);
    const [isPehchanDisabled, setisPehchanDisabled] = useState(false);
    const [PaymentReason, setPaymentReason] = React.useState([]);
    const [Premiums, setPremiums] = useState([]);
    const [IsPremiumDisabled, setIsPremiumDisabled] = useState(true);
    const [PremiumId, setPremiumId] = useState(0);
    const [UploadedFile, setUploadedFile] = useState(undefined);

    const ResetValues = () => {
        setRenewDetailsList(RenewalInfo)
        setRenewalLead(0)
        setPaymentReason([])
        setUploadedFile(undefined)
        setreasonvisible(false)
    }

    const GetRenewDetails = (LeadId) => {
        GetRenewalDetails(LeadId).then((result) => {
            if (result) {
                setRenewDetails(result);
            }
        }).catch((e) => {
            console.log(e);
        })
    }

    const handleChange = (e) => {
        const { name, value, type } = e.target;
        if (name === "LeadId") {
            setPremiums([]);
            setRenewalLead(value);
            setRenewDetailsList(RenewalInfo)
        }
        else if (name === "NoticePremium") {
            setPremiumId(Premiums[value].id);
            setRenewDetailsList({ ...RenewDetailsList, ['PremiumAmount']: parseFloat(Premiums[value].value) });
            if (Premiums[value].id === 3) {
                setIsPremiumDisabled(false);
            }
            else {
                setIsPremiumDisabled(true);
            }
        }
        else if (name === "PremiumAmount") {
            setRenewDetailsList({ ...RenewDetailsList, ['PremiumAmount']: parseFloat(value.replace(/[^0-9.]/g, '')) });
        }
        else {
            setRenewDetailsList({ ...RenewDetailsList, [name]: value });
        }
    }

    const handleMultiple = (e) => {
        const { name, value, type } = e.target;

        if (name == 'PaymentReasonList') {
            let arr = value;
            if (arr.some(item => item.Id === 23)) {
                setreasonvisible(true)
            }
            else {
                setreasonvisible(false)
            }
            setPaymentReason(arr)
        }
    };

    const SetRenewalPaymentDetails = () => {
        let reason = PaymentReason
            .filter(item => item.Name && item.Name.trim() !== '' && item.Name.trim() !== '1 Year' && item.Name.trim() !== '2 Year' && item.Name.trim() !== '3 Year' && item.Name.trim() !== 'Others')
            .map(item => item.Name)
            .join(', ');
        if ([0, 1, 2].indexOf(PremiumId) !== -1 && PaymentReason.some(item => item.Id !== 23)) {
            if (PremiumId == 0) { reason = reason != '' ? reason + ', 1 Year' : reason + '1 Year' }
            if (PremiumId == 1) { reason = reason != '' ? reason + ', 2 Year' : reason + '2 Year' }
            if (PremiumId == 2) { reason = reason != '' ? reason + ', 3 Year' : reason + '3 Year' }
        }
        if (RenewDetailsList.PremiumAmount > 500 && RenewalLead > 0) {
            let requestData = {
                "LeadId": RenewalLead,
                "PaymentReason": RenewDetailsList.PaymentReason != '' ? reason + ', ' + RenewDetailsList.PaymentReason : reason,
                "PremiumAmount": RenewDetailsList.PremiumAmount,
                "UploadedFile": UploadedFile
            }
            InsertRenewalPaymentDetails(requestData).then((result) => {
                if (result && result.ErrorCode == 2) {
                    enqueueSnackbar(result.Data, { variant: 'success', autoHideDuration: 3000, });
                }
                else if (result && result.ErrorCode == 1) {
                    enqueueSnackbar(result.Data, { variant: 'error', autoHideDuration: 3000, });
                }
                else {
                    enqueueSnackbar("Error", { variant: 'error', autoHideDuration: 3000, });
                }

            }).catch((e) => {
                console.log(e);
            })
            ResetValues()
        }
        else {
            enqueueSnackbar("Please select Amount > 500", { variant: 'error', autoHideDuration: 3000, });
        }
    }

    const GetHealthPehchanID = () => {
        const input = {
            url: `coremrs/api/LeadDetails/GetHealthNeedAnalysis/` + rootScopeService.getCustomerId() + '/' + RenewalLead + '/2',
            method: 'GET', service: 'MatrixCoreAPI'
        };
        CALL_API(input).then((response) => {
            response = response.Data;
            if (response) {
                setRenewDetailsList({ ...RenewDetailsList, ['PehchanID']: response.PehchanID });
                setisPehchanDisabled(true);
            }
            else {
                setisPehchanDisabled(false);
            }
        }, function () {
        });
    };

    const SubmitRequest = () => {
        SetRenewalPaymentDetails();
    }

    useEffect(() => {
        let LeadList = [];
        for (let key in allLeads) {
            if (allLeads[key].LeadSource === "Renewal"
                && allLeads[key].NoticePremium > 100
                && [1, 2, 3, 4, 11].indexOf(allLeads[key].StatusId) !== -1
                && !LeadList.includes(allLeads[key].LeadID)) {
                LeadList.push(allLeads[key].LeadID)
            }
        }
        if (LeadList.length === 1) {
            setRenewalLead(LeadList[0]);
        }
        else if (LeadList.length === 0) {
            LeadList.push("No Valid Leads")
        }
        else {
            setRenewalLead(LeadList[0]);
        }
        setvisibleLeads(LeadList);
    }, []);

    useEffect(() => {
        if (RenewalLead > 0) {
            GetHealthPehchanID()
            GetRenewDetails(RenewalLead)
        }
    }, [RenewalLead])

    useEffect(() => {
        let PremiumList = [];
        if (RenewDetails !== undefined && RenewDetails !== null) {
            PremiumList.push({ id: 0, value: RenewDetails.Premium, label: "One Year Premium" });
            PremiumList.push({ id: 1, value: RenewDetails.TwoYrPremium, label: "Two Year Premium" });
            PremiumList.push({ id: 2, value: RenewDetails.ThreeYrPremium, label: "Three Year Premium" });
            PremiumList.push({ id: 3, value: 0, label: "Custom" });
            setPremiumId(PremiumList[0].id);
            setPremiums(PremiumList)
            setIsPremiumDisabled(true);
            setRenewDetailsList({
                ...RenewDetailsList
                , ['PremiumAmount']: PremiumList[0].value
                , ['InsurerName']: RenewDetails.SupplierName
                , ['ProposerName']: RenewDetails.ProposerName
                , ['PolicyNo']: RenewDetails.OldPolicyNo
            });
        }
    }, [RenewDetails]);

    const onFileChange = (event) => {
        let file = event.target.files[0];
        let fileName = file.name;
        const maxSize = 1 * 1024 * 1024; // 1MB in bytes

        if (file.size > maxSize) {
            enqueueSnackbar("File size should not exceed 1MB", { variant: 'error', autoHideDuration: 3000, });
            return;
        }

        if (isImage(fileName)) {
            setUploadedFile(file)
        }
        else {
            enqueueSnackbar("Please upload an image file.", { variant: 'error', autoHideDuration: 3000, });
        }
    };

    const isImage = (filename) => {
        var ext = getExtension(filename);
        switch (ext.toLowerCase()) {
            case "png":
            case "jpg":
            case "jpeg":
                return true;
            default:
                return false;
        }
    }

    const getExtension = (filename) => {
        var parts = filename.split(".");
        return parts[parts.length - 1];
    }

    return (
        <>
            {RenewDetailsList && <>


                <Grid container spacing={3} className="mt-3">
                    <SelectDropdown
                        name="LeadId"
                        label="Select LeadId"
                        value={RenewalLead}
                        options={visibleLeads}
                        labelKeyInOptions='_all'
                        valueKeyInOptions='_all'
                        handleChange={handleChange}
                        show={true}
                        sm={12} md={6} xs={12}
                    />
                    <TextInput
                        name="InsurerName"
                        label="Insurer Name"
                        value={RenewDetailsList.InsurerName}
                        disabled='true'
                        className="custom-disabled-input"
                        sm={12} md={6} xs={12}
                    />
                    <TextInput
                        name="ProposerName"
                        label="Proposer Name"
                        value={RenewDetailsList.ProposerName}
                        disabled='true'
                        className="custom-disabled-input"
                        sm={12} md={6} xs={12}
                    />
                    <SelectDropdown
                        name="NoticePremium"
                        label="Premium"
                        value={PremiumId}
                        options={Premiums}
                        labelKeyInOptions='label'
                        valueKeyInOptions='id'
                        handleChange={handleChange}
                        show={true}
                        sm={12} md={6} xs={12}
                    />
                    <TextInput
                        name="PremiumAmount"
                        label="Premium Amount"
                        type="text"
                        value={RenewDetailsList.PremiumAmount}
                        disabled={IsPremiumDisabled}
                        className={IsPremiumDisabled ? "custom-disabled-input" : ""}
                        handleChange={handleChange}
                        sm={12} md={6} xs={12}
                    />
                    <TextInput
                        name="PolicyNo"
                        label="Policy Number"
                        value={RenewDetailsList.PolicyNo}
                        disabled='true'
                        className="custom-disabled-input"
                        sm={12} md={6} xs={12}
                    />
                    <TextInput
                        name="PehchanID"
                        label="Pehchan ID"
                        value={RenewDetailsList.PehchanID}
                        disabled={isPehchanDisabled}
                        handleChange={handleChange}
                        className={isPehchanDisabled ? "custom-disabled-input" : ""}
                        sm={12} md={6} xs={12}
                    />
                    <MultipleSelectDropdown
                        name="PaymentReasonList"
                        label="Payment Reason"
                        value={PaymentReason}
                        options={MultipleReasonsType}
                        labelKeyInOptions='Name'
                        valueKeyInOptions='_all'
                        handleChangeMultiple={handleMultiple}
                        renderValue={(selected) => selected.map((s) => s.Name).join(", ")}
                        show={true}
                        sm={12} md={6} xs={12}
                    />
                    <TextInput
                        name="PaymentReason"
                        label="Changes/Reason"
                        value={RenewDetailsList.PaymentReason}
                        handleChange={handleChange}
                        sm={12} md={6} xs={12}
                        show={reasonvisible}
                    />
                    <Grid item sm={12} md={12} xs={12}>
                     <Typography variant="caption" >
                        Upload Attachment :
                    </Typography>
                    <input
                        type="file"
                        id="hiddenFileInput"
                        onChange={onFileChange}
                        style={{ display: "none" }}
                    />
                    <label htmlFor="hiddenFileInput">
                        <Button variant="outlined" component="span" className="button-txt" >
                            Upload File
                        </Button>
                    </label>
                    {UploadedFile && UploadedFile.name ?
                        <>
                            <Typography variant="caption">
                                Uploaded Attachment :
                            </Typography>
                            <Typography>
                                {UploadedFile.name}
                            </Typography>
                        </>
                        : <></>
                    }
                    </Grid>
                     <Grid item sm={12} md={12} xs={12}>
                        <Button onClick={SubmitRequest} variant="outlined" className="GenerateBtn" >
                            Submit Request
                        </Button>
                    </Grid>
                </Grid>

              


                <p className="Note">Note : Please note that the generated link will be send to the customer on the primary contact details</p>

            </>
            }
        </>

    );
}
export default RenewalPaymentLink