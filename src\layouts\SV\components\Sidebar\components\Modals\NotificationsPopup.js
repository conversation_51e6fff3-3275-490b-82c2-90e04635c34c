import React from 'react';
import { useSelector } from 'react-redux';
import ModalPopup from '../../../../../../components/Dialogs/ModalPopup';
import Notifications from '../Notifications';
import {localStorageCache} from '../../../../../../../src/utils/utility';
import { getMongoNotificationData } from '../../../../../../helpers/commonHelper';

const NotificationsPopup = (props) => {
    let { open, handleClose } = props;
    let notificationDatafromRedux = useSelector(state => state.salesview.notificationData);
    let notificationDataNewFromRedux = useSelector(state => state.salesview.notificationDataNew);
    let showNewNotificationPanel = useSelector(state => state.salesview.showNewNotificationPanel);

    const GetUnreadNotifications = () => {
        let count = 0;
        // if(notificationDatafromRedux && notificationDatafromRedux.length > 0)
        // {
        //   count += Array.isArray(notificationDatafromRedux) ? notificationDatafromRedux.filter(item => item.IsRead !== true).length : 0
        // }
        // if(showNewNotificationPanel && notificationDataNewFromRedux && notificationDataNewFromRedux.length > 0)
        // {
        //   count += Array.isArray(notificationDataNewFromRedux) ? notificationDataNewFromRedux.filter(item => item.IsRead !== true).length : 0
        // }
        let MongoNotification = getMongoNotificationData();
        count = Array.isArray(MongoNotification) ? 
        MongoNotification.filter(item => item.IsRead !== true).length 
        : 0;
        return count;
    };

    if (!open) return null;
    return (

        <ModalPopup open={open}
            title={`Notifications (${GetUnreadNotifications()})`}
            handleClose={handleClose}
            className="notification-block"
        >
            <Notifications handleClose={handleClose}/>
        </ModalPopup >

    );
}
export default NotificationsPopup;