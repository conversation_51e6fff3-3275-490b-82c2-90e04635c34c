/* eslint-disable react-hooks/rules-of-hooks */
import React, { useEffect, useState } from "react";
import { Button, CircularProgress, Grid, Tooltip, useMediaQuery, useTheme } from '@mui/material';
import { ContinueJourneyPopUp } from "./Modals/ContinueJourneyPopUp";
import { NewContinueJourneyPopUp } from "./Modals/NewContinueJourneyPopUp";
import { PortDetailsPopUp } from "./Modals/PortDetailsPopUp";
import { EditPopUp } from "./Modals/EditPopUp";
import { CONFIG, SV_CONFIG } from '../../../appconfig/app.config'
import User from "../../../services/user.service";
import { CALL_API } from "../../../services";
import rootScopeService from "../../../services/rootScopeService";
import { RejectLeadPopUp } from "./Modals/RejectLeadPopUp";
import { UploadDocPopUp } from "./Modals/UploadDocPopUp";
import { useSnackbar } from "notistack";
import { useDispatch, useSelector } from "react-redux";
import ErrorBoundary from "../../../hoc/ErrorBoundary/ErrorBoundary";
import dayjs from "dayjs";
import ProposalDetails from "./Modals/ProposalDetails";
import CallTransferModal from "../RightBlock/Modals/CallTransferModal";
import Common, { IsLeadContent, SetCustomerComment, IsDiscountOptionAvailable, IsCustomerAccess, GetTLCallingNo, createCallTranserUrl, API_STATUS, OpenLeadContentOnClick, IsApptfeedbackLead, ExecuteReOpenLeadV2,GetHealthRenewalContinueLink, IsValidUserGroup } from "../../../services/Common";
import { ViewQuotesPopup } from "./Modals/ViewQuotesPopup";
import Slider from "react-slick";
import { RenewalDetailsPopup } from "./Modals/RenewalDetailsPopup";
import { PreExistingDiseasePopup } from "./Modals/PreExistingDiseasePopup";
import { PlanPortDetailsPopUp } from "./Modals/PlanPortDetailsPopUp";
import { setOpenRightBarMenu, updateStateInRedux } from "../../../store/actions/SalesView/SalesView";
import { ClaimDetailsPopup } from "./Modals/ClaimDetailsPopup";
import { PaymentDetailsPopup } from "./Modals/PaymentDetailsPopup";
import { format } from 'date-fns'
import { checkShowNewChat, setPosCookie } from "../../../helpers/commonHelper";
import { ChkIsFresh, gaEventTracker } from '../../../../src/helpers/index';
import { AvailDiscountPopup } from "./Modals/AvailDiscountPopup";
import { SmartInvestmentDetails } from "./Modals/SmartInvestmentDetails";
import { AppointmentSummaryPopup } from "./Modals/AppointmentSummaryPopup";
import { ImagePopup } from "./Modals/ImagePopup";
import TransferTypes from "../../../assets/json/TransferTypes";
// import SyncAltIcon from '@mui/icons-material/SyncAlt';
import { GenericModalPopup } from "./Modals/GenericModalPopup";
import { TermSpecialCustomer } from "./Modals/TermSpecialCustomer";
import { LastYearPolicyDetailsPopup } from "./Modals/LastYearPolicyDetailsPopup";
import { HdfcPasaPopup } from "./Modals/HdfcPasaPopup";
import AvailDiscounts from "./Modals/AvailDiscounts";
import { AnualOpenLeadsPanel } from "./Modals/AnualOpenLeadsPanel";
import ReferralDetailsPopup from "./ReferralDetailsPopup";
import CovidInvPopup from "./CovidInvPopup";
import StaticData, { default as Data } from "../../../../src/assets/json/StaticData";
import PotentialRepeatBuyerPopUp from "./Modals/PotentialRepeatBuyerPopUp";
import { TermSpecialNRICustomer } from "./Modals/TermSpecialNRICustomer";
import { LeadAdditionalInfoPopup } from "./Modals/LeadAdditionalInfoPopup";
import { RejectedLeadDetailsPopup } from "./Modals/RejectedLeadDetailsPopup";
import { PitchRecommendationPopup } from "./Modals/PitchRecommendationPopup";

const GridRow = (props) => {
  // let { label, value, show, title, landScapeViewRequired } = props;
  let { label, value, show, title, className } = props;
  if (!title) { title = value };

  if (!show) return null;
  return (
    // <Grid container item xs={landScapeViewRequired ? 4 : 12} className="GridRow customercard-details">
    <Grid container item xs={12} className={`GridRow customercard-details ${className}`}>
      <div>{label}</div>
      <div>
        <span title={title}>{value === undefined ? "N/A" : value}</span>
      </div>
    </Grid>
  )

}

export const getProductIconText = (ProductId, SubProductTypeId = null, PlanFeatureType = "") => {
  if (ProductId === 115 && PlanFeatureType && PlanFeatureType.toUpperCase() === 'T') {
    return "Trad";
  }

  if (ProductId === 115 && SubProductTypeId) {
    switch (SubProductTypeId) {
      case 3: // 'Child'
        return "Child";
      default:
        return null;
    }
  }
}

export const getProductIcon = (ProductId, IsPrimary, SubProductTypeId = null, PlanFeatureType = "") => {
  if (IsPrimary) return CONFIG.PUBLIC_URL + "/images/salesview/staricon.svg";
  if (ProductId === 131 && SubProductTypeId) {
    switch (SubProductTypeId) {

      case 1:
        return CONFIG.PUBLIC_URL + "/images/salesview/producticons/group_health_insure.svg";
      case 2:
        return CONFIG.PUBLIC_URL + "/images/salesview/producticons/group_accident.svg";
      case 3:
        return CONFIG.PUBLIC_URL + "/images/salesview/producticons/group_term.svg";
      // case 4: //group travel
      //   return CONFIG.PUBLIC_URL + "/images/salesview/producticons/directors_liability.svg";
      case 5:
        return CONFIG.PUBLIC_URL + "/images/salesview/producticons/fire_bulgary.svg";
      // case 6: // bulgary
      //   return CONFIG.PUBLIC_URL + "/images/salesview/producticons/fire_bulgary.svg";
      case 7:
        return CONFIG.PUBLIC_URL + "/images/salesview/producticons/office_package.svg";
      case 8:
        return CONFIG.PUBLIC_URL + "/images/salesview/producticons/shop_owner.svg";
      // case 9:
      //   return CONFIG.PUBLIC_URL + "/images/salesview/producticons/fire_bulgary.svg";
      // case 10: //key man insurance
      //   return CONFIG.PUBLIC_URL + "/images/salesview/producticons/fire_bulgary.svg";
      case 12:
        return CONFIG.PUBLIC_URL + "/images/salesview/producticons/general_liability.svg";
      case 14:
        return CONFIG.PUBLIC_URL + "/images/salesview/producticons/professional_indemnity.svg";
      case 15:
        return CONFIG.PUBLIC_URL + "/images/salesview/producticons/directors_liability.svg";
      case 16:
        return CONFIG.PUBLIC_URL + "/images/salesview/producticons/construction_all_risk.svg";
      case 17:
        return CONFIG.PUBLIC_URL + "/images/salesview/producticons/ear.svg";
      case 18:
        return CONFIG.PUBLIC_URL + "/images/salesview/producticons/plant_machine.svg";
      case 19:
        return CONFIG.PUBLIC_URL + "/images/salesview/producticons/workman_compensation.svg";
      case 21:
        return CONFIG.PUBLIC_URL + "/images/salesview/producticons/cyber_security.svg";
      case 33:
        return CONFIG.PUBLIC_URL + "/images/salesview/producticons/covid_group_health.svg";
      default:
        return CONFIG.PUBLIC_URL + "/images/logo_mob.png";
    }
  }
  if (ProductId === 115 && PlanFeatureType && PlanFeatureType.toUpperCase() === 'T') {
    // Trad
    return CONFIG.PUBLIC_URL + "/images/salesview/producticons/investment/tradProcess.png";
  }
  if (ProductId === 115 && SubProductTypeId) {
    switch (SubProductTypeId) {

      case 1: // 'ULIP'
        return CONFIG.PUBLIC_URL + "/images/salesview/producticons/investment/ULIP.svg";
      case 2: // 'Retirement'
        return CONFIG.PUBLIC_URL + "/images/salesview/producticons/investment/Retirement.svg";
      case 3: // 'Child'
        return CONFIG.PUBLIC_URL + "/images/salesview/producticons/investment/ChildSavingPlan.png";
      case 5: // Tax Saving
        return CONFIG.PUBLIC_URL + "/images/salesview/producticons/investment/TaxSaving.svg";
      default:
        return CONFIG.PUBLIC_URL + "/images/salesview/producticons/investment.svg"
    }
  }

  switch (ProductId) {
    case 2:
      return CONFIG.PUBLIC_URL + "/images/salesview/producticons/health.svg";
    case 7:
      return CONFIG.PUBLIC_URL + "/images/salesview/termicon.svg";
    case 101:
      return CONFIG.PUBLIC_URL + "/images/salesview/producticons/home.svg";
    case 115:
      return CONFIG.PUBLIC_URL + "/images/salesview/producticons/investment.svg";
    case 117:
      return CONFIG.PUBLIC_URL + "/images/salesview/Car.svg";
    default:
      return CONFIG.PUBLIC_URL + "/images/logo_mob.png";
  }
}
const verticalSliderSettings = {
  infinite: true,
  slidesToShow: 1,
  slidesToScroll: 1,
  autoplay: true,
  // speed: 2000,
  autoplaySpeed: 2000,
  cssEase: "linear",
  vertical: true,
  verticalSwiping: true,
  nextArrow: <></>,
  prevArrow: <></>
};


export default function LeadCard(props) {
  const { enqueueSnackbar } = useSnackbar();
  const { lead } = props;
  const dispatch = useDispatch();
  const [IsShowPSUAndPVT, setIsShowPSUAndPVT] = useState(false);
  const [OpenEditPopUp, setOpenEditPopUp] = useState(false);
  const [OpenUploadDocPopUp, setOpenUploadDocPopUp] = useState(false);
  const [OpenRejectLeadPopUp, setRejectLeadPopUp] = useState(false);
  const [OpenProposalDetails, setOpenProposalDetails] = useState(false);
  const [OpenCallTranserModal, setOpenCallTranserModal] = useState(false);
  const [OpenViewQuotesPopup, setOpenViewQuotesPopup] = useState(false);
  const [CallTransferUrl, setCallTransferUrl] = useState(null);
  const [OpenContinueLink, setOpenContinueLink] = useState(false);
  const [OpenNewContinueLink, setOpenNewContinueLink] = useState(false);
  const [OpenPortDetailsPopUp, setOpenPortDetailsPopUp] = useState(false);
  const [OpenClaimsPopup, setOpenClaimsPopup] = useState(false);
  const [allowActionInSMEquotes, setAllowActionInSMEquotes] = useState(false);
  const [OpenRenewalDetails, setOpenRenewalDetails] = useState(false);
  const [preExistingDiseasePopUp, setPreExistingDiseasePopUp] = useState(false);
  const [planPortDetailsPopUp, setplanPortDetailsPopUp] = useState(false);
  const [OpenPaymentDetailsPopUp, setPaymentDetailsPopUp] = useState(false);
  const [ShowFreshJourney, setShowFreshJourney] = useState(false);
  const [RejectionStatus, setRejectionStatus] = useState([5, 6, 7, 12, 14]);
  const [ResizeEdit, setResizeEdit] = useState(false);
  const [ShowAvailDiscountIcon, setShowAvailDiscountIcon] = useState(false);
  const [OpenAvailDiscountPopUp, setOpenAvailDiscountPopUp] = useState(false);
  const [OpenAdditionalInfoPopup, setOpenAdditionalInfoPopup] = useState(false);
  const [OpenLastYearPolicyDetailsPopup, setOpenLastYearPolicyDetailsPopup] = useState(false);
  const [OpenCovidInv, setOpenCovidInv] = useState(false);
  const [OpenRejectedLeadPopup, setOpenRejectedLeadPopup] = useState(false);

  const [OpenApptSummaryPopup, setOpenApptSummaryPopup] = useState(false);
  const [OpenPitchRecommendationPopup, setOpenPitchRecommendationPopup] = useState(false);
  const [OpenImgPopup, setOpenImgPopup] = useState(false);
  const [ProfessionTypePopUp, setProfessionTypePopUp] = useState(false);
  const [TermSpecialCustomerPopUp, setTermSpecialCustomerPopUp] = useState(false);
  const [TermSpecialNRICustomerPopUp, setTermSpecialNRICustomerPopUp] = useState(false);
  const [ShowMaxTermPopUp, setShowMaxTermPopUp] = useState(false);
  const [ShowAnualOpenPopup, setShowAnualOpenPopup] = useState(false);
  const [ImgUrl, setImgUrl] = useState("");
  const [OpenSmartInvestmentToolDetailsPopUp, setOpenSmartInvestmentToolDetailsPopUp] = useState(false);
  const [apptDetails, setApptDetails] = useState(null);
  const [DroneCategoryName, setDroneCategoryName] = useState("");
  const [DroneModelName, setDroneModelName] = useState("");
  const [DroneUASTypeName, setDroneUASTypeName] = useState("");

  let [OpenReferralDetails, setOpenReferralDetails] = useState(false);
  const isHighIntentLead = useSelector((state) => state.salesview.HighIntentLead);
  const issuedPolicies = useSelector((state) => state.salesview.issuedPolicies) || [];
  const CustAppmentSmmary = useSelector(({ salesview }) => salesview.CustomerAppointmentSummary);
  const GetCustAppointSummaryAPIStatus = useSelector(({ salesview }) => salesview.GetCustAppointSummaryAPIStatus);
  const TotalCallTT = useSelector(({ salesview }) => salesview.TotalCallTT);
  const ShowAssignCriticalComponents = useSelector(state => state.salesview.ShowAssignCriticalComponents);
  const ShowSmeCustomerType = useSelector(state => state.salesview.ShowSmeCustomerType);
  const UASTypeList = useSelector(({ salesview }) => salesview.UASTypeList);
  const DroneModelList = useSelector(({ salesview }) => salesview.DroneModelList);
  let SmePotentialBuyerSubProducts = SV_CONFIG["SmePotentialBuyerSubProducts"] ? SV_CONFIG["SmePotentialBuyerSubProducts"] : [];
  const MarineAnualOpenLeadData = useSelector(state => state.salesview.MarineAnualOpenLeadData);

  let invalids = [undefined, null, "", false];
  let ProductId = rootScopeService.getProductId();
  let IntProducts = SV_CONFIG["IntProducts"][SV_CONFIG["environment"]];
  let healthproducts = SV_CONFIG["healthproducts"];
  let [IsShowContjourneylink, setIsShowContjourneylink] = useState(false)
  let IsBooked = lead.StatusId >= 13;
  let TWLeadErrorType = '';
  let UTMSourceIntentMapping = ['organic', 'google', 'google_brand'];
  let PaisaBazaarUTMSource = ['paisa_homepage', 'bureau_crosssell_policy_health_insurance', 'paisa_crm', 'paisabazaar'];
  if (ProductId === 114) {
    switch (lead.TWLeadErrorType) {
      case '1':
        TWLeadErrorType = 'Proposal Error';
        break;
      case '2':
        TWLeadErrorType = 'Payment not attempted';
        break;
      case '3':
        TWLeadErrorType = 'Payment has failed';
        break;
      default:
        break;
    }
  }
  let IsViewDetailsVisible = (IntProducts.includes(ProductId) || [117, 131, 3, 114, 116, 101, 139, 179].includes(ProductId))
    || (healthproducts.includes(ProductId) && lead.LeadSource !== 'Renewal');
  let IsRenewalDetailsVisible = [2, 106, 118, 130].indexOf(ProductId) !== -1 && lead.LeadSource === 'Renewal';
  let isViewQuoteVisible = (
    ([101].includes(ProductId) && [1, 2, 3, 4, 11].indexOf(lead.StatusId) !== -1) ||
    (
      [131].includes(ProductId) &&
      (
        User.RoleId !== 13 ||
        (User.RoleId === 13 && (lead.LeadSourceId === 6 || [4, 11].indexOf(lead.StatusId) !== -1))
      )
    )
  );
  let IsDisabledCard = (User.RoleId === 13
    ? (!(IntProducts.indexOf(ProductId) !== -1 || lead.LeadOrder == '1' || lead.LeadOrder == '2' || lead.LeadOrder == '3'))
    : !(IntProducts.indexOf(ProductId) !== -1 || lead.LeadOrder == '1' || lead.LeadOrder == '2' || lead.LeadOrder == '3' || lead.LeadOrder == '5'));
  let isPortability = false;
  try {
    isPortability = (lead.LeadSource && lead.LeadSource.toLowerCase() === 'renewal' && lead.Utm_source.toLowerCase().indexOf('portability') !== -1);
  } catch { }

  let [ParentLeadId, PrimaryLeadId, leadIds, TermEligibleSA, PreApprovedData, IsInterestedinLimitedPay, allLeads, ISFOSIntentFlag]
    = useSelector(state => {
      let { parentLeadId, primaryLeadId, leadIds, TermEligibleSA, PreApprovedData, IsInterestedinLimitedPay, allLeads, ISFOSIntentFlag } = state.salesview;
      return [parentLeadId, primaryLeadId, leadIds, TermEligibleSA, PreApprovedData, IsInterestedinLimitedPay, allLeads, ISFOSIntentFlag];
    });

  let IsSmeAdmin = (ProductId == 131 && User.RoleId !== 13);
  // let IsPriorityLead = window.localStorage.getItem('isOneLead') != null ? window.localStorage.getItem('isOneLead') : "false";
  // let dob = lead.Age.split(" ");
  // dob = dob[0];
  let IsApptSummaryVisible = (IsCustomerAccess() && !IsApptfeedbackLead())

  const fdIdentifier = ['fd', 'deposit', 'fixed', 'recurring'];
  let marginclass = 'mx-8px';
  const theme = useTheme();

  const EditLead = () => {
    if (ProductId == 131 && lead && lead.LeadAssignedUser <= 0) {
      enqueueSnackbar("Cannot open this lead edit, please get this lead assigned first", { variant: 'error', autoHideDuration: 3000, });
      return;
    }
    else {
      setOpenEditPopUp(true);
    }

  }
  const isDesktop = useMediaQuery(theme.breakpoints.up('md'), {
    defaultMatches: true
  });
  const ShowPEDDetails = () => {
    setPreExistingDiseasePopUp(true);
    gaEventTracker("ShowPEDpopup", '', User.EmployeeId)
  }
  const ShowPlanPortDetails = () => {
    setplanPortDetailsPopUp(true);
    gaEventTracker("ShowPlanPortDetails", '', User.EmployeeId)
  }

  const ShowRenewalAgentAssist = () => {
    gaEventTracker("ShowRenewalAgentAssist", '', User.EmployeeId)
    window.open("https://docs.google.com/presentation/d/1Mh8s741uSSATw5mXHiYdRJpmrFQ1rqyXHlSQUEBIcAk/edit#slide=id.g32b08df4285_0_60")
  }
  const ShowCovidInv = () => {
    setOpenCovidInv(true);
    // Auto-close the popup after 5 seconds
    setTimeout(() => {
      setOpenCovidInv(false);
  }, 5000);
  }
  
  const ShowSavingPension = () => {
    setImgUrl(CONFIG.PUBLIC_URL + "/images/salesview/Banners/savingpension.png");
    setOpenImgPopup(true);
    // Auto-close the popup after 5 seconds
    setTimeout(() => {
      setOpenImgPopup(false);
  }, 5000);
  }
  const ShowProfessionTypeDetails = () => {
    setProfessionTypePopUp(true);
  }
  const ShowTermSpecialCustomerDetails = () => {
    setTermSpecialCustomerPopUp(true);
  }
  const ShowTermSpecialNRICustomerDetails = () => {
    setTermSpecialNRICustomerPopUp(true);
  }
  const ShowPASABanner = () => {
    setImgUrl(CONFIG.PUBLIC_URL + "/images/salesview/Banners/TermPASAbanner.jpg");
    setOpenImgPopup(true);
  }
  const ShowClaimDetails = () => {
    setOpenClaimsPopup(true);
  }
  const ShowPaymentDetails = () => {
    setPaymentDetailsPopUp(true);
  }
  const ShowMaxTermPanel = () => {
    setShowMaxTermPopUp(true);
  }
  const ShowRejectedLeadDetails = () => {
    setOpenRejectedLeadPopup(true);
  }
  const ShowPitchRecommendation = () => {
    setOpenPitchRecommendationPopup(true);
  }
  const OpenRejectLead = () => {
    if (IsDisabledCard) return;
    if (!ValidateforRenewal()) {
      alert("Renewal Group lead can be rejected by Renewal Team only");
    }
    else {
      if (lead && lead.LeadSource && lead.LeadSource == 'PRB' && ShowSmeCustomerType) {
        dispatch(updateStateInRedux({ key: "PotentialRepeatBuyer", value: true }));
        dispatch(updateStateInRedux({ key: "UnMarkPRB", value: true }));
      }
      setRejectLeadPopUp(true);
    }
  }

  const showFDtag = fdIdentifier.some(element => {
    if (lead.Utm_term && lead.Utm_term.toLowerCase().indexOf(element) !== -1) {
      return true;
    }
    return false;
  });
  //reopen
  const ReOpenMatrixLead = () => {
    let RoleId = User.RoleId;
    let UserId = User.UserId;

    if (RoleId === 13) {
      alert("Agent can't reopen the lead.");
      return;
    }

    let RejectedSubStatusGroups = SV_CONFIG["RejectedSubStatusGroups"][SV_CONFIG["environment"]];
    if (lead.SubStatusId && RejectedSubStatusGroups.indexOf(lead.SubStatusId) > -1 && RoleId === 12) {
      alert("You can't reopen this lead.");
      return;
    }

    checkReopenLimitAndExecute(lead, UserId);
  };
  const checkReopenLimitAndExecute = (lead, UserId) => {
    let SubStatusId = lead.SubStatusId && parseInt(lead.SubStatusId) > 0 ? lead.SubStatusId : 0;
    const input = {
      url: `coremrs/api/LeadRejection/GETLeadReopenTrackCount/${lead.LeadID}/${SubStatusId}`,
      method: 'GET',
      service: 'MatrixCoreAPI',
    };

    CALL_API(input).then((result) => {
      if (result <= 4 && rootScopeService.getProductId() === 2 && ChkRenewal()) {
        ExecuteReOpenLead(lead, UserId);
      } else if (result >= 2) {
        alert("Lead reopen limit end.");
      } else {
        ExecuteReOpenLead(lead, UserId);
        //INSERTLeadReopenTrack(lead.LeadID, lead.SubStatusId, UserId);
      }
    });
  };

  const ExecuteReOpenLead = (lead, userId) => {

    if (window.confirm("Do you want to reopen the lead")) {
      ExecuteReOpenLeadV2({ 'LeadId': lead.LeadID, 'StatusId': lead.StatusId }).then(resultData => {
        try {
          if (resultData && resultData.Data.IsSaved) {
            props.getLeads();
          }
          else if (resultData && resultData.Data.StatusCode == -1) {
            alert(resultData.Data.Message);
          }
          else {
            alert("Unable to Re-open lead");
          }
        }
        catch (ex) {
          alert("Unable to Re-open lead");
        }
      });
    };
  }
  const INSERTLeadReopenTrack = (LeadId, SubStatusID, UserID) => {
    const _input = {
      url: `onelead/api/LeadPrioritization/INSERTLeadReopenTrack/${LeadId}/${SubStatusID}/${UserID}`,
      method: 'GET',
      service: 'MatrixCoreAPI',
      requestData: { 'leadId': lead.LeadID, 'CustomerId': rootScopeService.getCustomerId(), 'ProductId': rootScopeService.getProductId() }
    }
    CALL_API(_input);
  }
  const SetPrimaryLead = () => {
    const input = {
      url: 'coremrs/api/LeadDetails/SetPrimaryLead?CustomerId=' + rootScopeService.getCustomerId() + '&ProductId=' + rootScopeService.getProductId() + '&LeadID=' + lead.LeadID,
      method: "GET",
      service: "MatrixCoreAPI",
      timeout: 's'
    };
    CALL_API(input).then((resultData) => {
      if (resultData && resultData == true) {
        // enqueueSnackbar("Save Mark Primary successfully", { variant: 'success', autoHideDuration: 3000, });
        props.getLeads();
        SaveComments("Lead Id " + lead.LeadID + " mark as primary lead.");
      }
      else {
        enqueueSnackbar("Something went wrong", { variant: 'error', autoHideDuration: 3000, });
      }
    });

  }
  const GetLeadCallTransferInfo = (leadid) => {
    const _input = {
      url: "api/Bms/GetLeadCallTransferInfo?LeadID=" + leadid,
      method: 'GET',
      service: 'MatrixCoreAPI',
    }
    return CALL_API(_input);
  }
  const fnCallTranfer = () => {

    GetLeadCallTransferInfo(lead.LeadID).then(function (resp) {
      if (resp != null) {
        var url = "u=" + User.UserId + "&agent=" + User.EmployeeId;
        if (resp.Campaign != "") {
          url += "&transfer_agents=&campaign=" + resp.Campaign + "&bookingid=" + lead.LeadID + "&transfer_type=transfer_salesservice";
          url += "&dtmf_no=&insurerid=" + resp.InsurerId + "&application_number=" + resp.ApplicationNo + "&grade=" + resp.ServiceAgentGrade;


          var input = {
            LeadId: lead.LeadID,
            UserId: User.UserId,
            MethodName: 'GetLeadCallTransferInfo',
            Message: '',
            Description: '',
            Url: url,
            LogType: 1
          };
          Common.InsertUpdateErrorLog(input);
          setCallTransferUrl(url);
          setOpenCallTranserModal(true);
        }
      }
      else {
        enqueueSnackbar("Can't transfer call.", {
          variant: 'error',
          autoHideDuration: 3000,
        });
        // alertify.error("");
      }
    }, function () {

    });

  };

  const GetCallTransferUrl = async (UserId) => {
    try {
      let thirdpartynumber;
      let res = await GetTLCallingNo(UserId);
      if (res != null && res > 0) {
        thirdpartynumber = res;
        let list = TransferTypes.TransferType.filter(plan => {
          let data = true;
          data = plan.Id === 1;
          return data;
        });
        let Transfertype = list[0].Transfertype;
        let url = createCallTranserUrl("TLCallTransfer", Transfertype, 219, {}, 'no', thirdpartynumber, ParentLeadId)
        console.log("call transfer url is", url)
        setCallTransferUrl(url);
        setOpenCallTranserModal(true);
      }
      else {
        enqueueSnackbar("TL Calling No not found", { variant: 'error', autoHideDuration: 3000 });
        return;
      }
    }
    catch (err) {
      console.log("something went wrong- GetTLCallingNo")
    }
  }

  const SaveComments = (Comment) => {
    let UserId = User.UserId;
    var requestData = {
      "CustomerId": rootScopeService.getCustomerId(),
      "ProductId": rootScopeService.getProductId(),
      ParentLeadId,
      PrimaryLeadId,
      UserId,
      "Comment": Comment,
      "EventType": 23
    }
    //};
    //CALL_API(input).then((result) => {
    //if (result.SetCustomerCommentResult.Data) {
    SetCustomerComment(requestData).then((result) => {
      if (result) {
        enqueueSnackbar("Save  successfully", {
          variant: 'success',
          autoHideDuration: 3000,
        });
      }
    });
  }


  const handleOpenSmartInvestToolLink = () => {
    GetSmartInvestToolLink(lead.LeadID).then(function (result) {
      if (result && result.ExitPointURL && result.ExitPointURL !== '') {
        window.open(result.ExitPointURL);
      }
      else {
        enqueueSnackbar("Smart Invest Tool Link not found", { variant: 'error', autoHideDuration: 2000, });
      }
    });
  }
  const GetSmartInvestToolLink = (leadid) => {
    const _input = {
      url: 'api/LeadDetails/SmartInvestQuesURL?CustomerID=' + rootScopeService.getCustomerId() + '&LeadID=' + leadid,
      method: 'GET',
      service: 'MatrixCoreAPI'
    }
    return CALL_API(_input);
  }
  const getSourceFromUrl = () => {
    var value = "";
    try {
      const urlParams = new URLSearchParams(window.location.search);
      if (urlParams.size > 0) {
        value = urlParams.get("src");
      }
    }
    catch {

    }
    return value;
  }

  const getCJUrl = (lead, cjurl) => {
    gaEventTracker("SmeCJUrlOpen", '', lead.LeadID);
    try {
      let src = getSourceFromUrl();
      if (src) {
        if (cjurl.indexOf('?') !== -1) {
          cjurl = cjurl + "&cjurlsource=" + src
        }
        else {
          cjurl = cjurl + "?cjurlsource=" + src
        }
      }

      if (cjurl.indexOf('?') !== -1) {
        cjurl = cjurl + "&LeadSource=" + lead.LeadSource + "&UtmSource=" + lead.Utm_source
      }
      else {
        cjurl = cjurl + "?LeadSource=" + lead.LeadSource + "&UtmSource=" + lead.Utm_source
      }
    }
    catch { }

    return cjurl;
  }

  const handleOpenContinueLink = () => {
    // IntProducts.indexOf(ProductId)!=-1|| !(roleId==13 ? (!( lead.LeadOrder == '1' || lead.LeadOrder == '2' || lead.LeadOrder == '3')): !( lead.LeadOrder == '1' || lead.LeadOrder == '2' || lead.LeadOrder == '3'|| lead.LeadOrder == '5')) && lead.ProductID !=7 && lead.ProductID !=115 && IsShowContjourneylink == false
    if (lead.ProductID == 131 && User && User.RoleId == 13 && lead.LeadAssignedUser <= 0) {
      enqueueSnackbar("Cannot open Continue Journey link as lead is unassigned, please get this lead assigned first.", { variant: 'error', autoHideDuration: 3000, });
      return;
    }
    if (IsShowContjourneylink == true && lead.ProductID == 2 && lead.LeadSource.toLowerCase() !== "renewal") {
      if (SV_CONFIG["ShowOldHealthCJPanel"] == true) {
        setOpenContinueLink(true);
      }
      else {
        setOpenNewContinueLink(true);
      }
    }
    else if ([7, 115].includes(lead.ProductID) || (lead.LeadSource.toLowerCase() !== "renewal" && lead.ProductID == 2)) {
      if ([7].includes(lead.ProductID)) {
        let cjlink = undefined
        if (lead.ContinueJourneyURL) 
          cjlink = lead.ContinueJourneyURL;
        else 
          cjlink = SV_CONFIG["TermCJ"];
        if (User.LanguageID && User.LanguageID != 0) {
          window.open(cjlink + "&LanguageID=" + User.LanguageID)
        } else {
          window.open(cjlink);
        }

      }
      if ([115, 2].includes(lead.ProductID)) {
        if (lead && lead.ContinueJourneyURL && lead.ContinueJourneyURL !== '') {
          window.open(lead.ContinueJourneyURL);
        }
        else {
          enqueueSnackbar("No Continue Journey found", { variant: 'error', autoHideDuration: 2000, });
        }
      }
    }
    else if ([3, 117].includes(lead.ProductID) && ((lead.LeadCreationSource && lead.LeadCreationSource.toLowerCase() == "pbcromaservicev2") || (lead.EnquiryID && lead.EnquiryID > 0))) {
      GetCommonContinueLink(lead.LeadID, lead.ProductID, lead.SupplierId, lead.LeadSource, lead.LeadCreationSource, lead.EnquiryID).then(function (result) {
        if (result && result.ExitPointURL && result.ExitPointURL !== '') {
          window.open(result.ExitPointURL);
        }
        else {
          enqueueSnackbar("No Continue Journey found", { variant: 'error', autoHideDuration: 2000, });
        }
      });
    }
    else if ([106, 118, 130, 2].indexOf(lead.ProductID) > -1 && lead.LeadSource.toLowerCase() === "renewal") {
      GetHealthRenewalContinueLink(lead.LeadID, lead.ProductID, lead.SupplierId, lead.LeadSource).then(function (result) {
        if (result && result != "") {
          if (result.indexOf("policybazaar.com") != -1) {
            if (result.indexOf("AgentId") == -1) {
              result = result + "&AgentId=" + window.btoa(User.UserId);
            }
            result = result + "&utm_source=matrix";
            window.open(result);
          }
          else {
            window.open(result);
          }
        }
        else {
          window.open("https://healthrenewalv1.policybazaar.com");
        }
      });
    }
    else if (lead.ProductID === 131 && !(lead.SubProductTypeId)) {
      enqueueSnackbar("Please select the SubProduct to Continue Journey", { variant: 'error', autoHideDuration: 2000, });
    }
    else if (lead.ContinueJourneyURL && (IntProducts.indexOf(ProductId) !== -1 || !(User.RoleId === 13 ? (!(['1', '2', '3'].includes(lead.LeadOrder))) : !(['1', '2', '3', '5'].includes(lead.LeadOrder)) && lead.ProductID != 7 && lead.ProductID != 115 && IsShowContjourneylink == false))) {
      if (lead.ProductID === 131) {
        lead.ContinueJourneyURL = getCJUrl(lead, lead.ContinueJourneyURL);
      }
      let urlsrc = getSourceFromUrl();
      if (SV_CONFIG.mtxGoCJexp && urlsrc == "MatrixGoApp") {
        window.open(lead.ContinueJourneyURL, "_self");
      }
      else {
        window.open(lead.ContinueJourneyURL);
      }
    }
    else {
      if ([131, 2, 3, 115, 117].indexOf(lead.ProductID) > -1) {
        GetContinueLink(lead.LeadID).then(function (result) {
          if (result && result.URL) {
            if (lead.ProductID === 131) {
              result.URL = getCJUrl(lead, result.URL);
            }
            let urlsrc = getSourceFromUrl();
            if (SV_CONFIG.mtxGoCJexp && urlsrc == "MatrixGoApp") {
              window.open(result.URL, "_self");
            }
            else {
              window.open(result.URL);
            }
          }
          else {
            enqueueSnackbar("No Continue Journey found", { variant: 'error', autoHideDuration: 2000, });
          }
        });
      }
      else {
        enqueueSnackbar("No Continue Journey found", { variant: 'error', autoHideDuration: 2000, });
      }
    }
  }

  const GetContinueLink = (leadid) => {
    const _input = {
      url: 'api/LeadDetails/GetCJExitPointUrl?LeadId=' + leadid + '&ProductId=' + lead.ProductID,
      method: 'GET',
      service: 'MatrixCoreAPI'
    }
    return CALL_API(_input);
  }

  const handleFreshJourneyClick = () => {
    // const MatrixTokenCookie = getCookie('MatrixToken');
    try {
      if (!IsDisabledCard) {
        setPosCookie();
        let offline_crosssell_url = 'https://www.policybazaar.com/offline_crosssell/';
        // let offline_crosssell_url = SV_CONFIG["PbURL"][SV_CONFIG["environment"]] + 'offline_crosssell/';
        window.open(offline_crosssell_url);
      }
    }
    catch { }
  }

  const GetCommonContinueLink = (leadid, productId, Supplier, leadSource, LeadCreationSource, EnquiryId) => {
    const _input = {
      url: 'api/LeadDetails/GetCommonCJUrl?LeadID=' + leadid + '&ProductId=' + productId + '&SupplierId=' + Supplier + '&LeadSource=' + leadSource + '&LeadCreationSource=' + LeadCreationSource + '&EnquiryId=' + EnquiryId,
      method: 'GET',
      service: 'MatrixCoreAPI'
    }
    return CALL_API(_input);
  }

  const OpenViewQuotes = (LeadID, allowAction = true) => {
    if (IsDisabledCard && !IsSmeAdmin)
      return;

    let _UserId = User.UserId;

    switch (ProductId) {
      case 7:
        let url = SV_CONFIG["TermQuote"][SV_CONFIG["environment"]] + LeadID + "?agentId=" + _UserId;
        window.open(url, '_blank');
        break;
      case 101:
      case 131:
        let isGhiGpaOrGtl = lead.SubProductTypeId && (lead.SubProductTypeId == 1 || lead.SubProductTypeId == 2 || lead.SubProductTypeId == 3);
        let isValidSumAssured = lead && lead.SA && lead.SA > 0;
        let isShowSmeQuotesPanel = (User.RoleId !== 13 && [1, 2, 3, 4, 11].indexOf(lead.StatusId) == -1);
        if (User && User.RoleId == 13 && lead && (lead.LeadAssignedUser <= 0 || lead.LeadAssignedUser != User.UserId)) {
          enqueueSnackbar("Cannot view quotes, please get this lead assigned to you first", { 
              variant: 'error', 
              autoHideDuration: 3000 
          });
          return;
      }
        else if (isGhiGpaOrGtl || (!isGhiGpaOrGtl && isValidSumAssured) || isShowSmeQuotesPanel) {
          setAllowActionInSMEquotes(allowAction);
          setOpenViewQuotesPopup(true);
          setResizeEdit(false);
        }
        else {
          alert("Please edit the lead card and update the Sum Assured");
          setResizeEdit(true);
          setTimeout(() => {
            setResizeEdit(false);
          }, 3000);
        }
        break;
      default:
        break;
    }
  }

  const showLastYearPolicyDetailsPopup = () => {
    setOpenLastYearPolicyDetailsPopup(true);
  }
  const showAvailDiscountPopup = () => {
    setOpenAvailDiscountPopUp(true);
  }
  const showAdditionalInfoPopup = () => {
    setOpenAdditionalInfoPopup(true);
  }
  const showPortDetailsPopup = () => {
    setOpenPortDetailsPopUp(true);
  }

  const showInvestmentToolDetails = () => {
    setOpenSmartInvestmentToolDetailsPopUp(true);
  }

  const showAppointmentSummary = () => {
    setOpenApptSummaryPopup(true);
  }
  //todo : update currency filter
  const currency = (value, curr) => {
    if (curr === "INR") return (`₹${value}`)
    else return `$${value}`
  }
  const ValidateforRenewal = () => {
    var isValid = true;
    if (rootScopeService.getProductId() === 2 && ChkIsRenewal()) {
      isValid = false;
      var UserBUMappingList = User.UserBUMapping;
      if (UserBUMappingList !== undefined && UserBUMappingList !== "") {
        UserBUMappingList.forEach(function (val, key) {
          if (val.IsRenewal && [106, 118, 130, 2].indexOf(val.ProductId) !== -1) {
            isValid = true;
          }
        });
      }
    }
    return isValid;
  }

  const ValidateforEditRenewal = () => {
    var isRenewalAgent = false;
    var allowedTAT = Math.floor((lead.OfferCreatedON + (5 * 86400000)));
    var currentTime = Math.floor(Date.now());
    var UserBUMappingList = User.UserBUMapping;
    if (UserBUMappingList !== undefined
      && UserBUMappingList !== ""
      && parseInt(lead.StatusId, 10) >= parseInt(13, 10) //Booked Lead
      && lead.StatusMode === "P" //Positive status Leads
      && (allowedTAT) > (currentTime)
    )//Add 5 days to TAT)
    {
      UserBUMappingList.forEach(function (val, key) {
        if (val.IsRenewal && ([106, 118, 130, 2].indexOf(val.ProductId) !== -1)) {
          isRenewalAgent = true;
        }
      });
    }
    return isRenewalAgent;
  }

  const ChkIsRenewal = () => {
    var IsRenewal = false;
    if (Array.isArray(allLeads) && allLeads.length > 0) {
      allLeads.forEach((vdata, index) => {
        if (vdata.LeadSourceId === 6 && [1, 2, 3, 4, 11].indexOf(vdata.StatusId) !== -1) {
          IsRenewal = true;
        }
      })
    }
    return IsRenewal;
  }

  const ChkRenewal = () => {
    var IsRenewal = false;
    if (Array.isArray(allLeads) && allLeads.length > 0) {
      allLeads.forEach((vdata, index) => {
        if (vdata.LeadSourceId === 6) {
          IsRenewal = true;
        }
      })
    }
    return IsRenewal;
  }
  const ChkIsShowDiscountAvailaibility = function () {
    if ([2].includes(rootScopeService.getProductId())) {
      if (Array.isArray(allLeads) && allLeads.length > 0) {
        const isNotHealthFresh = allLeads.find(
          (o) => o.LeadSource.toLowerCase() === "renewal");
        if (!isNotHealthFresh) {
          setShowAvailDiscountIcon(true);
        }
      }
    }
  }

  const ShowAnnualRangeNRI = function (AnnualIncome) {
    if (AnnualIncome == 2499999)
      return 'Annual Income Range : 10-25 Lakhs'
    else if (AnnualIncome == 4999999)
      return 'Annual Income Range : 25-50 Lakhs'
    else if (AnnualIncome == 5000001)
      return 'Annual Income Range : More than 50 Lakhs'
    else if (AnnualIncome == 999999)
      return 'Annual Income Range : Less than 10 Lakhs'
    else
      return 'Annual Income : ' + AnnualIncome
  }

  const ShowAnnualRangeNonNRI = function (AnnualIncome) {
    if (AnnualIncome <= 299999)
      return <p className="orangeBg">Annual Income Range - Less than 3 Lakhs</p>
    else if (AnnualIncome == 499999)
      return <p className="orangeBg">Annual Income Range - Between 3-5 Lakhs</p>
    else if (AnnualIncome == 999999)
      return <p className="orangeBg">Annual Income Range - Between 5-10 Lakhs</p>
    else if (AnnualIncome == 1499999)
      return <p className="orangeBg">Annual Income Range - Between 10-15 Lakhs</p>
    else if (AnnualIncome >= 1500001)
      return <p className="orangeBg">Annual Income Range - More than 15 Lakhs</p>
    else
      return ''
  }


  const ShowUtmTerm = function () {
    let result = false;
    try {
      if (([2].indexOf(ProductId) !== -1) && lead.Utm_term &&
        lead.LeadSource && (['pb', 'pbmobile'].indexOf(lead.LeadSource.toLowerCase()) !== -1) &&
        lead.Utm_source && (['google', 'organic'].indexOf(lead.Utm_source.toLowerCase()) !== -1)) {
        let matchReg = lead.Utm_term.match(/[A-Za-z]/g);
        if (matchReg && matchReg.length && matchReg.length >= 3) {
          result = true;
        }
      }
    }
    catch {
      result = false;
    }
    return result;
  }

  let isBrandNewCar = (ProductId == 117 && lead.PolicyType && lead.PolicyType == 'New' && lead.IsBrandNewCar);

  const GetPolicyExpiry = function () {
    let result = "N/A";
    try {
      if (isBrandNewCar) {
        result = lead.ExpectedDeliveryDate ? dayjs(lead.ExpectedDeliveryDate).format("DD/MM/YYYY") : "N/A"
      }
      else {
        result = lead.PolicyExpiryDate == -62135616600000 ? "N/A" : dayjs(lead.PolicyExpiryDate).format("DD/MM/YYYY")
      }
    }
    catch {

    }
    return result;
  }
  const DroneLeadCardDetails = function () {
    try {
      if (ProductId === 131 && DroneModelList && UASTypeList && Data.DroneCategoryType && lead.SubProductTypeId == 117) {
        let DroneModel = DroneModelList.find((e) => e.ID && lead.DroneModelId && parseInt(e.ID) === lead.DroneModelId)
        let DroneCategory = Data.DroneCategoryType.find((e) => e.Id && lead.DroneCategoryId && parseInt(e.Id) === lead.DroneCategoryId)
        let DroneUAS = UASTypeList.find((e) => e.ID && lead.UASTypeId && parseInt(e.ID) === lead.UASTypeId)
        if (DroneModel && DroneModel.Name) {
          setDroneModelName(DroneModel.Name);
        }
        if (DroneCategory && DroneCategory.Name) {
          setDroneCategoryName(DroneCategory.Name);
        }
        if (DroneUAS && DroneUAS.Name) {
          setDroneUASTypeName(DroneUAS.Name)
        }
      }
    }
    catch {

    }

  }

  useEffect(() => {
    //var grpList = User.GroupList;
    DroneLeadCardDetails();
    var grpList = User.GroupList || []
    grpList.forEach(function (item, key) {
      item['GroupName'] = item['GroupName'] || item['groupname'];
    })
    if (rootScopeService.getProductId() === 2 && User.RoleId === 13) {
      var arr = Array.isArray(grpList) ? grpList.find(x => x.GroupName.toLowerCase().includes("renewals")) : undefined;
      if (arr !== undefined) {
        setIsShowContjourneylink(false);
      }
      else {
        setIsShowContjourneylink(true);
      }
    }
    else if (rootScopeService.getProductId() === 2 && User.RoleId !== 13) {
      setIsShowContjourneylink(true);
    }
    chkUserGroup();
    chkNewJourneyUserGroup();
    if (IsDiscountOptionAvailable()) {
      ChkIsShowDiscountAvailaibility();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [lead])
  useEffect(() => {
    setApptDetails(null);

    if (Array.isArray(CustAppmentSmmary) && CustAppmentSmmary.length > 0) {
      if (IsApptfeedbackLead()) {
        let refererObj = CustAppmentSmmary.find((obj) => obj.ProductId === 2)
        setApptDetails(refererObj);
      }
      else {
        setApptDetails(CustAppmentSmmary[0]);
      }
    }
  }, [CustAppmentSmmary])
  const UploadDoc = (leadId) => {

    let IsUploadDoc = (lead.StatusMode === 'P' && [13, 55, 37, 39].indexOf(lead.StatusId) !== -1 && [7, 115].indexOf(rootScopeService.getProductId()) !== -1);
    if (IsUploadDoc && !OpenUploadDocPopUp && ShowAssignCriticalComponents) {
      setOpenUploadDocPopUp(true);
    }
  };

  useEffect(() => {
    if (
      lead && lead.ProductID === 2 &&
        lead?.LeadCreationSource?.toLowerCase() === "pbcromaservicev2" &&
        lead.StatusMode === "P" &&
        lead.HealthIsSegment === 1
    ) {
        let commaseperatedGroupList = "";
        if(User && User.GroupList && Array.isArray(User.GroupList) && User.RoleId === 13)
        {
            commaseperatedGroupList = User.GroupList.map((group) => group.GroupId).join(',');
            commaseperatedGroupList = commaseperatedGroupList.toString();
        }
        gaEventTracker("LeadAdditionalInfoPopup-IconVisible", JSON.stringify({ GroupId: commaseperatedGroupList, empId: User.EmployeeId }), lead.LeadID);
    }
}, []);

  const chkUserGroup = function () {
    if (User.RoleId === 13) {
      var usergrp = User.UserGroupList;
      let PVTandPSUgrps = SV_CONFIG["PVTandPSUgrps"][SV_CONFIG["environment"]];
      usergrp.forEach(function (item, key) {
        if (PVTandPSUgrps.indexOf(item.GroupId) > -1 && rootScopeService.getProductId() === 117) {
          setIsShowPSUAndPVT(true);
        }
      });
    }
    else {
      setIsShowPSUAndPVT(true);
    }
  }
  const chkNewJourneyUserGroup = function () {
    if (User.RoleId === 13 || User.RoleId === 12) {
      var usergrp = User.UserGroupList;
      let NewJourneygrps = SV_CONFIG["NewJourneyGroup"][SV_CONFIG["environment"]];
      usergrp.forEach(function (item, key) {
        if (NewJourneygrps.indexOf(item.GroupId) > -1) {
          setShowFreshJourney(true);
        }
      });
    }
    else {
      setShowFreshJourney(true);
    }
  }

  const chkSmartInvestmentEnable = () => {
    let isVisible = false;
    if (User.RoleId === 13 && User.GroupProcessId && (['6', '7', '8'].indexOf(User.GroupProcessId) !== -1)) {
      isVisible = true;
    }
    const SmartInvestmentGrps = (window && window.SV_CONFIG_UNCACHED
      && Array.isArray(window.SV_CONFIG_UNCACHED.SmartInvestmentToolGroups)
      && window.SV_CONFIG_UNCACHED.SmartInvestmentToolGroups
    ) || SV_CONFIG.SmartInvestmentToolGroups;
    var usergrp = User.UserGroupList;
    if (User.RoleId === 13) {
      Array.isArray(usergrp) && usergrp.forEach(function (item) {
        if (Array.isArray(SmartInvestmentGrps) && SmartInvestmentGrps.indexOf(item.GroupId) > -1) {
          isVisible = true;
        }
      });
    }
    return isVisible;
  }

  const openWhatsApp = (Leadid) => {
    const chat = checkShowNewChat() ? "ChatNew" : "WhatsApp";

    if (chat === "WhatsApp") {
      gaEventTracker("OldWhatsappUsed", User.EmployeeId, 0)
    }
    dispatch(setOpenRightBarMenu({ OpenRightBarMenu: chat }));
    dispatch(updateStateInRedux({ key: "AgentChatLeadId", value: Leadid }))
  }
  const IsHealthRenewalAgent = () => {
    var isRenewalAgent = false;
    var UserBUMappingList = User.UserBUMapping;
    if (UserBUMappingList !== undefined && UserBUMappingList !== "") {
      UserBUMappingList.forEach(function (val, key) {
        if (val.IsRenewal && ([106, 118, 130, 2].indexOf(val.ProductId) !== -1)) {
          isRenewalAgent = true;
        }
      });
    }
    return isRenewalAgent;
  }

  let isPotentialBuyer = ProductId === 131 &&
    (SmePotentialBuyerSubProducts.indexOf(lead.SubProductTypeId) !== -1) &&
    ShowSmeCustomerType;
  let isMarineAnualOpenLead = ProductId === 131 && lead.SubProductTypeId && lead.SubProductTypeId == 13 && MarineAnualOpenLeadData;

  const GetLeadCardCss = (lead) => {
    let className = 'cust-info secondary';
    if (ProductId === 131 && lead.IsInboundCall) {
      className = 'cust-info Golden'
    }
    else if (isPotentialBuyer) {
      className = 'cust-info secondary OrangeBorderSME'
      if (lead.InitialNoOfLives && lead.SubProductTypeId && ProductId === 131 && lead.SubProductTypeId === 13 && lead.InitialNoOfLives === 1) {
        className = 'cust-info secondary OrangeBorderSME RedBackground'
      }
    }
    else if (ProductId === 131 && ((lead.LargeLeadId && lead.LargeLeadId == 2) || (lead.HighIntent && lead.HighIntent == 1))) {
      className = 'cust-info greenBackGround'
    }
    else if (lead.InitialNoOfLives && lead.SubProductTypeId && ProductId === 131 && lead.SubProductTypeId === 13 && lead.InitialNoOfLives === 1) {
      className = 'cust-info RedBackground'
    }
    else if (lead.IsPrimary) {
      className = 'cust-info primary'
    }
    return className
  }

  const AnualOpenTag = () => {
    try {
      if (isMarineAnualOpenLead && Array.isArray(MarineAnualOpenLeadData)) {
        let text = 'Annual open customer';
        if (lead && lead.TransitType && ['Annual Transit', 'Annual Open', 'Single Transit'].indexOf(lead.TransitType) > -1) {
          for (let i = 0; i < MarineAnualOpenLeadData.length; i++) {
            let oldData = MarineAnualOpenLeadData[i];
            if (lead.OccupancyId == oldData.OccupancyId && lead.ShipmentType == oldData.ShipmentType && lead.LeadID !== oldData.LeadId) {
              text = 'Avoid Selling';
              break;
            }
          }
        }
        return <div className={'anualOpenLeadTag'} style={text === 'Avoid Selling' ? { left: "222px" } : {}} onClick={OpenAnualOpenPopup}>{text}</div>
      }
    }
    catch { }
  }

  const OpenAnualOpenPopup = () => {
    setShowAnualOpenPopup(true);
  }

  let isLeadPrimary = !(!lead.IsPrimary && (['1', '2', '3'].indexOf(lead.LeadOrder) !== -1));

  let card = (
    <>
      {(isPotentialBuyer) &&
        <div className="repeatBuyer">Potential Repeat Buyer</div>
      }
      {AnualOpenTag()}
      <div
        className={
          IsDisabledCard
            ? `card-design vertical ${marginclass} disable`
            : `card-design vertical ${marginclass}`
        }
      >
        {
          lead.showExpandMinimize > 1 ? <button className={lead.show ? "minimize" : "maximize"} onClick={lead.toggleExpandFn}>
            <span>{lead.show ? '' : lead.showExpandMinimize}</span>
          </button>
            : null
        }
        <div className={GetLeadCardCss(lead)}>
          {lead && [2].indexOf(ProductId) != -1 && lead.leadSource !== 'Renewal' && ['392', '91', '999', 'INDIA', '0', 'NULL'].indexOf(lead.Country) == -1 && lead.Utm_campaign && lead.Utm_campaign.includes("40%disc")
            && <div className="PitchMaternity">Pitch NRI discount</div>}
          {lead && [2].indexOf(ProductId) != -1 && lead.leadSource !== 'Renewal' && ['392', '91', '999', 'INDIA', '0', 'NULL'].indexOf(lead.Country) == -1 && lead.Utm_campaign && lead.Utm_campaign.includes("care_program")
            && <div className="PitchMaternity">Customer shown interest for NRI - Care Program</div>}
          {/* {lead && (lead.IsFOSInterested || lead.IsHealthRenewalFOS) && <div className="interestedFos">Interested in FOS</div>} */}
          {lead && (lead.IsFOSInterested || lead.IsHealthRenewalFOS || lead.HealthCommunicationFOS || (lead.LeadID == ParentLeadId && ISFOSIntentFlag)) && <div className="interestedFos">Interested in FOS</div>}
          {[2].indexOf(ProductId) !== -1 && lead && lead.LeadSource.toLowerCase() !== 'renewal' && ((lead.Utm_term && lead.Utm_term.toLowerCase().includes("maternity")) || (lead.Utm_campaign && lead.Utm_campaign.toLowerCase().includes("maternity")) ||
            (lead.Utm_content && lead.Utm_content.toLowerCase().includes("maternity")) || (lead.UTM_Medium && new RegExp("growth%000110%".replace(/%/g, '.*'), 'i').test(lead.UTM_Medium)))
            && <div className="PitchMaternity">Pitch ABHI Active One Maternity</div>}
          {lead && lead.ProductID == 7 && ((lead.Country && (['392', '91', '999', 'INDIA', '0', 'NULL'].indexOf(lead.Country) !== -1)) || (lead.Country != undefined && lead.Country == ''))
            && lead.IsDiabetic && lead.LeadRank == 59 && <div className="interestedFos">Diabetic Customer</div>}
          {/* {lead && lead.ProductID == 7 && ((lead.Country && (['392','91','999','INDIA','0','NULL'].indexOf(lead.Country) !== -1)) || (lead.Country !=undefined && lead.Country == ''))
          && lead.IsDiabetic && lead.LeadRank != 59 && <div className="interestedFos">Transferred Diabetic Customer</div> } */}
          <div className="producticon">
            <img alt="product_img" src={getProductIcon(rootScopeService.getProductId(), lead.IsPrimary, lead.SubProductTypeId, lead.PlanFeatureType)} />
            <span>{lead.R_Number}</span>
            <p>{getProductIconText(rootScopeService.getProductId(), lead.SubProductTypeId, lead.PlanFeatureType)}</p>
            {/* <p>Trad</p> */}
            {/* <p>Child</p> */}
          </div>
          <div className="cust-data-div">
            <h3 title={ProductId == 131 ? lead.CompanyName : lead.Name}>
              {ProductId == 131 ? lead.CompanyName : lead.Name}

            </h3>

            {/*<span className="edit-ico">
              {//([2].indexOf(ProductId) !==-1 && lead.ISPed===true) && 
              (
                <span onClick={ShowPEDDetails}>
                  Pre existing disease
                </span>
              )}
              </span>*/}
            {!props.leadViewOnly && !props.LeadOnlyViewNew && !IsApptfeedbackLead() && ShowAssignCriticalComponents && <span className="edit-ico">
              {(([131, 7, 115].includes(ProductId) && !IsBooked) || (ProductId === 117 && User.RoleId !== 13 && !IsBooked) || ([106, 118, 130].includes(ProductId) && IsHealthRenewalAgent() && !IsBooked) || ([2].includes(ProductId) && !IsBooked) || ([2].includes(ProductId) && IsBooked && ValidateforEditRenewal() && lead.LeadSource != "Renewal")) &&
                (
                  <span onClick={EditLead}>
                    <img className={ResizeEdit ? "editIcon penSizingCss" : "editIcon"} alt="Edit" src={CONFIG.PUBLIC_URL + "/images/salesview/edit.svg"} />
                  </span>
                )}
            </span>}
            <div className="datetime-div">
              <span className="datetime">
                {lead.CreatedOn
                  ? dayjs(lead.CreatedOn).format("DD/MM/YYYY h:mm:ss A")
                  : ""}
              </span>
            </div>
            {/* <span className="contacted-text">{lead.StatusName}</span> */}
            
              <span className={lead.StatusMode === "N" ? "Status-text cursorPointer" : "contacted-text cursorPointer"} onClick={UploadDoc}>{lead.StatusName}</span>
            

            <div className="iconSection">
              {((!IsLeadContent() || isDesktop) && !props.LeadOnlyViewNew && ([115, 7, 2, 106, 118, 130, 117, 131, 114, 139].indexOf(ProductId) > -1)) &&
                <img src={CONFIG.PUBLIC_URL + "/images/salesview/whatsapp.svg"} alt="Whatsapp" title="Whatsapp Available"
                  onClick={() => openWhatsApp(lead.LeadID)} className="cursorPointer"
                />
              }
              {(lead.LeadSource && lead.LeadSource.toLowerCase() !== "renewal" && parseInt(lead.ProductID) === 2
                && lead.NoVerifiedBy && lead.NoVerifiedBy.toLowerCase() === "truecaller") &&
                <img src={CONFIG.PUBLIC_URL + "/images/salesview/verified.svg"} alt="TrueCaller" title="TrueCaller Verified"
                  className="cursorPointer verifedIcon"
                />
              }
              {lead.IsBirthday && rootScopeService.getProductId() === 7 &&
                <span title="Birthday Lead" alt="Birthday Lead">
                  <img src={CONFIG.PUBLIC_URL + "/images/salesview/birthdayicon.svg"} alt="Birthday Lead" />
                </span>
              }
              {/* {IsCustomerAccess() && 
              <div className="ht-20">
              <div className="ApptSummary cursorPointer" onClick={showAppointmentSummary}>Appointment Summary</div></div>
              } */}
              {(lead.PaymentSTATUS == 1 || lead.PaymentSTATUS == 1001) && (
                <span title="Payment Attempt">
                  <img src={CONFIG.PUBLIC_URL + "/images/salesview/Paymenticon.svg"} alt="Payment Attempt" />
                </span>
              )}
            </div>
            {
              (ProductId == 131 && lead.LeadSource && lead.LeadSource == 'Inbound' && (invalids.indexOf(lead.IsInboundCall) !== -1)) &&
              <Tooltip title="Inbound Lead">
                <img
                  src={CONFIG.PUBLIC_URL + "/images/salesview/inboundicon.png"}
                  alt="Inbound"
                  className="inboundIcon"
                />
              </Tooltip>
            }
            {
              (ProductId == 131 && lead.IsInboundCall) &&
              <Tooltip title="Inbound Lead">
                <img
                  src={CONFIG.PUBLIC_URL + "/images/salesview/InboundCustomer.png"}
                  alt="Inbound"
                  className="inboundCallingIcon"
                />
              </Tooltip>
            }
          </div>

        </div>

        {(!props.leadViewOnly && !props.LeadOnlyViewNew && !IsApptfeedbackLead() && IsBooked) &&
          <div className="ht-20">
            <div className="hotClTransfer cursorPointer" onClick={fnCallTranfer}>Hot Call Transfer <img src={CONFIG.PUBLIC_URL + "/images/salesview/external-link.svg"} alt="Hot Call Transfer" /></div></div>
        }
        <div className="ht-20">
          {//(isPortability || isPPT || lead.PremiumInflation) ?
            <div className="leadCardTagBar">
              <Slider {...verticalSliderSettings} key={leadIds}>
                {lead.leadRibbons && Array.isArray(lead.leadRibbons) && lead.leadRibbons.length > 0 && lead.leadRibbons.map((ribbon, index) => {
                  return (
                    <>
                      <p className={ribbon.Background}>{ribbon.Message}</p>
                    </>)
                })
                }
                {lead.SubProductTypeName === "Spouse Journey" &&
                  <p className="MegentaBg">{lead.SubProductTypeName}</p>}
                {(lead.SandBoxEligibility === true) && <p>Fit Pass Membership</p>}
                {isPortability && <p>Portability</p>}
                {lead.IsMarkExchange == 1 && <p> Hx</p>}
                {([2].indexOf(ProductId) !== -1 && lead.ISPed === true) && <p onClick={ShowPEDDetails} className='cursorPointer'>Pre existing disease <img className='clickableImg' src={CONFIG.PUBLIC_URL + "/images/salesview/external-link.svg"} alt="" /></p>}
                {ProductId === 115 && (lead.SubProductTypeId == 1 || lead.SubProductTypeId == 3) && lead.PlanFeatureType && lead.PlanFeatureType.toLowerCase().indexOf('sip') !== -1 && <p>Smart Investment Pack </p>}
                {ProductId === 7 && lead.IsBirthday && <p>Birthday Lead </p>}
                {lead.PlanFeatureType && lead.PlanFeatureType.toUpperCase() === 'T' && ((lead.Utm_term && lead.Utm_term.toLowerCase().indexOf('fd') !== -1) || (lead.Utm_term && lead.Utm_term.toLowerCase().indexOf('fixed deposit') !== -1)) && <p>Fixed Deposit</p>}
                {ProductId === 115 && (lead.IsImmediatePension == 1 || (lead.Utm_campaign && lead.Utm_campaign.toLowerCase().indexOf('annuity') !== -1)) && <p> Annuity</p>}
                {ProductId === -7 && (lead.Utm_source && lead.Utm_campaign && lead.Utm_source !== '' && lead.Utm_campaign !== '' && lead.Utm_source.toLowerCase().indexOf('paisa') !== -1 && lead.Utm_campaign.toLowerCase().indexOf('tata') !== -1)
                  && <p title='Eligible for Pre-Approved Sum Assured of 1 Cr with Tata'>Pre Approved </p>}
                {(ProductId === 7 && (TermEligibleSA > 0 || (Array.isArray(PreApprovedData) && PreApprovedData.indexOf(lead.LeadID) > -1)))
                  && <p title={`Eligible for Pre-Approved Sum Assured of ${TermEligibleSA}`}>Pre-Approved</p>}
                {(Array.isArray(PreApprovedData) && PreApprovedData.indexOf(lead.LeadID) === -1) && ProductId === 7 && TermEligibleSA == 0 && lead.Utm_source && lead.Utm_campaign && lead.Utm_source !== '' && lead.Utm_campaign !== '' && lead.Utm_source.toLowerCase().indexOf('paisa') !== -1 && lead.Utm_campaign.toLowerCase().indexOf('pnb') !== -1 && lead.QualificationId == 1
                  && <p title='Eligible for Pre-Approved Sum Assured of 1 Cr with PNB'>Pre-Approved</p>}

                {lead.LeadSource.toLowerCase() == 'crosssell' && lead.Utm_source.toLowerCase() == 'monthlymode'
                  && <p onClick={ShowPaymentDetails} className="cursorPointer">Payment Failed</p>}
                {User.RoleId == 13 && ProductId === 7 && lead.LeadSource.toLowerCase() == 'reopen' && lead.Utm_source.toLowerCase() == 'housewife_reopen'
                  && <p>Cancelled - Pitch HW Plan</p>}
                {lead.LeadSource.toLowerCase() == 'pb' && lead.Utm_source.toLowerCase() == 'sme_pb_paisa_leads'
                  && <p>{lead.Utm_campaign}</p>}
                {ProductId === 7 && lead.LeadSource.toLowerCase() == 'reopen' && lead.Utm_source.toLowerCase() == 'pitch_hw_on_ipru'
                  &&
                  <p className="YellowBg">Pitch_HW_On_iPru</p>
                }
                 {ProductId === 7 && lead.LeadSource.toLowerCase() == 'reopen' && lead.Utm_source.toLowerCase() == 'term_wife_upsell'
                  && <p className="MegentaBg">{lead.Utm_campaign}</p>
                }
                {ProductId === 7 && lead.LeadSource.toLowerCase() == 'reopen' && lead.Utm_source.toLowerCase() == 'hw_on_term_issued'
                  && <p className="blueBg">Pitch HW - Term Issued</p>}
                {ProductId === 7 && lead.LeadSource.toLowerCase() == 'reopen' && lead.Utm_source.toLowerCase() == 'term_nri_on_orphan_inv_nri'
                  &&
                  <p className="greenShadeBg">Term NRI Cross-Sell</p>
                }
                {ProductId === 7 && lead.LeadSource.toLowerCase() == 'reopen' && lead.Utm_source.toLowerCase() == 'reject_reopen_health_on_term'
                  &&
                  <p className="greenShadeBg">Health HNI Customer - Pitch Term Plans</p>
                }
                {ProductId === 7 && lead.LeadSource.toLowerCase() == 'reopen' && lead.Utm_source.toLowerCase() == 'term_cross-sell_on_inv_orphan_bkgs_2021-22'
                  &&
                  <p>Pitch term on investment issued</p>
                }
                {ProductId === 7 && (IsInterestedinLimitedPay === 1) &&
                  <div className="LimitedPay"><img src={CONFIG.PUBLIC_URL + "/images/salesview/AlertAnimation.gif"}
                    alt="Customer has shown interest in Limited Pay" /><p> Customer has shown interest in <b> Limited Pay </b></p></div>
                }
                {ProductId === 115 && (lead.SubProductTypeId == 6) && showFDtag &&
                  <p className="orangeBg">Pitching MAX SFRD will increase your conversion chances </p>
                }
                {ProductId === 115 && (lead.LeadSource.toLowerCase() === 'reopen') && (lead.Utm_source.toLowerCase() == 'ai_upsell_fos') &&
                  Array.isArray(issuedPolicies) && (issuedPolicies.find((policy) => ((policy.ProductId === 7 || policy.ProductId === 1000) && (policy.SupplierId === 10)))) &&
                  <p className="orangeBg">Max Life Term Customer: Pitch Smart FD </p>
                }
                {ProductId === 115 && (lead.InvestmentSearchedPlanId === 32684 || lead.InvestmentSearchedPlanId === 15757 || lead.InvestmentSearchedPlanId === 15768) &&
                  <div className="LICInterestedLeads"><img src={CONFIG.PUBLIC_URL + "/images/salesview/LICLogo.svg"}
                    alt="Interested in LIC" /><p> <b>Interested in LIC </b></p></div>
                }
                {ProductId === 114 && TWLeadErrorType && <p>{TWLeadErrorType}</p>}
                {ProductId === 2 && ChkIsFresh(allLeads) && lead.PolicyType && (lead.PolicyType.toLowerCase() === 'rollover') &&
                  <p className="orangeBg">Rollover Case</p>
                }
                {ProductId === 7 && lead.Utm_source && (lead.Utm_source.toLowerCase() == 'term_nri_mkt_base') &&
                  <p className="orangeBg">Sell Term on Not Visited customer</p>
                }
                {ProductId === 7 && lead.Utm_source && (lead.Utm_source.toLowerCase() == 'termonmotor_reopen') &&
                  lead.LeadSource && (lead.LeadSource.toLowerCase() == 'crosssell') &&
                  <p className="greenShadeBg">Motor Policyholder</p>
                }
                {lead.ProductID == 115 && lead.LeadSource.toLowerCase().indexOf('pb') !== -1 && lead.LeadSource.toLowerCase().indexOf('pbp') == -1
                  && lead.Utm_source && (lead.Utm_source.toLowerCase().indexOf('tata') !== -1 && lead.Utm_source.toLowerCase().indexOf('consumer') !== -1
                    && lead.Utm_source.toLowerCase().indexOf('portal') !== -1 && lead.Utm_source.toLowerCase().indexOf('tata') < lead.Utm_source.toLowerCase().indexOf('consumer')
                    && lead.Utm_source.toLowerCase().indexOf('consumer') < lead.Utm_source.toLowerCase().indexOf('portal'))
                  && <p className="orangeBg"> Tata Consumer Portal Upsell Lead : Pitch Tata Policy</p>
                }
                {/* {lead.ProductID == 115 && (lead.LeadSource.toLowerCase() == 'reopen') && (lead.Utm_source.toLowerCase() == 'ai_upsell') && 
                 (lead.Utm_campaign && lead.Utm_campaign.toLowerCase() == 'hdfc_pasa_proposition') &&
                  <p className="orangeBg"> HDFC PASA Proposition : Pitch HDFC Plans</p>
                }   */}
                {lead.ProductID == 115 && lead.CountryId && [392, 91, 999, 0].indexOf(lead.CountryId) == -1 && lead.ExactAnnualIncome &&
                  (parseInt(lead.ExactAnnualIncome) > 10000) && <p className="orangeBg">{ShowAnnualRangeNRI(lead.ExactAnnualIncome)}   </p>
                }
                {lead.ProductID == 115 && lead.CountryId && [392, 91, 999, 0].indexOf(lead.CountryId) !== -1 && lead.ExactAnnualIncome &&
                  lead.LeadSource && (['referral', 'crosssell'].indexOf(lead.LeadSource.toLowerCase()) == -1) &&
                  ShowAnnualRangeNonNRI(lead.ExactAnnualIncome)
                }
                {lead.ProductID == 115 && lead.CountryId && [392, 91, 999, 0].indexOf(lead.CountryId) !== -1 && lead.ExactAnnualIncome &&
                  lead.LeadSource && (['referral', 'crosssell'].indexOf(lead.LeadSource.toLowerCase()) !== -1) &&
                  <p className="orangeBg">{'Annual Income : ' + lead.ExactAnnualIncome}   </p>
                }
                {(lead.ProductID == 7 && (lead.Utm_source && lead.Utm_source.toLowerCase() === 'whatsapp_crm_sales')) && (lead.Utm_campaign && (lead.Utm_campaign.toLowerCase().indexOf('tipru') !== -1)) && <p onClick={ShowPASABanner} className='greenShadeBg cursorPointer'>Ipru PASA Customer : Pitch Ipru <img className='clickableImg' src={CONFIG.PUBLIC_URL + "/images/salesview/external-link.svg"} alt="" /></p>}

                {
                  ShowUtmTerm() &&
                  <p title={lead.Utm_term} className="orangeBg">
                    {
                      (lead.Utm_term && lead.Utm_term.length > 0)
                        ? lead.Utm_term.substring(0, 30)
                        : ""
                    }
                  </p>
                }
                {ProductId === 115 && lead.LeadSource.toLowerCase() === 'reopen' && lead.Utm_source && lead.Utm_source.toLowerCase() === 'ai_upsell_maturing'
                  && <p className="greenShadeBg">Maturing Bajaj Goal Assure Customer</p>}
                {lead.ProductID == 7 && (lead.LeadSource && lead.LeadSource.toLowerCase() == 'reopen') && (lead.Utm_source && lead.Utm_source.toLowerCase() == 'term_nri_rejected') &&
                  <p className="orangeBg"> NRI reopen lead</p>
                }
                {lead.ProductID == 117 && (lead.Utm_source && (UTMSourceIntentMapping.includes(lead.Utm_source.toLowerCase()))) && (lead.CustomerIntent && lead.CustomerIntent.length > 0) &&
                  <p className="orangeBg"> {lead.CustomerIntent} </p>
                }
                {lead.ProductID == 7 && (lead.Utm_source && (lead.Utm_source.toLowerCase() === 'whatsapp_crm_sales')) && (lead.Utm_campaign && (lead.Utm_campaign.toLowerCase().indexOf('fos') !== -1)) &&
                  <p className="greenShadeBg"> Interested for visit </p>
                }
                {lead.ProductID == 117 && (lead.Utm_source && (lead.Utm_source.toLowerCase() === 'whatsapp_crm_sales')) && (lead.Utm_term && (lead.Utm_term.toLowerCase() === 'moh')) && (lead.LeadSource && lead.LeadSource.toLowerCase() === 'whatsapp') &&
                  <p className="greenShadeBg"> Existing HNI Health Cx, Motor policy expiring soon (WA lead) </p>
                }
                {/* {lead.ProductID == 2 && (lead.LeadSource && lead.LeadSource.toLowerCase() === 'pbmobileapp') &&
                  (lead.UTM_Medium && (lead.UTM_Medium.toLowerCase().indexOf('2_health') !== -1 || lead.UTM_Medium.toLowerCase().indexOf('2_hlt') !== -1)) &&
                  <p className="greenShadeBg"> {lead.UTM_Medium} </p>
                } */}
                {ProductId === 115 && lead.LeadSource.toLowerCase() === 'reopen' && lead.Utm_source && lead.Utm_source.toLowerCase() === 'nri_upsell'
                  && ['392', '91', '999', 'INDIA', '0', 'NULL'].indexOf(lead.Country) == -1
                  && <p className="orangeBg">Existing NRI Booked Customer : Upsell New Plan</p>}
                {lead.ProductID == 2 && lead.LeadSource && (lead.LeadSource.toLowerCase() === 'crosssell' || lead.LeadSource.toLowerCase() === 'referral') &&
                  lead.Utm_source && (lead.Utm_source.toLowerCase() === 'visithealth-teleconsult' || lead.Utm_source.toLowerCase() === 'healthoncorp') &&
                  lead.Utm_campaign &&
                  <p className="greenShadeBg">{lead.Utm_campaign.substring(0, 40)}</p>
                }
                {/* {ProductId === 2 && lead.Utm_source && lead.Utm_source.substring(0,3).toLowerCase() === 'yt_'
                && <p className="YellowBg">Youtube</p>} */}
                {ProductId === 2 && lead.LeadSource && lead.LeadSource.toLowerCase() === 'crosssell' && lead.Utm_source && lead.Utm_source.toLowerCase() === 'hom_relhi'
                  && <p className="orangeBg">HOM Lead</p>}
                {/* {lead.ProductID == 2 && (lead.Utm_source && (PaisaBazaarUTMSource.includes(lead.Utm_source.toLowerCase()))) &&
                  <p className="greenShadeBg"> PaisaBazaar </p>} */}
                {ProductId === 115 && lead.Utm_source && lead.Utm_source.toLowerCase() === 'ai_upsell_ronri' && lead.LeadSource && lead.LeadSource.toLowerCase() === 'nrireopen'
                  && <p className="orangeBg"> Existing NRI Booked Customer : Pitch New Plan </p>}
                {ProductId === 115 && lead.Utm_source && lead.Utm_source.toLowerCase() === 'hdfc life single pay upsell' && lead.LeadSource && lead.LeadSource.toLowerCase() === 'nrireopen'
                  && <p className="greenShadeBg">  Existing HDFC Life Customer - Pitch Flexi Cap Fund </p>}
                {([7].indexOf(ProductId) !== -1 &&
                  lead.ProfessionType && lead.ProfessionType.toLowerCase().includes("self") && lead.ProfessionType.toLowerCase().includes("employed")
                )
                  && <p onClick={ShowProfessionTypeDetails} className='cursorPointer'>Self Employed Pitch<img className='clickableImg' src={CONFIG.PUBLIC_URL + "/images/salesview/external-link.svg"} alt="" /></p>}

                {([7].indexOf(ProductId) !== -1 &&
                  lead.LeadSource && lead.LeadSource.toLowerCase() === "reopen" && lead.Utm_source && lead.Utm_source.toLowerCase() === "special_cust_retarget"
                )
                  && <p onClick={ShowTermSpecialCustomerDetails} className='orangeBg cursorPointer'>Term_Special_Customer | {lead.UTM_Medium} <img className='clickableImg' src={CONFIG.PUBLIC_URL + "/images/salesview/external-link.svg"} alt="" /></p>}
                {([7].indexOf(ProductId) !== -1 &&
                  lead.LeadSource && lead.LeadSource.toLowerCase() === "reopen" && lead.Utm_source && lead.Utm_source.toLowerCase() === "special_nri_cust_retarget"
                )
                  && <p onClick={ShowTermSpecialNRICustomerDetails} className='orangeBg cursorPointer'>NRI_Term_Special_Customer | {lead.UTM_Medium} <img className='clickableImg' src={CONFIG.PUBLIC_URL + "/images/salesview/external-link.svg"} alt="" /></p>}
             
                {([115].indexOf(ProductId) !== -1 &&
                  lead.LeadSource && lead.LeadSource.toLowerCase() === "reopen" && lead.Utm_source && lead.Utm_source.toLowerCase() === "inv_nri_cust_retarget"
                )
                  && <p className='orangeBg cursorPointer'>Special Retarget Customer : {lead.UTM_Medium} </p>}
                {
                  [7, 1000].indexOf(ProductId) !== -1 &&
                  (
                    (
                      lead.LeadSource && lead.LeadSource.toLowerCase() === 'whatsapp' &&
                      lead.Utm_campaign && lead.Utm_campaign.toLowerCase().includes('tbi_max')
                    ) ||
                    (
                      lead.LeadSource && lead.LeadSource.toLowerCase() === 'pbmobileapp' &&
                      lead.UTM_Medium && lead.UTM_Medium.toLowerCase().includes('tbi_max')
                    )
                  )
                  && <p onClick={ShowMaxTermPanel} className="greenShadeBg"> Customer eligible for Max Term-By-Invite  <img className='clickableImg' src={CONFIG.PUBLIC_URL + "/images/salesview/external-link.svg"} alt="" /></p>
                }
                {ProductId === 7 && lead.JobTackingId === 77
                  &&
                  <p className="greenShadeBg">Customer is seeking help from a senior advisor</p>
                }
                {ProductId === 2 && lead.leadSource !== 'Renewal' && (lead.Utm_campaign && lead.Utm_campaign.toLowerCase().indexOf('2on114_book') !== -1) && (lead.Utm_source && (lead.Utm_source.toLowerCase() === 'whatsapp_crm_sales'))
                  &&
                  <p className="greenShadeBg">Eligible for 10% Additional Discount</p>
                }
                {ProductId === 7 && lead.PreferredLanguage && lead.PreferredLanguage.length > 0 && lead.PreferredLanguage.toLowerCase() !== "others"
                  &&
                  <p className="greenShadeBg">Customer prefers to speak in {lead.PreferredLanguage} </p>
                }
                {ProductId === 115
                  && lead.Utm_source && lead.Utm_source.toLowerCase() === 'crosssell_transfer_to_pension'
                  && lead.LeadSource && lead.LeadSource.toLowerCase() === 'reopen'
                  && <p className="orangeBg">High Age Cust : Pitch Pension with Life Cover Plans</p>
                }
                {ProductId === 115
                  && lead.Utm_source && (lead.Utm_source.toLowerCase().indexOf('portal') > lead.Utm_source.toLowerCase().indexOf('consumer')) > lead.Utm_source.toLowerCase().indexOf('tata') && lead.Utm_source.toLowerCase().indexOf('tata') != -1
                  && lead.Utm_campaign && (lead.Utm_campaign.toLowerCase().indexOf('centre') > lead.Utm_campaign.toLowerCase().indexOf('contact')) > lead.Utm_campaign.toLowerCase().indexOf('tata') && lead.Utm_source.toLowerCase().indexOf('tata') != -1
                  && lead.LeadSource && lead.LeadSource.toLowerCase().indexOf('reopen') !== -1 && lead.LeadSource.toLowerCase().indexOf('pbp') == -1
                  && <p className="greenShadeBg">Tata Offline Store Lead : Pitch Tata Policy</p>
                }
                {ProductId === 131 && isHighIntentLead && lead.LeadID == isHighIntentLead &&
                  <p className="greenShadeBg">High Intent Lead</p>
                }

                {([2].indexOf(ProductId) !== -1 && lead.LeadSource == 'Renewal' && lead.RenewalSupplierId == 17) && <p onClick={ShowPlanPortDetails} className='cursorPointer MegentaBg'>Click for Port Plans  <img className='clickableImg' src={CONFIG.PUBLIC_URL + "/images/salesview/external-link.svg"} alt="" /></p>}
                {
                  ([2].indexOf(ProductId) !== -1 && lead.LeadSource == 'Renewal' && lead.RenewalYear > 1 && [2, 3].includes(lead.PreviousTerm))
                  &&
                  <p onClick={ShowRenewalAgentAssist} className='cursorPointer MegentaBg'>Click here for MY opening pitch <img className='clickableImg' src={CONFIG.PUBLIC_URL + "/images/salesview/external-link.svg"} alt="" />
                  </p>
                }
                {
                  ([115].indexOf(ProductId) !== -1 && lead.LeadSource == 'Reopen' && lead.Utm_source && (lead.Utm_source == 'Buy_the_Dip_Cust_NRI' || lead.Utm_source == 'Buy_the_Dip_Cust_Dom' ) && lead.Utm_campaign && lead.Utm_campaign.toLowerCase().includes('cust_bought_in_covid_period_')  )
                  &&
                  <p onClick={ShowCovidInv} className='cursorPointer greenShadeBg'>Covid Period Customer. Absolute Return {lead.Utm_campaign.split('_')[5]} <img className='clickableImg' src={CONFIG.PUBLIC_URL + "/images/salesview/external-link.svg"} alt="" />
                  </p>
                }
                {
                  ([115].indexOf(ProductId) !== -1 && lead.LeadSource == 'Reopen' && lead.Utm_source && lead.Utm_source == 'Pension_Upsell' && lead.Utm_campaign && lead.Utm_campaign.toLowerCase() == 'upsell_pension_on_non-issued_new_inv_cust' )
                  &&
                  <p onClick={ShowSavingPension} className='cursorPointer greenShadeBg'>Previous Booked , Non Issued , Savings Customer <img className='clickableImg' src={CONFIG.PUBLIC_URL + "/images/salesview/external-link.svg"} alt="" />
                  </p>
                }
                {
                  ([2].includes(ProductId) && ['392', '91', '999', 'INDIA', '0', 'NULL'].indexOf(lead.Country) == -1 && lead.PreviousBooking && lead.PreviousBooking == true) &&
                  <p className="MegentaBg">High-Paying Customer: Pitch Rs 20L SI & 3-year Policy</p>
                }

              </Slider>
            </div>
            //: null
          }
        </div>
        <Grid container >
          <GridRow
            label="Sum Assured"
            value={lead.SA === undefined ? "N/A" : (ProductId === 3) ? currency(lead.SA) : currency(lead.SA, "INR")}
            show={[115, 131, 3, 195].indexOf(ProductId) !== -1}
          // landScapeViewRequired={landScapeViewRequired}
          />
          <GridRow
            label="Investment"
            value={(lead.Premium === undefined ? "N/A" : (currency(lead.Premium, "INR"))) + " " + (lead.PaymentPeriodicity === undefined || lead.PaymentPeriodicity === null ? "Yearly" : lead.PaymentPeriodicity)}
            show={[115].indexOf(ProductId) !== -1}
          // landScapeViewRequired={landScapeViewRequired}
          />
          <GridRow
            label="Journey Type"
            value={lead.SubProductTypeName ? lead.SubProductTypeName : "N/A"}
            show={ProductId === 7}
          // landScapeViewRequired={landScapeViewRequired}
          />
          <GridRow
            label="Customer Selection"
            value={lead.CustomerSelection === undefined ? "N/A" : lead.CustomerSelection.SupplierDisplayName +
              " (" + lead.CustomerSelection.PlanName + ")"}
            show={ProductId === 7}
          // landScapeViewRequired={landScapeViewRequired}
          />
          <GridRow
            label="Riders Selection"
            value={lead.Planfeature === undefined || lead.Planfeature == "" ? "N/A" : lead.Planfeature}
            show={ProductId === 7 && lead.Planfeature && lead.Planfeature !== undefined && lead.Planfeature != ""}
          />
          <GridRow
            label="NRI PricingCompare"
            value={lead.IsTermPricingCompare && lead.IsTermPricingCompare === true ? "Yes" : "No"}
            show={ProductId === 7 && lead.IsTermPricingCompare && lead.IsTermPricingCompare === true}
          />
          <GridRow
            label="Business Type"
            value={ProductId == 131 && lead.LargeLeadId && lead.LargeLeadId == 1 ? "Small Business" : "Large Business"}
            show={ProductId == 131 && lead.LargeLeadId && (lead.LargeLeadId == 1 || lead.LargeLeadId == 2)}
          />
          <GridRow
            label="Company Name"
            value={lead.CompanyName === undefined ? "N/A" : lead.CompanyName.substring(0, 25)}
            show={([165].indexOf(ProductId) !== -1)}
          // landScapeViewRequired={landScapeViewRequired}
          />
          <GridRow
            label="Name"
            value={lead.Name ? lead.Name.substring(0, 25) : "N/A"}
            show={ProductId == 131}
          />
          <GridRow
            label="Members"
            value={lead.Need === undefined ? "N/A" : lead.Need}
            show={[2, 106, 118, 130].indexOf(ProductId) !== -1 && lead.LeadSource !== "Renewal"}
          // landScapeViewRequired={landScapeViewRequired}
          />
          <GridRow
            label="Members Age"
            value={(ProductId === 3) ? lead.AgeOfAllMembers : lead.AgeOfAllMembers.trim(',')}
            show={((ProductId === 3) || (([2, 106, 118, 130].indexOf(ProductId) !== -1) && lead.LeadSource !== "Renewal"))}
          // landScapeViewRequired={landScapeViewRequired}
          />
          <GridRow
            label="Category"
            value={lead.Category === undefined ? "N/A" : lead.Category}
            show={ProductId === 3}
          // landScapeViewRequired={landScapeViewRequired}
          />
          <GridRow
            label="Cover"
            value={lead.SA != null ? lead.SA : "N/A"}
            show={ProductId === 7}
          // landScapeViewRequired={landScapeViewRequired}
          />
          <GridRow
            label="Policy Type"
            value={lead?.PolicyType?.trim() || "N/A"}
            show={[117, 2, 131, 106, 118, 130, 114, 190, 139].indexOf(rootScopeService.getProductId()) !== -1}
          // landScapeViewRequired={landScapeViewRequired}
          />
          <GridRow
            label={isBrandNewCar ? "Expected Delivery" : "Policy Expiry"}
            value={GetPolicyExpiry()}
            show={[117, 106, 118, 130, 114, 139].indexOf(ProductId) !== -1 || ([2, 131, 190].indexOf(ProductId) !== -1 && lead.LeadSource === 'Renewal')}
          />
          <GridRow
            label="Supplier"
            value={lead.SupplierName ? lead.SupplierName : "N/A"}
            show={[2, 106, 118, 130, 190].indexOf(ProductId) !== -1 && lead.LeadSource === 'Renewal'}
          // landScapeViewRequired={landScapeViewRequired}
          />
          <GridRow
            label="Policy Number"
            value={lead.PolicyNo == undefined ? "N/A" : lead.PolicyNo}
            show={[2, 106, 118, 130, 190].indexOf(ProductId) !== -1 && lead.LeadSource === 'Renewal'}
          // landScapeViewRequired={landScapeViewRequired}
          />
          <GridRow
            label="Notice Premium"
            value={lead.NoticePremium == undefined ? "N/A" : lead.NoticePremium}
            show={[2, 106, 118, 130, 190].indexOf(ProductId) !== -1 && lead.LeadSource === 'Renewal'}
          // landScapeViewRequired={landScapeViewRequired}
          />
          <GridRow
            label="NCB"
            value={lead.NCB === undefined ? "N/A" : lead.NCB}
            show={[2, 106, 118, 130, 190].indexOf(ProductId) !== -1 && lead.LeadSource === 'Renewal'}
          // landScapeViewRequired={landScapeViewRequired}
          />
          <GridRow
            label="BasicSumInsured"
            value={lead.BasicSumInsured === undefined ? "N/A" : lead.BasicSumInsured}
            show={[2, 106, 118, 130, 190].indexOf(ProductId) !== -1 && lead.LeadSource === 'Renewal'}
          // landScapeViewRequired={landScapeViewRequired}
          />
          <GridRow
            label="Reg. No"
            value={lead?.RegistrationNumber?.trim() || "N/A"}
            show={[117, 114, 139].indexOf(rootScopeService.getProductId()) !== -1}
          // landScapeViewRequired={landScapeViewRequired}
          />
          <GridRow
            label="Revisit"
            value={lead.TPPreferred ? lead.TPPreferred : "N/A"}
            show={[117].indexOf(ProductId) !== -1 && IsShowPSUAndPVT}
          />
          <GridRow
            label="Preference"
            value={lead.CompPreferred ? lead.CompPreferred : "N/A"}
            show={[117].indexOf(ProductId) !== -1 && IsShowPSUAndPVT}
          />
          <GridRow
            label="Retirement Age"
            value={lead.RetirementAge ? lead.RetirementAge : "N/A"}
            show={(ProductId === 115 && lead.SubProductTypeId === 2)}
          // landScapeViewRequired={landScapeViewRequired}
          />
          <GridRow
            label="Child Age"
            value={lead.ChildAge ? lead.ChildAge : "N/A"}
            show={ProductId === 115 && lead.SubProductTypeId === 3}
          // landScapeViewRequired={landScapeViewRequired}
          />
          <GridRow
            label="Accommodation"
            value={lead.Accommodation === undefined ? "N/A" : lead.Accommodation}
            show={ProductId === 101 && lead.LeadSource !== 'Renewal'}
          // landScapeViewRequired={landScapeViewRequired}
          />
          <GridRow
            label="Property Age"
            value={lead.PropertyAge === undefined ? "N/A" : lead.PropertyAge}
            show={ProductId === 101 && lead.LeadSource !== 'Renewal'}
          // landScapeViewRequired={landScapeViewRequired}
          />
          <GridRow
            label="Content Value"
            value={lead.ContentValue === undefined ? "N/A" : lead.ContentValue}
            show={ProductId === 101}
          // landScapeViewRequired={landScapeViewRequired}
          />
          <GridRow
            label="Building Value"
            value={lead.BuildingValue === undefined ? "N/A" : lead.BuildingValue}
            show={ProductId === 101}
          // landScapeViewRequired={landScapeViewRequired}
          />
          <GridRow
            label="Final Premium"
            value={lead.TotalPremium === undefined ? "N/A" : lead.TotalPremium}
            show={ProductId === 101 && lead.LeadSource !== 'Renewal'}
          // landScapeViewRequired={landScapeViewRequired}
          />
          <GridRow
            label="Existing Supplier"
            value={lead.ExistingSupplierName}
            show={[101, 131].includes(ProductId) && lead.LeadSource === 'Renewal'}
          // landScapeViewRequired={landScapeViewRequired}
          />
          <GridRow
            label="Existing Plan"
            value={lead.ExistingPlanName}
            show={ProductId === 101 && lead.LeadSource === 'Renewal'}
          // landScapeViewRequired={landScapeViewRequired}
          />
          <GridRow
            label="Existing Premium"
            value={lead.NoticePremium}
            show={ProductId === 101 && lead.LeadSource === 'Renewal'}
          // landScapeViewRequired={landScapeViewRequired}
          />
          <GridRow
            label="Policy End Date"
            value={lead.PolicyExpiryDate === -62135616600000 ? "N/A" : dayjs(lead.PolicyExpiryDate).format("DD/MM/YYYY")}
            show={(ProductId === 101 && lead.LeadSource === 'Renewal') || (ProductId === 131 && lead.LeadSource === 'winback')}
          // landScapeViewRequired={landScapeViewRequired}
          />
          <GridRow
            label="Policy Start Date"
            value={lead.PolicyStartDate === -62135616600000 ? "N/A" : dayjs(lead.PolicyStartDate).format("DD/MM/YYYY")}
            show={(ProductId === 131 && lead.LeadSource === 'winback')}
          // landScapeViewRequired={landScapeViewRequired}
          />
          <GridRow
            label="Countries"
            value={lead.Countries === undefined ? "N/A" : lead.Countries}
            show={(ProductId === 3)}
          // landScapeViewRequired={landScapeViewRequired}
          />
          <GridRow
            label="Travel Start Date"
            value={lead.TravelStartDate ? dayjs(lead.TravelStartDate).format("DD-MM-YYYY") : ""}
            show={(ProductId === 3)}
          // landScapeViewRequired={landScapeViewRequired}
          />
          <GridRow
            label="Travel End Date"
            value={lead.TravelEndDate ? dayjs(lead.TravelEndDate).format("DD-MM-YYYY") : ""}
            show={(ProductId === 3)}
          // landScapeViewRequired={landScapeViewRequired}
          />
          <GridRow
            label="Smoker?"
            value={lead.IsTobaccoUser === true ? "Yes" : "No"}
            show={[7, 138].indexOf(ProductId) !== -1}
          // landScapeViewRequired={landScapeViewRequired}
          />
          <GridRow
            label={ProductId !== 139 ? "DOB" : "Registration Date"}
            value={lead.Age !== undefined ? lead.Age : "N/A"}
            show={[117, 131, 165, 101, 219, 220, 139].indexOf(ProductId) === -1}
          // landScapeViewRequired={landScapeViewRequired}
          />
          <GridRow
            label="Registration Date"
            value={lead?.RegistrationDate > 0 ? dayjs(lead?.RegistrationDate).format("DD/MM/YYYY") : "N/A"}
            show={[139].indexOf(ProductId) > -1}
          // landScapeViewRequired={landScapeViewRequired}
          />
          <GridRow
            label="Gender"
            value={lead.Gender}
            show={[3, 101, 131, 219, 195].indexOf(ProductId) === -1}
          // landScapeViewRequired={landScapeViewRequired}
          />
          <GridRow
            label="Pay Term"
            value={lead.PayTerm ? lead.PayTerm : "N/A"}
            show={(ProductId === 115)}
          // landScapeViewRequired={landScapeViewRequired}
          />
          <GridRow
            label="Policy Term"
            value={lead.PolicyTerm ? lead.PolicyTerm : "N/A"}
            show={[7, 138, 101].indexOf(ProductId) !== -1}
          // landScapeViewRequired={landScapeViewRequired}
          />
          <GridRow
            label="Insurance Type"
            value={lead.InsuranceType === undefined ? "N/A" : lead.InsuranceType}
            show={([131, 165].indexOf(ProductId) !== -1)}
          // landScapeViewRequired={landScapeViewRequired}
          />
          {
            !!([131].indexOf(ProductId) !== -1 && lead.LeadSource && ['winback', 'FutureProspect'].indexOf(lead.LeadSource) != -1 && lead.OccupancyName) &&
            <GridRow
              label="Occupancy"
              value={lead.OccupancyName === undefined ? "N/A" : lead.OccupancyName?.trim()}
              show={lead.SubProductTypeId && [117].indexOf(lead.SubProductTypeId) != -1 ? false : true}
            // landScapeViewRequired={landScapeViewRequired}
            />
          }
          
          <GridRow
            label="Old Policy No"
            value={lead.PrevPolicyNo ? lead.PrevPolicyNo : "N/A"}
            show={ProductId == 131 && lead.LeadSource && lead.LeadSource == "Renewal"}
          />
          <GridRow
            label="Employee Count"
            value={lead.NoOfEmployees === undefined ? "N/A" : lead.NoOfEmployees}
            show={[131].indexOf(ProductId) !== -1 && lead.SubProductProperty && lead.SubProductProperty === 'EB'}
          />
          <GridRow
            label="Coverage Type"
            value={lead.CoverageType === undefined ? "N/A" : lead.CoverageType}
            show={ProductId === 101}
          // landScapeViewRequired={landScapeViewRequired}
          />
          <GridRow
            label="Property Type"
            value={lead.PropertyType === undefined ? "N/A" : lead.PropertyType}
            show={ProductId === 101}
          />
          <GridRow
            label="PurposeType"
            value={lead.PurposeType === undefined ? "N/A" : lead.PurposeType}
            show={ProductId === 101}
          />
          <GridRow
            label="Pincode"
            value={lead.Pincode === undefined ? "N/A" : lead.Pincode}
            show={(([106, 118, 130].indexOf(ProductId) !== -1) || (ProductId === 2 && lead.LeadSource !== 'Renewal'))}
          // landScapeViewRequired={landScapeViewRequired}
          />
          <GridRow
            label="Make"
            value={lead?.Make?.trim() ? lead.Make : "N/A"}
            show={[117, 114, 148, 139].indexOf(rootScopeService.getProductId()) !== -1}
          // landScapeViewRequired={landScapeViewRequired}
          />
          <GridRow
            label="Model"
            value={lead?.Model?.trim() ? lead.Model : "N/A"}
            show={[117, 114, 148, 139].indexOf(rootScopeService.getProductId()) !== -1}
          // landScapeViewRequired={landScapeViewRequired}
          />
          <GridRow
            label="Variant"
            value={lead?.VehicleVariantName?.trim() || "N/A"}
            show={[139].indexOf(rootScopeService.getProductId()) !== -1}
          // landScapeViewRequired={landScapeViewRequired}
          />
          <GridRow
            label="Customer ID"
            value={rootScopeService.getCustomerId()}
            show={(ProductId === 115)}
          // landScapeViewRequired={landScapeViewRequired}
          />

          <GridRow
            label="Annual Income"
            value={lead.AnnualIncome ? lead.AnnualIncome : "N/A"}
            show={[7, 138, 140, 115].indexOf(ProductId) !== -1 || (ProductId === 2 && lead.AnnualIncome && lead.LeadSource !== 'Renewal')}
          // landScapeViewRequired={landScapeViewRequired}
          />

          <GridRow
            label="City"
            value={lead.Need === undefined ? "N/A" : lead.City}
            show={([115, 131, 101, 7].includes(ProductId) || ([2, 106, 118, 130].indexOf(ProductId) !== -1 && lead.LeadSource !== "Renewal"))}
          // landScapeViewRequired={landScapeViewRequired}
          />

          <GridRow
            label="Utm_Campaign"
            value={props.Utm_Campaign ? props.Utm_Campaign : "N/A"}
            show={([7, 1000, 115].includes(ProductId) && props.Utm_Campaign !== '')}
          // landScapeViewRequired={landScapeViewRequired}
          />
          <GridRow
            label="Country"
            value={(([2, 106, 118, 130].indexOf(ProductId) !== -1) && lead.LeadSource === 'Renewal')
              ? `${lead.Country}-${lead.City.substring(0, 25)}` : lead.Country}
            show={lead.Country && ([101, 131, 219, 220].indexOf(ProductId) === -1)}
            title={lead.Country}
          // landScapeViewRequired={landScapeViewRequired}
          />
          <GridRow
            label="Corporate Policy"
            value={lead.IsCorporatePlan ? "Yes (" + (lead.CorporateSumInsured ? lead.CorporateSumInsured : 0) + ")" : ""}
            show={([2].indexOf(ProductId) !== -1) && lead.IsCorporatePlan}
          />
          <GridRow
            label="Call Timings"
            value={lead.CountryPermissibleCallTime ? lead.CountryPermissibleCallTime : "N/A"}
            show={lead.Country && ['392', '91', '999', 'INDIA', '0', 'NULL'].indexOf(lead.Country) == -1}
          />
          <GridRow
            label="VahanExpiryDate"
            value={lead.VahanExpiryDate > 0 ?
              format((new Date(Math.floor(lead.VahanExpiryDate))), 'dd/MM/yyyy') :
              undefined}
            show={ProductId == 117}
          />
          <GridRow
            label="Carrier Type"
            value={ lead?.VehicleCarrierType === 1 ? "Public" :
              lead?.VehicleCarrierType === 2 ? "Private" :
              'N/A'}
            show={ProductId == 139}
          />
          <GridRow
            label="Policy Status"
            value={lead.VisibilityStatus == 1 ? "Active" : "Expired"}
            show={(ProductId == 117 || ProductId == 139) && lead.VisibilityStatus && [1, 2].indexOf(lead.VisibilityStatus) > -1}
          />
          <GridRow
            label="BreedType"
            value={lead.BreedType ? lead.BreedType : "N/A"}
            show={ProductId == 195}
          />
          <GridRow
            label="Weight"
            value={lead.Weight ? lead.Weight : "N/A"}
            show={ProductId == 195}
          />
          <GridRow
            label="Category"
            value={lead.Category ? lead.Category : "N/A"}
            show={ProductId == 195}
          />
          <GridRow
            label="PetPrice"
            value={lead.PetPrice ? lead.PetPrice : "N/A"}
            show={ProductId == 195}
          />
          {/* <GridRow
            label="LeadErrorType"
            value={TWLeadErrorType}
            show={ProductId === 114}
          /> */}
          <GridRow
            label="Lead Id"
            value={lead.LeadID}
            show={true && !IsCustomerAccess()}
          // landScapeViewRequired={landScapeViewRequired}
          />
          <GridRow
            label="Parent ID"
            value={lead.ParentID}
            show={true && !IsCustomerAccess()}
          // // landScapeViewRequired={landScapeViewRequired}
          />
          <GridRow
            label="CIBIL Score"
            value={lead.CIBILScore}
            show={!IsCustomerAccess() && [2].indexOf(ProductId) !== -1 && lead.LeadSourceId != 6 && lead.CIBILScore}
          />
          <GridRow
            label="Drone Category"
            value={DroneCategoryName}
            show={(([131].indexOf(ProductId) !== -1 && lead.SubProductTypeId && [117].indexOf(lead.SubProductTypeId) != -1) && DroneCategoryName)}
          />
          <GridRow
            label="Drone Model"
            value={DroneModelName}
            show={(([131].indexOf(ProductId) !== -1 && lead.SubProductTypeId && [117].indexOf(lead.SubProductTypeId) != -1) && DroneModelName)}
          />
          <GridRow
            label="UAS Type"
            value={DroneUASTypeName}
            show={(([131].indexOf(ProductId) !== -1 && lead.SubProductTypeId && [117].indexOf(lead.SubProductTypeId) != -1) && DroneUASTypeName)}
          />
          <GridRow
            label={IsApptfeedbackLead() ? "Verification Lead Id" : "Case Id"}
            value={lead.LeadID}
            show={IsCustomerAccess()}
          // landScapeViewRequired={landScapeViewRequired}
          />
          <GridRow
            label={"Source"}
            value={<a href="#" onClick={() => { setOpenReferralDetails(true) }}>{"Referral"}</a>}
            show={((rootScopeService.getProductId() == 2 && ((lead.UTM_Medium && lead.UTM_Medium.toLowerCase() == "health_renewal") || (lead.Utm_source && lead.Utm_source.toLowerCase() == "health_rm")))
                  || (lead.LeadSource && ["crosssell","referral"].includes(lead.LeadSource.toLowerCase()) && lead.Utm_source && ["motorcrt", "motorclaims", "twowheelerclaims"].includes(lead.Utm_source.toLowerCase()) && ((lead.LeadCreationSource.toLowerCase()).includes("bms") || (lead.LeadCreationSource.toLowerCase()).includes("claim")))
                  )}
          />
          <GridRow
            label="IP Country"
            value={lead.IpCountryName ? lead.IpCountryName : "N/A"}
            show={lead.IpCountryName && ProductId == 2 && lead.LeadSource !== 'Renewal'}
          />

          {(GetCustAppointSummaryAPIStatus != API_STATUS.LOADING) && <>
            <GridRow
              label="Appointment Lead Id"
              value={apptDetails ? <a href="#" onClick={() => OpenLeadContentOnClick(apptDetails.LeadID, apptDetails.CustomerID, apptDetails.ProductId)}>{apptDetails.LeadID}</a> : 'N.A'}
              show={IsCustomerAccess()}
              title={apptDetails ? apptDetails.LeadID : 'N.A'}
            />
            <GridRow
              label="Product"
              value={(apptDetails && apptDetails.ProductName) ? apptDetails.ProductName.split(' ')[0] : 'N.A'}
              show={IsCustomerAccess()}
            />
            <GridRow
              label="Gender"
              value={apptDetails ? apptDetails.GenderName : 'N.A'}
              show={IsCustomerAccess() && !IsApptfeedbackLead()}
            />
            <GridRow
              label="Appointment Status"
              value={apptDetails ? apptDetails.Status : 'N.A'}
              show={IsCustomerAccess()}
            />
            <GridRow
              label="Appointment DateTime"
              value={apptDetails ? dayjs(apptDetails.AppointmentDateTime).format("DD/MM/YYYY h:mm:ss A") : 'N.A'}
              show={IsCustomerAccess()}
            />
            <GridRow
              label="Appointment Address"
              value={apptDetails ? apptDetails.Address + ", " + apptDetails.Landmark + "," + apptDetails.Pincode : 'N.A'}
              show={IsCustomerAccess() && IsApptfeedbackLead()}
            />
            <GridRow
              label="Assigned To"
              value={(apptDetails && apptDetails.EmployeeId) ? apptDetails.EmployeeId : 'N.A'}
              show={IsCustomerAccess()}
            />
            <GridRow
              label="1st Reporting"
              value={(apptDetails && apptDetails.FirstReportingValue) ?
                <>{apptDetails.FirstReportingValue}
                  <a href="#" id={"span_" + apptDetails.AppointmentId} onClick={(e) => GetCallTransferUrl(apptDetails.AssignTo)} className="CallTransferCustomerAccess">
                    <span title="Transfer to Advisor's TL">Call Transfer</span>
                  </a></> : 'N.A'}
              show={IsCustomerAccess() && !IsApptfeedbackLead()}
              title={apptDetails ? apptDetails.FirstReportingValue : 'N.A'}
            />
            <GridRow
              label="2nd Reporting"
              value={(apptDetails && apptDetails.SecondReportingValue) ?
                <>{apptDetails.SecondReportingValue}
                  <a href="#" id={"span_" + apptDetails.AppointmentId} onClick={(e) => GetCallTransferUrl(apptDetails.FirstReporting)} className="CallTransferCustomerAccess">
                    <span title="Transfer to Advisor's TL">Call Transfer</span>
                  </a></>
                : 'N.A'}
              show={IsCustomerAccess() && !IsApptfeedbackLead()}
              title={apptDetails ? apptDetails.SecondReportingValue : 'N.A'}
            />
          </>}
          {
            (IsCustomerAccess() && GetCustAppointSummaryAPIStatus == API_STATUS.LOADING) && <><br /><br /><br /><br /><div style={{ margin: 'auto' }}><CircularProgress /></div></>
          }
        </Grid>
        <div className="btm-bar">
          {!props.leadViewOnly && !props.LeadOnlyViewNew && <div className="bottom-icon">
            {ShowFreshJourney && !IsApptfeedbackLead() && (!IsLeadContent() || isDesktop) &&
              <p title="Fresh Journey"
                onClick={handleFreshJourneyClick}
              >
                <img
                  alt="fresh Journey"
                  src={CONFIG.PUBLIC_URL + "/images/salesview/freshJourney.svg"}
                />{" "}
              </p>
            }
            {/* <Button
                disabled={IsDisabledCard}
                className="ContinueJourneybtn"
              >
                <a target="_blank" href="https://www.policybazaar.com/offline_crosssell/">Fresh Journey</a>
              </Button> */}
            {isViewQuoteVisible && !IsApptfeedbackLead() &&
              <p
                onClick={() => {
                  if (!IsDisabledCard || IsSmeAdmin) OpenViewQuotes(lead.LeadID);
                }}
                title="View Quotes"
                className={IsDisabledCard && IsSmeAdmin ? "QuotesHighlight" : null}
              >
                <img
                  alt="View Quotes"
                  className={(IsDisabledCard && !IsSmeAdmin) ? "disabledIcon" : null}
                  src={CONFIG.PUBLIC_URL + "/images/salesview/price.svg"}
                />
              </p>
            }


            {RejectionStatus.indexOf(lead.StatusId) !== -1 && User.RoleId !== 13 &&
              <p title="Click here to Reopen Lead"
                onClick={ReOpenMatrixLead}
              >
                <img
                  alt="Reopen lead"
                  src={CONFIG.PUBLIC_URL + "/images/salesview/like.svg"}
                />{" "}

              </p>
            }
            {ShowAssignCriticalComponents && ((!IsApptfeedbackLead() && (lead.LeadOrder == '1' || lead.LeadOrder == '2' || lead.LeadOrder == '3')) ||
              ((IsApptfeedbackLead() && TotalCallTT > SV_CONFIG['TTFeedbackleadRejectBtn'] && (lead.LeadOrder == '1' || lead.LeadOrder == '2' || lead.LeadOrder == '3')))) &&
              <p title="Click here to Reject Lead"
                onClick={(IsDisabledCard || lead.StatusMode === "N")
                  ? null
                  : OpenRejectLead}
              >
                <img
                  alt="reject lead"
                  className={
                    (IsDisabledCard || lead.StatusMode === "N")
                      ? "disabledIcon"
                      : null
                  }
                  src={CONFIG.PUBLIC_URL + "/images/salesview/dislike.svg"}
                />{" "}

              </p>
            }
            {!IsApptfeedbackLead() && <>
              <p title="Click here to set Primary lead"
                onClick={
                  (IsDisabledCard || lead.StatusMode === "N" || isLeadPrimary)
                    ? null
                    : SetPrimaryLead
                }
              >
                <img
                  alt="set Primary lead"
                  className={
                    IsDisabledCard || lead.StatusMode === "N" || isLeadPrimary
                      ? "disabledIcon"
                      : null
                  }
                  src={CONFIG.PUBLIC_URL + "/images/salesview/star.svg"}
                />{" "}
              </p>
              {((ProductId == 7 && SV_CONFIG.ShowTermDiscountPlan) || ProductId == 2 || ProductId == 130) && lead.LeadCreationSource && lead.LeadCreationSource.toLowerCase() == "pbcromaservicev2" && lead.StatusMode === 'P' &&
                <p title="Avail Discount"
                  onClick={showAvailDiscountPopup}
                >
                  {lead.DiscountAvailedCount > 0 &&
                    <span className="bulletPoint"></span>
                  }
                  <img
                    alt="Avail Discount"
                    src={CONFIG.PUBLIC_URL + "/images/salesview/DiscountAvail.svg"}

                  />{" "}
                </p>
              }
              {(ProductId == 2) && lead.LeadCreationSource && lead.LeadCreationSource.toLowerCase() == "pbcromaservicev2" 
                && lead.StatusMode === 'P' && lead.HealthIsSegment == 1 &&
                <p title="Additional Lead Info"
                  onClick={showAdditionalInfoPopup}
                >
                  <span className="bulletPoint"></span>
                  <img
                    alt="Additional Lead Info"
                    src={CONFIG.PUBLIC_URL + "/images/salesview/AdditionalInfoIcon.svg"}

                  />{" "}
                </p>
              }
              {(ProductId == 117) && lead.LeadSource === 'Renewal' &&
                <p title="Last Year Policy Details"
                  onClick={showLastYearPolicyDetailsPopup}
                >
                  <img
                    alt="Last Year Policy Details"
                    src={CONFIG.PUBLIC_URL + "/images/salesview/LastYearPolicyIcon.svg"}
                  />{" "}
                </p>
              }
              {[106, 118, 130, 2].indexOf(ProductId) !== -1 && lead.LeadSource === 'Renewal' && lead.Wasport == true &&
                <p title="Click here to Get Port Details"
                  onClick={showPortDetailsPopup}
                >
                  <img
                    alt="Port Details"
                    src={CONFIG.PUBLIC_URL + "/images/salesview/portIcon.svg"}
                  />{" "}

                </p>
              }
              {([115].includes(ProductId) && (!props.leadViewOnly && !props.LeadOnlyViewNew) && lead.LeadCreationSource && lead.LeadCreationSource.toLowerCase() == "pbcromaservicev2" && chkSmartInvestmentEnable() == true) &&
                <p title="Smart Invest Tool"
                  onClick={handleOpenSmartInvestToolLink}
                >
                  <img
                    alt="Smart Invest Tool"
                    src={CONFIG.PUBLIC_URL + "/images/salesview/SmartInvestmentIcon.svg"}
                  />{" "}
                </p>
              }
              <p title="Pitch Recommendation"
                onClick={ShowPitchRecommendation}
              >
                <img
                  alt="Pitch Recommendation"
                  src={CONFIG.PUBLIC_URL + "/images/salesview/announcement.svg"}
                />{" "}
              </p>
            </>}
            {/* <p onClick={}><img className={!IsPriorityLead  ? 'disabledIcon' : null} src={CONFIG.PUBLIC_URL + "/images/salesview/star.svg"} /> </p> */}
          </div>}
          <div className="ContinueJourney">
            {!props.leadViewOnly && !props.LeadOnlyViewNew && !IsApptfeedbackLead() && <Button
              disabled={IsDisabledCard}
              className="ContinueJourneybtn"
              onClick={handleOpenContinueLink}
            >
              Continue Journey
            </Button>}
            {([115].includes(ProductId) && (!props.leadViewOnly && !props.LeadOnlyViewNew) && !IsApptfeedbackLead() && lead.LeadCreationSource && lead.LeadCreationSource.toLowerCase() == "pbcromaservicev2" && chkSmartInvestmentEnable() == true) &&
              <Button
                className="ContinueJourneybtn"
                onClick={showInvestmentToolDetails}
              >
                View Investment Details
              </Button>}
            {/* {ProductId === 115 && lead.EnquiryID > 0 &&
              <Button
                disabled={IsDisabledCard}
                className="ContinueJourneybtn"
              >
                <a target="_blank" href={lead.ContinueJourneyURL + "&iscobrowse=true"}>Start Co-Browsing ({lead.EnquiryID})</a>
              </Button>
            } */}
            {IsViewDetailsVisible &&
              <Button
                disabled={IsDisabledCard}
                className="viewBtn"
                onClick={() => { setOpenProposalDetails(true) }}
                title={([2, 106, 118, 130].includes(ProductId) && lead.LeadSource !== 'Renewal')
                  ? "Profile Details"
                  : 'View Details'}
              >
                {([2, 106, 118, 130].includes(ProductId) && lead.LeadSource !== 'Renewal')
                  ? "Profile Details"
                  : 'View Details'}
              </Button>
            }
            {IsApptSummaryVisible &&
              <Button
                //disabled={IsDisabledCard}
                className="viewBtn"
                onClick={showAppointmentSummary}
                title={'Appointment Summary'}
              >
                {'Appointment Summary'}
              </Button>
            }
            {IsRenewalDetailsVisible &&
              <Button
                disabled={IsDisabledCard}
                className="viewBtn"
                onClick={() => { setOpenRenewalDetails(true) }}
                title={'Renewal Details'}
              >
                Renewal Details
              </Button>
            }
            {/* {ShowFreshJourney &&
              <Button
                disabled={IsDisabledCard}
                className="ContinueJourneybtn"
                onClick={handleFreshJourneyClick}
              >
                Fresh Journey
              </Button>
            } */}
          </div>
        </div>

      </div>
      <ErrorBoundary name="EditPopUp">
        <EditPopUp
          open={OpenEditPopUp}
          handleClose={() => {
            setOpenEditPopUp(false);
          }}
          getLeads={props.getLeads}
          lead={lead}
        />
      </ErrorBoundary>
      <ErrorBoundary name="PreExistingDiseasePopup">
        <PreExistingDiseasePopup
          open={preExistingDiseasePopUp}
          handleClose={() => {
            setPreExistingDiseasePopUp(false);
          }}
          // getLeads={props.getLeads}
          lead={lead}
        />
      </ErrorBoundary>
      <ErrorBoundary name="PreExistingDiseasePopup">
        <PlanPortDetailsPopUp
          open={planPortDetailsPopUp}
          handleClose={() => {
            setplanPortDetailsPopUp(false);
          }}
          lead={lead}
        />
      </ErrorBoundary>
      <ErrorBoundary name="ProfessionTypePopUp">
        <GenericModalPopup
          open={ProfessionTypePopUp}
          handleClose={() => {
            setProfessionTypePopUp(false);
          }}
          Title="Self Employed Customer"
          message="Pitch from 'Special plans for self employed' or PNB SJB to get issuance of 65-70% instead of 15-20%"
        />
      </ErrorBoundary>

      <ErrorBoundary name="TermSpecialCustomerPopUp">
        <TermSpecialCustomer
          open={TermSpecialCustomerPopUp}
          handleClose={() => {
            setTermSpecialCustomerPopUp(false);
          }}
          UTMMedium={lead.UTM_Medium}
          Title="Term Special Customer Pitch"
        />
      </ErrorBoundary>

      <ErrorBoundary name="TermSpecialNRICustomerPopUp">
        <TermSpecialNRICustomer
          open={TermSpecialNRICustomerPopUp}
          handleClose={() => {
            setTermSpecialNRICustomerPopUp(false);
          }}
          UTMMedium={lead.UTM_Medium}
          Title="Term Special Customer Pitch"
        />
      </ErrorBoundary>

      <ErrorBoundary name="ContinueJourneyPopUp">
        <ContinueJourneyPopUp
          open={OpenContinueLink}
          handleClose={() => {
            setOpenContinueLink(false);
          }}
          leadId={lead.LeadID}
        />
      </ErrorBoundary>

      <ErrorBoundary name="ContinueJourneyPopUp">
        <NewContinueJourneyPopUp
          open={OpenNewContinueLink}
          handleClose={() => {
            setOpenNewContinueLink(false);
          }}
          lead={lead}
        />
      </ErrorBoundary>
      <ErrorBoundary name="PortDetailsPopUp">
        <PortDetailsPopUp
          open={OpenPortDetailsPopUp}
          handleClose={() => {
            setOpenPortDetailsPopUp(false);
          }}
          leadId={lead.LeadID}
        />
      </ErrorBoundary>
      <ErrorBoundary name="RejectLeadPopUp">
        <RejectLeadPopUp
          open={OpenRejectLeadPopUp}
          getLeads={props.getLeads}
          LeadId={lead.LeadID}
          lead={lead}
          handleClose={() => {
            setRejectLeadPopUp(false);
          }}
        />
      </ErrorBoundary>
      <ErrorBoundary name="UploadDocPopUp">
        <UploadDocPopUp
          open={OpenUploadDocPopUp}
          handleClose={() => {
            setOpenUploadDocPopUp(false);
          }}
          LeadId={lead.LeadID}
        />
      </ErrorBoundary>
      <ErrorBoundary name="RenewalDetailsPopup">
        {OpenRenewalDetails && <RenewalDetailsPopup
          open={OpenRenewalDetails}
          handleClose={() => {
            setOpenRenewalDetails(false);
          }}
          LeadId={lead.LeadID}
          ProductId={lead.ProductID}
        />}
      </ErrorBoundary>
      <ErrorBoundary name="ProposalDetails">
        <ProposalDetails
          open={OpenProposalDetails}
          getLeads={props.getLeads}
          LeadId={lead.LeadID}
          ProductId={lead.ProductID}
          handleClose={() => {
            setOpenProposalDetails(false);
          }}
        />
      </ErrorBoundary>
      <ErrorBoundary name="CallTransferModal">
        <CallTransferModal
          url={CallTransferUrl}
          open={OpenCallTranserModal}
          handleClose={() => { setOpenCallTranserModal(false) }} />
      </ErrorBoundary>
      {IsCustomerAccess() && <ErrorBoundary name="AppointmentSummaryPopup">
        <AppointmentSummaryPopup
          open={OpenApptSummaryPopup}
          handleClose={() => { setOpenApptSummaryPopup(false) }}
          SearchType={3}
          SearchText={lead.LeadID}
        />
      </ErrorBoundary>
      }
      <ErrorBoundary name="ViewQuotesPopup">
        <ViewQuotesPopup
          open={OpenViewQuotesPopup}
          handleClose={() => { setOpenViewQuotesPopup(false) }}
          allowAction={allowActionInSMEquotes}
          leadId={lead.LeadID}
          LeadSourceId={lead.LeadSourceId}
          SubProductTypeId={lead.SubProductTypeId}
          SumAssured={lead.SA}
          LeadStatusId={lead.StatusId}
          SubProductProperty={lead.SubProductProperty ? lead.SubProductProperty : ""}
        />
      </ErrorBoundary>
      <ErrorBoundary name="ClaimDetailsPopup">
        <ClaimDetailsPopup
          open={OpenClaimsPopup}
          handleClose={() => {
            setOpenClaimsPopup(false);
          }}
          leadId={lead.LeadID}
        />
      </ErrorBoundary>
      <ErrorBoundary name="PayemntDetailsPopup">
        <PaymentDetailsPopup
          open={OpenPaymentDetailsPopUp}
          handleClose={() => {
            setPaymentDetailsPopUp(false);
          }}
          LeadID={lead.LeadID}
        />
      </ErrorBoundary>
      {OpenAvailDiscountPopUp &&
        <ErrorBoundary name="AvailDiscountPopup">
          <AvailDiscounts
            open={OpenAvailDiscountPopUp}
            handleClose={() => { setOpenAvailDiscountPopUp(false); }}
            LeadID={lead.LeadID}
            ProductID={lead.ProductID}
            LeadAssignedUser={lead.LeadAssignedUser}
          />
        </ErrorBoundary>
      }
      {OpenAdditionalInfoPopup && (ProductId == 2) &&
        <ErrorBoundary name="AdditionalInfoPopup">
          <LeadAdditionalInfoPopup
            open={OpenAdditionalInfoPopup}
            handleClose={() => { setOpenAdditionalInfoPopup(false); }}
            lead={lead}
          />
        </ErrorBoundary>
      }
      {OpenLastYearPolicyDetailsPopup && (ProductId == 117) &&
        <ErrorBoundary name="LastYearPolicyDetailsPopup">
          <LastYearPolicyDetailsPopup
            open={OpenLastYearPolicyDetailsPopup}
            handleClose={() => { setOpenLastYearPolicyDetailsPopup(false); }}
            lead={lead}
          />
        </ErrorBoundary>
      }
      {OpenImgPopup && <ImagePopup
        open={OpenImgPopup}
        handleClose={() => { setOpenImgPopup(false) }}
        ImgUrl={ImgUrl}
        alt="banner"
      />
      }
      {OpenSmartInvestmentToolDetailsPopUp &&
        <ErrorBoundary name="SmartInvestmentDetails">
          <SmartInvestmentDetails
            open={OpenSmartInvestmentToolDetailsPopUp}
            handleClose={() => { setOpenSmartInvestmentToolDetailsPopUp(false); }}
            LeadID={lead.LeadID}
          />
        </ErrorBoundary>
      }
      {ShowMaxTermPopUp &&
        <ErrorBoundary name="ShowMaxTermPopUp">
          <HdfcPasaPopup
            open={true}
            handleClose={() => { setShowMaxTermPopUp(false) }}
            Title="Customer eligible for Max Term-By-Invite"
            message="Customer eligible for Max Term-By-Invite"
            IsMaxTermPopUp={true}
          />
        </ErrorBoundary>
      }
      {ShowAnualOpenPopup &&
        <ErrorBoundary name="ShowAnualOpenPopup">
          <AnualOpenLeadsPanel
            open={true}
            handleClose={() => { setShowAnualOpenPopup(false) }}
            Title="Customer is already having the Annual Open Policy"
            message="Customer is already having the Annual Open Policy"
          />
        </ErrorBoundary>
      }
      {OpenReferralDetails &&
        <ReferralDetailsPopup
          open={OpenReferralDetails}
          handleClose={() => {
            setOpenReferralDetails(false);
          }}
          LeadId = {lead.LeadID}
          ReferralLeadId={lead.ReferralID}
          Utm_source={lead.Utm_source}
          UTM_Medium={lead.UTM_Medium}
          ProductID = {rootScopeService.getProductId()}
          LeadSource = {lead.LeadSource}
          LeadCreationSource = {lead.LeadCreationSource}
        />}
      {OpenCovidInv &&
        <CovidInvPopup
          open={OpenCovidInv}
          handleClose={() => {
            setOpenCovidInv(false);
          }}
          Utm_campaign={lead.Utm_campaign}
        />}
      {OpenRejectedLeadPopup &&
        <RejectedLeadDetailsPopup
          open={OpenRejectedLeadPopup}
          handleClose={() => {
            setOpenRejectedLeadPopup(false);
          }}
          lead={lead}
        />}
      {OpenPitchRecommendationPopup &&
        <PitchRecommendationPopup
          open={OpenPitchRecommendationPopup}
          handleClose={() => {
            setOpenPitchRecommendationPopup(false);
          }}
          lead={lead}
        />}
    </>
  );
  return (
    card
  );
}