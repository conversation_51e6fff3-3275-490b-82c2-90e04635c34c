import { SV_CONFIG } from "../../../../../appconfig";
import { gaEvent<PERSON>ames, gaEventTracker } from "../../../../../helpers";
import { CALL_API } from "../../../../../services";
import rootScopeService from "../../../../../services/rootScopeService";
import User from "../../../../../services/user.service";

export const GetSOSBookingsDeskURL = () => {
    const input = {
        url: "api/BMS/SalesLogin",
        method: 'POST',
        service: "MatrixCoreAPI",
        requestData: { 'UserId': User.UserId, 'LeadId': rootScopeService.getLeadId() }
    }
    return CALL_API(input)
}
export const ShowNewMyBookings = () => {
    let url = SV_CONFIG["matrixdashboard"][SV_CONFIG["environment"]] + `/admin/MyBookings`;
     if (User.ProductList && User.ProductList.length > 0 && SV_CONFIG['NewMyBookingProducts'] && User.ProductList.some(product => SV_CONFIG['NewMyBookingProducts'].includes(product.ProductId))) {
         url = SV_CONFIG["matrixdashboard"][SV_CONFIG["environment"]] + `/admin/MyBooking_V2`;
    }
    window.open(url);
}
export const ShowMyBookings = () => {
    window.open('/PGV/Leads/BookingDetails.aspx?Source=SV&ProductId=' + rootScopeService.getProductId(), '_blank');
}
export const OpenPBQuiz = () => {
    window.open(SV_CONFIG["LMS"][SV_CONFIG["environment"]] + `/client/QuizRedirection?EmployeeId=${User.EmployeeId}`);
}
export const ShowQcReport = () => {
    window.open('/PGV/Leads/QcReport.aspx', '_blank');
}
export const ShowDuplicateLeads = () => {
    window.open('/PGV/Administration/ReleasedNDNC.aspx?pg=dl', '_blank');
}
export const ShowDuplicateLeadsNew = () => {
     window.open(SV_CONFIG["matrixdashboard"][SV_CONFIG["environment"]] + `/admin/DuplicateLeads`);
}
export const ShowCreateLeads = () => {
    window.open('/PGV/Leads/CreateLeads.aspx', '_blank');
}
export const ShowSOSBookings = () => {
    window.open('/PGV/Leads/MyBooking.aspx', '_blank');
}
export const ShowReleaseChat = () => {
    window.open('/PGV/Administration/ReleaseChatRestriction.aspx', '_blank');
}
export const ShowPGView = () => {
    window.open('/PGV/Leads/PGView.aspx', '_blank');
}
export const ShowPaymentAttemptsDashboard = () => {
    window.open('../PaymentAttemptLeads ', '_blank');
}
export const OpenPBSchool = () => {
    window.open(SV_CONFIG["LMS"][SV_CONFIG["environment"]] + '/school/PBSchool/CourseDashboard');
}
export const OpenLeaveManagement = () => {
    window.open(SV_CONFIG["RMS"][SV_CONFIG["environment"]] + '/admin/rmsredirection?target=L2FnZW50L0xlYXZlTWFuYWdlbWVudC8=');
}
export const ShowSOSBookingsDesk = () => {
    GetSOSBookingsDeskURL().then(function (resultData) {
        if (resultData) {
            window.open(resultData, '_blank');
        }
    });
    gaEventTracker(gaEventNames.BookingDesk, User.EmployeeId, rootScopeService.getLeadId());
}

export const ShowLeadSummary = () => {

    let productid = rootScopeService.getProductId();
    if (productid === 7 || productid === '7')
        productid = 1000;
    window.open('/PGV/Leads/AgentScreen.aspx?Source=SV&ProductId=' + productid, '_blank');
}


export const GetAssignedLeadsService = () => {
    const input = {
        url: `onelead/api/LeadPrioritization/GetUserAssignedLeads/${User.UserId}`,
        method: 'GET', service: 'MatrixCoreAPI',
    };
    return CALL_API(input);
}
export const GetReassignedLeadsService = () => {
    const input = {
        url: `api/SalesView/GetReassignedLeads`,
        method: 'GET', service: 'MatrixCoreAPI',
    };
    return CALL_API(input);
}

export const GetFosAgentAssignedLeads = (NoOfDays) => {
    const input = {
        url: `api/fos/GetAgentAssignedLeads?NoOfDays=${NoOfDays}`,
        method: 'GET', service: 'MatrixCoreAPI',
    };
    return CALL_API(input);
}

export const GetBookingCreditLog = (ECode) => {
    const input = {
        url: `coremrs/api/LeadDetails/GetBookingCreditLogData?ECode=${ECode}`,
        // https://localhost:44321/coremrs/api/LeadDetails/GetBookingCreditLogData?ECode=qw12
        method: 'GET', service: 'MatrixCoreAPI',
    }
    return CALL_API(input);
}

export const GetRejectedLeadsService = () => {
    let SubStatusID = SV_CONFIG["RejectedSubStatusGroups"][SV_CONFIG["environment"]];
    const input = {
        url: `coremrs/api/LeadDetails/GetRejectedLeads/${SubStatusID}/${User.UserId}`,
        method: 'GET', service: 'MatrixCoreAPI',
    };
    return CALL_API(input)
}

export const GetRejectedLeadsBookedService = () => {
    const input = {
        url: `coremrs/api/MRSCore/GetCRTSuggestedPlans`,
        method: "GET",
        service: "MatrixCoreAPI",
        timeout: 's'
    };
    return CALL_API(input)
}

export const ReleaseRequestService = function (reqData) {
    const input = {
        url: "onelead/api/LeadPrioritization/ReleaseRequest",
        method: 'POST', service: 'MatrixCoreAPI',
        requestData: {
            LeadIds: reqData.LeadIds,
            UserId: reqData.UserId,
            Status: reqData.Status
        }
    };
    return CALL_API(input);
};

export const CallReleaseService = function (leadid) {
    const input = {
        url: "onelead/api/LeadPrioritization/CallRelease/" + leadid + "/" + User.UserId + "/" + User.EmployeeId,
        method: 'GET', service: 'MatrixCoreAPI'
    };
    return CALL_API(input);
};

export const GetReleaseLeadsService = () => {
    let userid = User.UserId;
    let roleId = User.RoleId;
    let prodId = rootScopeService.getProductId();
    const input = {
        url: `onelead/api/LeadPrioritization/GetReleaseLeads/${userid}/${prodId}/${roleId}`,
        method: 'GET', 
        service: 'MatrixCoreAPI'
    }
    return CALL_API(input);
}
export const GetReleaseLeadsCountService = () => {
    const input = {
        url: `onelead/api/LeadPrioritization/GetReleaseLeadsCount/${User.UserId}`,
        method: 'GET', service: 'MatrixCoreAPI',
    };
    return CALL_API(input);
}

export const GetImportantLeadsService = () => {
    const input = {
        url: `onelead/api/LeadPrioritization/GetStarLeads/${User.UserId}`,
        method: 'GET', service: 'MatrixCoreAPI',
        requestData: { CustomerId: rootScopeService.getCustomerId() }
    };
    return CALL_API(input);
}

export const GetNotContactedLeadsService = (LastDays) => {
    const input = {
        url: `onelead/api/LeadPrioritization/GetNotContactedLeads/${User.UserId}/${LastDays}`,
        method: 'GET', service: 'MatrixCoreAPI',
        // requestData: { CustomerId: rootScopeService.getCustomerId() }
    };
    return CALL_API(input);
}

export const GetExpiryLeadsService = () => {
    const input = {
        url: `onelead/api/LeadPrioritization/GetExpiringLeads/${User.UserId}/${rootScopeService.getProductId()}`,
        method: 'GET', service: 'MatrixCoreAPI',
    };
    return CALL_API(input);
}

export const GetHWOpportunityDataService = () => {
    const input = {
        url: `api/SalesView/GetHWEligibleData`,
        method: 'GET', service: 'MatrixCoreAPI',
    };
    return CALL_API(input);
}

export const PushHWEligibleDataService = (reqData) => {
    const input = {
        url: `api/WebSiteService/PushHWEligibleData`,
        method: 'POST', service: 'MatrixCoreAPI',
        requestData: reqData
    };
    return CALL_API(input);
}

export const GetPaymentFailedCasesInfoService = function (ProductID, UserId) {
    let input = {};
        if ( [12,11,2,19].includes(User.RoleId) ) {
            input = {
                url: 'api/SalesView/GetPaymentFailedCasesInfo?ProductID=' + ProductID + '&UserId=' + UserId,
                method: 'GET', service: 'MatrixCoreAPI'
            }
        } else {
            input = {
                url: 'api/SalesView/GetPaymentFailedCasesInfo?ProductID=' + ProductID,
                method: 'GET', service: 'MatrixCoreAPI'
            }
        }
    return CALL_API(input);
}
export const GetBusinessHealthRatingService = function (UserId,productid) {
    const input = {
        url: `api/SalesView/GetBusinessHealthRatingPercentage?AgentId=` + UserId + `&ProductId=` + productid,
        //url: `api/SalesView/GetBusinessHealthRatingPercentage?AgentId=36853`,
        method: 'GET', service: 'MatrixCoreAPI',
    };
    return CALL_API(input);
}
export const GetConsolidatedBizHealthDataService = function (UserId, productid) {
    const input = {
        url: `api/SalesView/GetConsolidatedBusinessHealthRating?AgentId=` + UserId + `&ProductId=` + productid,
        //url: `api/SalesView/GetConsolidatedBusinessHealthRating?AgentId=36853`,
        method: 'GET', service: 'MatrixCoreAPI',
    };
    return CALL_API(input);
}
export const GetBHRPercentageAndColorService = function (UserId, productid) {
    const input = {
        url: `api/SalesView/GetBHRPercentageAndColor/` + UserId + `/` + productid,
        method: 'GET', service: 'MatrixCoreAPI',
    };
    return CALL_API(input);
}
export const GetUserSuperGroup = function (UserId, productid) {
    const input = {
        url: `api/SalesView/GetUserSuperGroup?AgentId=` + UserId + `&ProductId=` + productid,
        method: 'GET', service: 'MatrixCoreAPI',
    };
    return CALL_API(input);
}
export const IsNewPaymentOverdueGroup = () => {
    let usergrp = User.UserGroupList;
    let isVisible = false;
    const NewPaymentOverdueGroup = (window && window.SV_CONFIG_UNCACHED && Array.isArray(window.SV_CONFIG_UNCACHED.NewPaymentOverdueGroup) && window.SV_CONFIG_UNCACHED.NewPaymentOverdueGroup) || SV_CONFIG.NewPaymentOverdueGroup;
    Array.isArray(usergrp) && usergrp.forEach(function (item) {
        if (Array.isArray(NewPaymentOverdueGroup) && NewPaymentOverdueGroup.indexOf(item.GroupId) > -1) {
            isVisible = true;
        }
    });
    return true;
}
export const UpdatePaymentStatusForBookedLeadsService = function (reqData, ProductID) {
        const input = {
            url: "external/api/SalesViewExternal/GetUpdatedPaymentStatusForBookedLeads",
            method: 'POST', service: 'MatrixCoreAPI', timeout: "l",
            requestData: {
                LeadId: reqData.LeadId,
                PolicyNo: reqData.PolicyNo,
                ApplicationNo: reqData.ApplicationNo,
                InsurerId: reqData.InsurerId,
                ProductID: ProductID
            }
        };
    return CALL_API(input);
}
export const GetUpdatedPaymentStatus = function (reqData) {
    const input = {
        url: 'coremrs/api/LeadDetails/IsPaymentDoneatPG?LeadID=' + reqData.LeadId + '&ProductID=2',
        method: 'GET', service: 'MatrixCoreAPI', timeout: "l"
    };
    return CALL_API(input);
}
export const SendPaymentFailedComm = function (reqData, IsRefreshData, ProductID) {
    let IsRefreshed = 0;
    if (IsRefreshData === true) {
        IsRefreshed = 1;
    }
    const input = {
        url: "external/api/SalesViewExternal/SendPaymentFailedCommunication",
        method: 'POST', service: 'MatrixCoreAPI', timeout: "l",
        requestData: {
            LeadId: reqData.LeadId,
            PolicyNo: reqData.PolicyNo,
            ApplicationNo: reqData.ApplicationNo,
            InsurerId: reqData.InsurerId,
            UniqueId: reqData.UniqueIdForComboPolicy,
            IsRefreshed: IsRefreshed,
            ProductID: ProductID,
            RenewalLink: (IsRefreshData && reqData.PaymentLink) ? reqData.PaymentLink : ''
        }
    };
    return CALL_API(input);
}
export const SendPaymentOverdueComm = function (reqData, ProductID, Source, GroupId, IsHealthPersistency) {
    var DueDate = reqData.NextDueDate;
    if (reqData.NextDueDate && reqData.NextDueDate.length > 1) {
        var NextDueDate = DueDate.split("/");
        DueDate = NextDueDate[1] + '/' + NextDueDate[0] + '/' + NextDueDate[2]
    }
    const input = {
        url: 'api/SalesView/SendPaymentCommunication?LeadID=' + reqData.LeadId + '&PendingAmount=' + reqData.DueAmount + '&ProductID=' + ProductID + '&DueDate=' + DueDate + '&Source=' + Source + '&GroupId=' + GroupId + '&IsHealthPersistency=' + (IsHealthPersistency == true ? "true" : "false") + "&UserId=" + User.UserId + "&EmployeeId=" + User.EmployeeId,
        method: 'GET', service: 'MatrixCoreAPI', timeout: "l"
    };
    return CALL_API(input);
}
export const SendEmandateLinktoCust = function (reqData) {
    const input = {
        url: 'api/SalesView/SendEmandateEnableCommunication?LeadId=' + reqData.LeadId + '&Source=' + reqData.Source,
        method: 'GET', service: 'MatrixCoreAPI', timeout: "l"
    };
    return CALL_API(input);
}
export const GetAdditionalDetailsReferralLead = function (LeadId) {
    const input = {
        url: 'api/SalesView/GetAdditionalDetails/' + LeadId,
        method: 'GET', service: 'MatrixCoreAPI', timeout: "l"
    };
    return CALL_API(input);
}
export const OpenAgentDashboard = function () {
    window.open(`${SV_CONFIG["agentdashboard"][SV_CONFIG["environment"]]}?event=click&product=${isAgentDashboardVisible()}`, '_blank');
}

export const IsHealthRenewalAgent = () => {
    var isRenewalAgent = false;
    var UserBUMappingList = User.UserBUMapping;
    if (UserBUMappingList && Array.isArray(UserBUMappingList)) {
        UserBUMappingList.forEach(function (val, key) {
            if (val.IsRenewal && ([106, 118, 130, 2].indexOf(val.ProductId) !== -1)) {
                isRenewalAgent = true;
            }
        });
    }
    return isRenewalAgent;
}
export const IsRenewalAgent = (productArray = null) => {
    var isRenewalAgent = false;
    var UserBUMappingList = User.UserBUMapping;
    if (UserBUMappingList && Array.isArray(UserBUMappingList)) {
        UserBUMappingList.forEach(function (val, key) {
            if (val.IsRenewal && (productArray == null || (Array.isArray(productArray) && productArray.indexOf(val.ProductId) !== -1))) {
                isRenewalAgent = true;
            }
        });
    }
    return isRenewalAgent;
}
export const isAgentDashboardVisible = () => {
    let productId = 0;
    if (User.RoleId === 13) {
        var usergrp = User.ProductList;

        usergrp.forEach(function (item, key) {
            if ([7, 1000, 2, 117, 115, 131].indexOf(item.ProductId) > -1) {
                productId = item.ProductId;
            }
        });
    }
    return productId;
}
export const OpenInternalEmailINNewTab = () => {
    return true;
    // var usergrp = User.ProductList;
    // var newTab = false;
    // Array.isArray(usergrp) && usergrp.forEach(function (item, key) {
    //     if ([131, 101,115].indexOf(item.ProductId) > -1) {
    //         newTab = true;
    //     }
    // });
    // return newTab;
}

export const IsPaymentOverdueVisible = () => {
    let productList = User.ProductList;
    let usergrp = User.UserGroupList;
    let isVisible = false;
    //let isValidGroupList = false;
    // const PaymentFailedDashboardGrps = (window && window.SV_CONFIG_UNCACHED && Array.isArray(window.SV_CONFIG_UNCACHED.PaymentFailedDashboardGrps) && window.SV_CONFIG_UNCACHED.PaymentFailedDashboardGrps) || SV_CONFIG.PaymentFailedDashboardGrps;
    // Array.isArray(usergrp) && usergrp.forEach(function (item) {
    //     if (Array.isArray(PaymentFailedDashboardGrps) && PaymentFailedDashboardGrps.indexOf(item.GroupId) > -1) {
    //         isValidGroupList = true;
    //     }
    // });
    Array.isArray(productList) && productList.forEach(function (item, key) {
        if (([115, 1001, 2, 7, 1000].indexOf(item.ProductId) !== -1)) {
            isVisible = true;
        }
    });
    return isVisible;
}

export const CreateReferralLeadService = function (reqData) {
    const input = {
        url: `api/WebSiteService/CreateLead`,
        method: "POST",
        service: "MatrixCoreAPI",
        requestData: reqData,
        //timeout: 's'
        timeout: (reqData && reqData.ProductId && reqData.ProductId == 115) ? SV_CONFIG["TimeoutCreateLeadInvestment"][SV_CONFIG["environment"]] : 3000
    };
    return CALL_API(input);
};


export const IsPaymentAttemptDashboardVisible = () => {
    let isVisible = false;

    const PaymentAttemptPrds = (window && window.SV_CONFIG_UNCACHED
        && Array.isArray(window.SV_CONFIG_UNCACHED.PaymentAttemptPrds)
        && window.SV_CONFIG_UNCACHED.PaymentAttemptPrds
    ) || SV_CONFIG.PaymentAttemptPrds;

    let userProductList = User.ProductList;
    if (User.RoleId === 13) {
        Array.isArray(userProductList) && userProductList.forEach(function (item) {
            if (Array.isArray(PaymentAttemptPrds) && PaymentAttemptPrds.indexOf(item.ProductId) !== -1) {
                isVisible = true;
            }
        })
    }
    const PaymentAttemptGrps = (window && window.SV_CONFIG_UNCACHED
        && Array.isArray(window.SV_CONFIG_UNCACHED.PaymentAttemptGrps)
        && window.SV_CONFIG_UNCACHED.PaymentAttemptGrps
    ) || SV_CONFIG.PaymentAttemptGrps;
    var usergrp = User.UserGroupList;
    if (User.RoleId === 13) {
        Array.isArray(usergrp) && usergrp.forEach(function (item) {
            if (Array.isArray(PaymentAttemptGrps) && PaymentAttemptGrps.indexOf(item.GroupId) > -1) {
                isVisible = true;
            }
        });
    }
    return isVisible;
}

export const GetBookingDetailsForCreditChangeService = function (LeadID, ProductId) {
        let input = {
                url:  `api/SalesView/GetBookingDetailsForCreditChange?leadId=${LeadID}&ProductId=${ProductId}`,
                method: 'GET', service: 'MatrixCoreAPI'
            }
    return CALL_API(input);
}
export const GetHierarchialAgentListService = function (ProductID) {
    let input = {
            url: 'api/SalesView/GetHierarchialAgentList?ProductID=' + ProductID,
            method: 'GET', service: 'MatrixCoreAPI'
        }
return CALL_API(input);
}
export const ValidateReferenceLeadService = function (bookingId,ReferenceId) {
    let input = {
            url: 'api/SalesView/ValidateSalesCreditReferenceLead/' + bookingId + '/' + ReferenceId,
            method: 'GET', service: 'MatrixCoreAPI'
        }
return CALL_API(input);
}
export const GetReasonMasterService = () =>
{
    let input = 
    {
        url :  `api/SalesView/GetCreditChangeReasonMaster`,
        method: 'GET', service : 'MatrixCoreAPI'
    }
    return CALL_API(input);
}
export const GetUserAuthorizedAttributesValues = () =>
{
    let input = 
    {
        url :  `coremrs/api/Agent/GetUserAuthorizedAttributesValues/${User.UserId}`,
        method: 'GET', service : 'MatrixCoreAPI'
    }
    return CALL_API(input);
}
export const FetchTabularData = (MenuId) =>
{
    let input = 
    {
        url :  `coremrs/api/LeadDetails/FetchTabularData/${User.UserId}/${MenuId}`,
        method: 'GET', service : 'MatrixCoreAPI'
    }
    return CALL_API(input);
}
export const BulkUploadSalesCreditChange = (data) =>
    {
        let input = 
        {
            url : 'api/SalesView/BulkUploadSalesCreditChange',
            method: 'POST', service : 'commonUploadFileToUrl',
            requestData : data
        }
        return CALL_API(input);
    }
export const IsUserEligibleService = (BookingId,ReferenceId,ProductSelect,SelectedAgent,AgentType) =>
    {
        let input = 
        {
            url :  `api/SalesView/IsUserEligibleForCredit/${BookingId}/${ReferenceId}/${ProductSelect}/${SelectedAgent}/${AgentType}`,
            method: 'GET', service : 'MatrixCoreAPI'
        }
        return CALL_API(input);
    }
export const SetCreditChangeRequest = (reqData) => {
    const _input = {
        url : 'api/SalesView/SetUpdateCreditChangeRequest',
        method: 'POST', service: 'MatrixCoreAPI',
        requestData: reqData,
        timeout: 3000
    }
    return CALL_API(_input)
}
export const CreateCreditChangeRequest = (reqData) => {
    const _input = {
        url : 'api/SalesView/CreateCreditChangeRequest',
        method: 'POST', service: 'MatrixCoreAPI',
        requestData: reqData,
        timeout: 3000
    }
    return CALL_API(_input)
}
export const GetCreditChangeRequests = (ProductId) =>
{
    let input = 
    {
        url :  `api/SalesView/GetCreditChangeRequests/${ProductId}`,
        method: 'GET', service : 'MatrixCoreAPI'
    }
    return CALL_API(input);
}
export const GetCreditChangeActivityLogs = (BookingID, AgentTypeID, RequestID) =>
{
    let input = 
    {
        url : 'api/SalesView/GetCreditChangeLogs?BookingID=' + BookingID + '&AgentTypeID=' + AgentTypeID + '&RequestID=' + RequestID,
        method: 'GET', service : 'MatrixCoreAPI'
    }
    return CALL_API(input);
}
export const GetCreditChangeBookingHistory = (BookingId) =>
    {
        let input = 
        {
            url : `api/SalesView/GetCreditChangeBookingHistory/${BookingId}`,
            method: 'GET', service : 'MatrixCoreAPI'
        }
        return CALL_API(input);
    }

export const LeadPreviouslyAssignToUser = (leadId) => {
    const input = {
        url: `api/SalesView/CheckLeadPrevAssignToUser?leadId=${leadId}`,
        method: 'GET', service: 'MatrixCoreAPI',
    };
    return CALL_API(input);
}
export const GetCallBacksService = (leadId) => {
    const input = {
        url: `coremrs/api/CallBackSchedular/GetCallBacks`,
        method: 'POST', 
        service: 'MatrixCoreAPI',
        timeout: 2000,
        requestData: { 
          "AgentId": User.RoleId === 13 ? User.UserId : 0, 
          "Id": leadId, 
          "IsCore": false,
          "IsCBWidget": true
        }
      };
    return CALL_API(input);
}

export const FetchRolloverLeadsforUser = (fromdate,todate) => {
    console.log(fromdate)
    console.log(todate)
    const input = {
        url: 'coremrs/api/ProposalService/FetchRolloverLeadsforUser?FromDate='+ fromdate+'&ToDate='+todate,
        method: 'GET', service: 'MatrixCoreAPI',
    };
    return CALL_API(input);
}

