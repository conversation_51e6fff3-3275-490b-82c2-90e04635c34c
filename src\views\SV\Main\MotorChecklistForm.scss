/* Motor Checklist Form Styles */
.motor-checklist-section {
    margin-top: 16px;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    background: #ffffff;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.motor-checklist-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 16px;
    background: #f8f9fa;
    border-bottom: 1px solid #e0e0e0;

    .motor-checklist-icons {
        display: flex;
        gap: 8px;

        .checklist-icon {
            font-size: 20px;
            opacity: 0.7;
        }
    }

    .motor-checklist-buttons {
        display: flex;
        gap: 8px;

        .motor-checklist-btn {
            padding: 6px 12px !important;
            font-size: 12px !important;
            min-width: auto !important;
            border-radius: 4px !important;
            text-transform: none !important;
            font-weight: 500 !important;

            &.continue-journey-btn {
                background: #ffffff !important;
                color: #2196F3 !important;
                border: 1px solid #2196F3 !important;

                &:hover {
                    background: #f0f7ff !important;
                }
            }

            &.view-details-btn {
                background: #2196F3 !important;
                color: #ffffff !important;
                border: 1px solid #2196F3 !important;

                &:hover {
                    background: #1976D2 !important;
                }
            }
        }
    }
}

.motor-checklist-form {
    padding: 12px 16px;
    background: #ffffff;

    .checklist-form-header {
        display: flex;
        align-items: center;
        gap: 8px;
        cursor: pointer;
        transition: all 0.2s ease;

        &:hover {
            opacity: 0.8;
        }

        .form-icon {
            font-size: 16px;
            color: #2196F3;
        }

        .form-title {
            color: #424242;
            font-size: 14px;
            font-weight: 500;
            flex: 1;
        }

        .form-arrow {
            color: #666;
            font-size: 12px;
            transition: transform 0.2s ease;
        }

        &:hover .form-arrow {
            transform: translateX(2px);
        }
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .motor-checklist-header {
        flex-direction: column;
        gap: 12px;
        align-items: stretch;

        .motor-checklist-icons {
            justify-content: center;
        }

        .motor-checklist-buttons {
            justify-content: center;
            
            .motor-checklist-btn {
                flex: 1;
                max-width: 120px;
            }
        }
    }

    .motor-checklist-form {
        padding: 10px 12px;

        .checklist-form-header {
            .form-title {
                font-size: 13px;
            }
        }
    }
}

/* Animation for smooth appearance */
.motor-checklist-section {
    animation: slideInUp 0.3s ease-out;
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Integration with existing LeadCard styles */
.customercard .motor-checklist-section {
    margin: 16px 0 0 0;
}

.customercard .ContinueJourney .motor-checklist-section {
    margin-top: 12px;
}

/* Hover effects for better UX */
.motor-checklist-section:hover {
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
    transition: box-shadow 0.2s ease;
}

/* Focus states for accessibility */
.motor-checklist-btn:focus {
    outline: 2px solid #2196F3;
    outline-offset: 2px;
}

.checklist-form-header:focus {
    outline: 2px solid #2196F3;
    outline-offset: 2px;
    border-radius: 4px;
}
