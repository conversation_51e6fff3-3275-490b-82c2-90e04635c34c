<svg xmlns="http://www.w3.org/2000/svg" width="84.264" height="46.678" viewBox="0 0 84.264 46.678"><defs><style>.a{fill:#dde8fd;}.b{fill:#120e59;}.c{fill:#060632;}.d{fill:#b55c5c;}.e{fill:#a83f3f;}.f{fill:#8ca2d9;}.g{fill:#6d89cf;}.h{fill:#0b0b36;}.i{fill:#93b3f4;}.j{fill:#fc9290;}.k{fill:#ea8888;}.l{fill:#fff;}.m{fill:#fa7269;}.n{fill:#e75755;}.o{fill:#0b0754;}.p{fill:#9e332e;}.q{fill:#3662c1;}.r{fill:#ed6966;}.s{fill:#e8615f;}.t{fill:#a8c1f6;}</style></defs><g transform="translate(-952.689 -664.706)"><path class="a" d="M957.856,711.361s-10.353-11.116-1.751-23.15c7.632-10.678,16.531-9.948,23.06-5.615,2.781,1.846,10.8.228,17.621-10.637,9.305-14.817,41.561-5.83,35.68,16.966-2.846,11.032,8.662,11.433,2.8,22.46Z" transform="translate(0)"/><path class="b" d="M1406.56,818.442a5.459,5.459,0,0,1,1.552-.606c.645-.083,2.809-5.037,3.689-5.3a1.557,1.557,0,0,1,1.32.831s-.99,5.415-2.384,7.359-.147,4.866-.147,4.866l-1.434-.281Z" transform="translate(-432.355 -140.818)"/><path class="c" d="M1289.19,784.875s-1.522.2-1.725-.659-1.566-1.266-1.334-2.032c.284-.938-.272-1.687.18-2.367.5-.748,1.339-.548,1.681-.993a1.6,1.6,0,0,1,1.853-.605c.824.32,1.343-.03,1.91.32.627.387.537,1.036,1.041,1.286.462.229.451,1.092.234,1.832-.156.533.5.885-.375,1.625-.363.308-.166.878-1.38,1.208C1290.726,784.638,1289.474,784.6,1289.19,784.875Z" transform="translate(-317.609 -108.032)"/><path class="d" d="M1327.973,882.482a11.852,11.852,0,0,1,.146,2.009l-1.2,1.215,5.328-.183-1.552-1.48a8.442,8.442,0,0,1-.639-2.464C1330.127,880.869,1327.973,882.482,1327.973,882.482Z" transform="translate(-356.49 -206.42)"/><path class="e" d="M1349.713,901.736s.592.911,1.361.746.782-1.07.782-1.07Z" transform="translate(-378.202 -225.485)"/><path class="d" d="M1331.293,837.222a1.937,1.937,0,0,1-1.9-1.384l-.266-1.44a.584.584,0,0,1,.408-.709l2.135-.395a.584.584,0,0,1,.635.516l.251,1.358A1.887,1.887,0,0,1,1331.293,837.222Z" transform="translate(-358.581 -160.59)"/><path class="b" d="M1323.3,948.339a24.6,24.6,0,0,1-3.865,1.128,19.417,19.417,0,0,1-2.454.263c-.8.041-1.386.035-1.386.035s-.411.114-.545-1.991a29.468,29.468,0,0,0-.734-4.454c-.035-.161-.067-.353-.1-.568-.234-1.712-.286-4.862.075-5.1a7.273,7.273,0,0,1,1.55-.619,2.37,2.37,0,0,0,1.487.748,2.5,2.5,0,0,0,1.089-1.2,14.062,14.062,0,0,1,2.056.016s.134.31,1.452,5.874C1322.45,944.68,1323.893,948.236,1323.3,948.339Z" transform="translate(-344.214 -258.962)"/><g transform="translate(971.079 689.707)"><path class="c" d="M1342.664,1608.24a3.373,3.373,0,0,1,.089-.827c.088-.207.413-1.047.413-1.047h1.268a7.761,7.761,0,0,1,.251,1.018c.015.251,0,.857,0,.857Z" transform="translate(-1342.566 -1586.725)"/><path class="c" d="M1471.8,1608.24a3.382,3.382,0,0,0-.088-.827c-.089-.207-.413-1.047-.413-1.047h-1.268a7.81,7.81,0,0,0-.251,1.018c-.015.251,0,.857,0,.857Z" transform="translate(-1463.65 -1586.725)"/><path class="f" d="M1340.858,1192.318s.222,6.776-.11,10.642a77.294,77.294,0,0,0,0,9.247,2.509,2.509,0,0,0,2.211,0s1.261-7.469,1.253-9.247c-.008-1.893,1.373-10.347,1.373-10.347Z" transform="translate(-1340.602 -1192.307)"/><path class="f" d="M1414.349,1211.976a2.508,2.508,0,0,1-2.211,0s-1.261-7.469-1.253-9.247c0-.8-.241-2.77-.524-4.778-.388-2.748-.849-5.568-.849-5.568l4.914-.307a97.9,97.9,0,0,0-.073,10.654A77.289,77.289,0,0,1,1414.349,1211.976Z" transform="translate(-1406.244 -1192.075)"/><path class="g" d="M1427.944,1273.478c0-.8-.241-2.77-.524-4.779a9.462,9.462,0,0,1,1.011-2.354C1429.338,1268.889,1427.944,1273.478,1427.944,1273.478Z" transform="translate(-1423.304 -1262.824)"/></g><path class="c" d="M1327.2,824.667a1.573,1.573,0,0,1,1.484-1.349c1.307-.05,1.467.2,1.661.491s.183-1.226.183-1.226l-1.583-.168s-1.717.563-1.853.871-.1.331-.1.331Z" transform="translate(-356.552 -150.232)"/><g transform="translate(970.781 674.194)"><path class="h" d="M1335.179,865.78c-.22.041-.668.073-.816-.294a.353.353,0,0,1,.008-.309.416.416,0,0,1,.3-.2l.676-.125a.3.3,0,0,1,.287.06.45.45,0,0,1,.058.382c-.059.271-.182.415-.387.453A1.209,1.209,0,0,1,1335.179,865.78Zm-.5-.755a.371.371,0,0,0-.265.173.307.307,0,0,0-.006.27.5.5,0,0,0,.506.291,1.361,1.361,0,0,0,.378-.052h0c.186-.034.3-.167.352-.418.012-.056.045-.247-.047-.341a.262.262,0,0,0-.246-.047Z" transform="translate(-1334.332 -864.846)"/></g><g transform="translate(972.438 673.902)"><path class="h" d="M1370.022,859.617a1.212,1.212,0,0,1-.125.017.5.5,0,0,1-.524-.285.449.449,0,0,1-.082-.378.3.3,0,0,1,.246-.159l.675-.125a.416.416,0,0,1,.347.077.351.351,0,0,1,.118.286C1370.672,859.446,1370.242,859.576,1370.022,859.617Zm-.475-.76a.262.262,0,0,0-.213.132c-.052.121.047.287.078.335.141.215.293.3.479.265h0a1.354,1.354,0,0,0,.371-.087.5.5,0,0,0,.369-.453.308.308,0,0,0-.1-.25.371.371,0,0,0-.309-.067Z" transform="translate(-1369.274 -858.679)"/></g><g transform="translate(972.032 674.312)"><path class="h" d="M1360.735,867.5a.027.027,0,0,1-.027-.011.027.027,0,0,1,.007-.038.64.64,0,0,1,.511-.094.027.027,0,0,1-.016.052.6.6,0,0,0-.464.086Z" transform="translate(-1360.703 -867.333)"/></g><path class="a" d="M1359.176,894.231l.543-.1.557-.1s-.157.425-.484.477A.8.8,0,0,1,1359.176,894.231Z" transform="translate(-387.217 -218.45)"/><path class="i" d="M2551.616,1554.294h-4.152c-.223-.189.154-.617.547-.646a2.413,2.413,0,0,1,.634.075c.057-.759.383-4.171,1.613-4.034,1.383.154,0,1.5-.391,2.3a19.04,19.04,0,0,0-.735,1.84c.112.024.229.048.348.07.184-.562.865-2.456,1.732-2.345,1.019.129.609.888-.112,1.338-.53.332-1.18.8-1.489,1.03a4.424,4.424,0,0,0,.813.065A1.75,1.75,0,0,1,2551.616,1554.294Z" transform="translate(-1519.109 -843.025)"/><g transform="translate(961.3 706.649)"><path class="i" d="M1170.323,1554.026s-1.81-4.97-3.081-4.547S1170.323,1554.026,1170.323,1554.026Z" transform="translate(-1165.46 -1549.454)"/><path class="i" d="M1157.123,1598.816c-.154,0-3.042-3-3.235-2.213S1157.123,1598.816,1157.123,1598.816Z" transform="translate(-1152.953 -1594.244)"/><path class="i" d="M1141.583,1628.676a3.44,3.44,0,0,0-2.707-.81c-1.512.285-2.158.6-3.108.381a1.406,1.406,0,0,0-1.435.429Z" transform="translate(-1134.333 -1624.103)"/></g><g transform="translate(1010.693 674.313)"><path class="j" d="M2178.079,1036.681l-1.373-3.4-.47,2.225s1.455,3.651,2.322,3.339C2178.963,1038.7,2178.079,1036.681,2178.079,1036.681Z" transform="translate(-2176.236 -1025.413)"/><path class="c" d="M2253.033,869.843a1.213,1.213,0,0,0-.378,1.694s-.857-.157-1.042-.413a.671.671,0,0,0,.053.16,2.352,2.352,0,0,1-1.046-.507c-.258-.329,1.006-4.268,3.055-3.248a1.649,1.649,0,0,1,1.14,2.427c-.733,2.08-.04,2.263-.04,2.263a1.027,1.027,0,0,1-.312.019s-.122-.088-.129-.17a.49.49,0,0,0,.021.172,2.041,2.041,0,0,1-1.066-.509A2.425,2.425,0,0,1,2253.033,869.843Z" transform="translate(-2247.062 -867.361)"/><path class="j" d="M2272.224,990.346h0C2272.215,990.346,2272.216,990.346,2272.224,990.346Z" transform="translate(-2267.667 -984.516)"/><path class="j" d="M2274.751,941.262c.029.258.063.489.078.6.034.247-2.309.086-2.458.086a6.417,6.417,0,0,0,.523-1.438c.122-.466.218-.97.218-.97l1.655.1.1.006a5.674,5.674,0,0,0-.134.636c0,.027-.008.054-.012.08A4.29,4.29,0,0,0,2274.751,941.262Z" transform="translate(-2267.814 -936.116)"/><path class="c" d="M2278.731,874.8a4.788,4.788,0,0,1-.214-1.5,1.642,1.642,0,0,1,2.3-1.379,2.326,2.326,0,0,1,.84.6c.3.338.676,1-.286,2.285l-.28.446Z" transform="translate(-2273.661 -871.543)"/><path class="k" d="M2289.445,942.214a2.92,2.92,0,0,1-1.649-1.676,3.531,3.531,0,0,1,1.628.7c0,.026-.008.053-.011.08A4.291,4.291,0,0,0,2289.445,942.214Z" transform="translate(-2282.507 -937.069)"/><path class="j" d="M2288.39,889.13s-2.091-.517-1.749-1.814.3-2.238,1.652-1.963,1.478.838,1.468,1.3S2288.943,889.231,2288.39,889.13Z" transform="translate(-2281.371 -884.454)"/><path class="c" d="M2278.947,882.065s-.973,1.371-1.908,1.364a1.783,1.783,0,0,1-1.252-.341,2.563,2.563,0,0,0,1.2-1.249S2278.708,881.227,2278.947,882.065Z" transform="translate(-2271.068 -880.947)"/><path class="c" d="M2338.756,889.506a1.9,1.9,0,0,1,.353.81c.016.358-.117,1.05.087,1.079,0,0,.7-.921.375-1.633C2339.224,889.006,2338.756,889.506,2338.756,889.506Z" transform="translate(-2331.052 -888.302)"/><path class="c" d="M2298.6,884.969a3.636,3.636,0,0,1-2.067,1.7,3.944,3.944,0,0,0,1.735-1.714C2298.645,883.932,2298.6,884.969,2298.6,884.969Z" transform="translate(-2290.834 -883.692)"/><path class="l" d="M2317.415,948.407s.026.2.152.244a1.827,1.827,0,0,0,.494.11,1.718,1.718,0,0,0,.16-.175A2.606,2.606,0,0,0,2317.415,948.407Z" transform="translate(-2310.722 -944.563)"/><path class="j" d="M2222.885,1235.174l-.079.33c-.158.655-.3,1.263-.4,1.771a2.12,2.12,0,0,0,.009,1.361c.649.851,0,7.609,0,7.609l-.739.761s-1.285-6.671-1.633-7.717a41.386,41.386,0,0,1-.593-4.577c-.011-.1-.021-.207-.03-.311-.036-.362-.069-.723-.1-1.076-.183-1.96-.32-3.639-.32-3.639a6.481,6.481,0,0,1,2.6-1.065,1.56,1.56,0,0,1,1.754,1.065C2223.849,1230.99,2223.349,1233.242,2222.885,1235.174Z" transform="translate(-2216.973 -1211.486)"/><path class="k" d="M2241.419,1235.174a7.273,7.273,0,0,1-2.954-4.166,2.464,2.464,0,0,1,1.67-2.387,1.56,1.56,0,0,1,1.754,1.065C2242.383,1230.99,2241.883,1233.242,2241.419,1235.174Z" transform="translate(-2235.507 -1211.486)"/><path class="j" d="M2371.061,1095.243l4.976,1.285v.581s-4.953-.058-5.272-.61S2371.061,1095.243,2371.061,1095.243Z" transform="translate(-2361.455 -1084.44)"/><path class="j" d="M2277.354,1217.481l-.233.96s-6.732-3.477-7.6-4.145a9.957,9.957,0,0,1-1.24-3.373c-.638-2.457-1.2-5.338-1.2-5.338s.944-3.63,3.47-1.408c1.511,1.329,1.276,3.891,1.111,5.883-.11,1.337-.189,2.417.312,2.7a17.213,17.213,0,0,1,4.755,4.79Z" transform="translate(-2262.773 -1187.525)"/><path class="m" d="M2212.825,991.106c-.091,2.226-.424,4.43-1.393,4.288a2.013,2.013,0,0,1-1.56-2.106,4.31,4.31,0,0,1,.124-1.155.687.687,0,0,0,.727-.207c.364-.491.162-4.126.384-4.548s1.665-.693,1.665-.693S2212.916,988.906,2212.825,991.106Z" transform="translate(-2208.271 -981.027)"/><path class="n" d="M2212.947,1049.256c-.091,2.226-.424,4.43-1.393,4.288a2.014,2.014,0,0,1-1.56-2.106,1.328,1.328,0,0,0,1.009-.4,33.366,33.366,0,0,0,1.2-3.313S2212.564,1048.372,2212.947,1049.256Z" transform="translate(-2208.394 -1039.177)"/><path class="m" d="M2240.236,976.059a1.8,1.8,0,0,1-.53,2.058,2,2,0,0,1-1.912.569c-.4-.059-1.529-.695-1.647-1.47-.021-.141-.033-.223-.033-.223a4.44,4.44,0,0,0-.532.61c-.009.044-.053.431-.06.484a1.886,1.886,0,0,0,.038.3,5.375,5.375,0,0,1-4.473-.149s-.577-4.946-.163-5.769a10.715,10.715,0,0,1,1.764-1.557.639.639,0,0,0,.157-.471c-.02-.13.129-.349.254-.309a2.769,2.769,0,0,0,1.408.287c.24-.064.544-.341.777-.278.284.078-.016.762.145.771a4.038,4.038,0,0,1,1.522.444c.035.067.379.98.812,2.188a.007.007,0,0,1,0,0C2238.575,975.813,2239.764,975.388,2240.236,976.059Z" transform="translate(-2228.189 -965.255)"/><path class="n" d="M2300.769,1077.5s-.059-1.887-.212-1.864-2.352,2.14-1.829,2.987S2300.769,1077.5,2300.769,1077.5Z" transform="translate(-2292.845 -1065.758)"/><path class="o" d="M2222.877,1143.851a14.621,14.621,0,0,1-2.1.435,11.882,11.882,0,0,1-5.638-.435,16.875,16.875,0,0,1,.3-4.37c.458-1.5.671-2.214.671-2.214a9.68,9.68,0,0,0,3.694.1,5.981,5.981,0,0,0,.781-.2s1.148,2.438,1.464,3.482S2222.877,1143.851,2222.877,1143.851Z" transform="translate(-2213.272 -1124.377)"/><path class="c" d="M2307.664,1143.851a14.622,14.622,0,0,1-2.1.435c-2.29-1.3-1.173-6.763-1.173-6.763q.1-.083.2-.153a5.988,5.988,0,0,0,.78-.2s1.148,2.438,1.464,3.482S2307.664,1143.851,2307.664,1143.851Z" transform="translate(-2298.059 -1124.377)"/><g transform="translate(2.739 12.292)"><path class="p" d="M2236.064,1127.779a3.088,3.088,0,0,1-1.959-.5l-.087-.639a5.581,5.581,0,0,0,4.654.056l-.06.484A7.392,7.392,0,0,1,2236.064,1127.779Z" transform="translate(-2234.018 -1126.641)"/></g><path class="j" d="M2483.818,1111.677a6.678,6.678,0,0,1,1.276-.493,2.884,2.884,0,0,1,.973,0,2.706,2.706,0,0,1-.828.493c-.174.03-.508.276-.406.305s.639-.094.646,0-.327.153-.385.225a.693.693,0,0,1-.588.116c-.327-.043-.688-.065-.688-.065Z" transform="translate(-2469.237 -1099.588)"/><path class="o" d="M2301.457,1350.157l-.233.96s-6.732-3.477-7.6-4.145a9.965,9.965,0,0,1-1.24-3.373,4.471,4.471,0,0,0,3.382-.863c-.111,1.337-.189,2.417.312,2.7a17.215,17.215,0,0,1,4.755,4.789Z" transform="translate(-2286.875 -1320.2)"/><path class="p" d="M2472.562,1480.568h-.006a.42.42,0,0,1-.26-.273c-.061-.145-.079-.264-.134-.407l-.227-.392c-.238-.313-.321-.24-.321-.24s-.062.08-.154.185a1.912,1.912,0,0,1-.548.478.478.478,0,0,0-.235.184.462.462,0,0,0-.07.162s.488.085.446.325c-.083.466-.063.55-.055.565l0,0s.833.436,1.1.521a2.077,2.077,0,0,1,.875.621.274.274,0,0,0,.233.073c.125-.02.287-.06.322-.136Z" transform="translate(-2456.649 -1450.239)"/><path class="c" d="M2231.5,1361.223c.649.852,0,7.609,0,7.609l-.739.761s-1.285-6.671-1.633-7.717a41.4,41.4,0,0,1-.593-4.577,8.081,8.081,0,0,0,3.354.792c-.158.655-.3,1.263-.4,1.771A2.12,2.12,0,0,0,2231.5,1361.223Z" transform="translate(-2226.058 -1334.073)"/><path class="p" d="M2244.585,1596.619l-.4.216c-.145.052-.264.066-.411.123a.848.848,0,0,0-.387.283c-.112.457-.272.428-.272.428l-1.6.015c-.075-.038.052-.361.114-.422.151-.149.6-.074.77-.543.093-.264.553-1.083.553-1.083l0,0c.016-.008.1-.025.564.071.239.05.338-.436.338-.436a.451.451,0,0,1,.159.075.477.477,0,0,1,.177.24,1.911,1.911,0,0,0,.462.562c.1.095.18.16.18.16S2244.905,1596.39,2244.585,1596.619Z" transform="translate(-2238.398 -1560.76)"/></g><path class="b" d="M1244.5,957.16a6.133,6.133,0,0,0-1.813,1.457c-.784.954-2.265,7.763-2.155,8.2a4.887,4.887,0,0,0,2.052-.894c.72-.656,2.435-5.046,2.435-5.046Z" transform="translate(-274.189 -278.59)"/><g transform="translate(969.404 675.203)"><rect class="q" width="40.967" height="14.778" transform="translate(0 7.82) rotate(-11.004)"/><rect class="l" width="37.558" height="12.03" transform="translate(1.701 8.836) rotate(-11.004)"/><path class="q" d="M1684.084,1015.989l-4.051.788a.173.173,0,0,1-.2-.136l-.008-.042a.173.173,0,0,1,.136-.2l4.051-.788a.173.173,0,0,1,.2.136l.008.042A.172.172,0,0,1,1684.084,1015.989Z" transform="translate(-1662.063 -1009.468)"/><path class="q" d="M1669.59,1029.4l-5.92,1.151a.173.173,0,0,1-.2-.136l-.008-.042a.173.173,0,0,1,.136-.2l5.92-1.151a.172.172,0,0,1,.2.136l.008.042A.172.172,0,0,1,1669.59,1029.4Z" transform="translate(-1646.476 -1022.241)"/><g transform="translate(4.499 12.364)"><path class="q" d="M1406.319,1243.736l-5.92,1.151a.173.173,0,0,1-.2-.136l-.008-.042a.173.173,0,0,1,.136-.2l5.92-1.151a.173.173,0,0,1,.2.136l.008.042A.173.173,0,0,1,1406.319,1243.736Z" transform="translate(-1400.185 -1238.782)"/><path class="q" d="M1411.116,1255.607l-7.252,1.41a.19.19,0,0,1-.223-.15v-.007a.19.19,0,0,1,.15-.223l7.252-1.41a.191.191,0,0,1,.223.15v.007A.191.191,0,0,1,1411.116,1255.607Z" transform="translate(-1403.473 -1250.09)"/><path class="q" d="M1583.934,1222l-7.252,1.41a.191.191,0,0,1-.223-.15v-.007a.191.191,0,0,1,.15-.223l7.252-1.41a.191.191,0,0,1,.223.15v.007A.191.191,0,0,1,1583.934,1222Z" transform="translate(-1568.098 -1218.078)"/><path class="q" d="M1762.336,1164.742l-13.025,2.533a.194.194,0,0,1-.227-.153h0a.194.194,0,0,1,.153-.227l13.025-2.533a.194.194,0,0,1,.227.153h0A.194.194,0,0,1,1762.336,1164.742Z" transform="translate(-1732.541 -1163.532)"/><path class="q" d="M1555.959,1195.728l-10.74,2.089a.2.2,0,0,1-.227-.153h0a.2.2,0,0,1,.153-.227l10.74-2.088a.194.194,0,0,1,.227.153h0A.194.194,0,0,1,1555.959,1195.728Z" transform="translate(-1538.123 -1193.049)"/><path class="q" d="M1804.841,1147.331l-10.74,2.089a.194.194,0,0,1-.227-.153h0a.2.2,0,0,1,.153-.227l10.74-2.088a.194.194,0,0,1,.227.153h0A.194.194,0,0,1,1804.841,1147.331Z" transform="translate(-1775.207 -1146.947)"/></g><path class="m" d="M2066.707,1119.135a7.935,7.935,0,0,1-1.207.813.987.987,0,0,0,.061-.334c-.008-.144-.123-.207-.237-.266-.028-.015-.055-.031-.082-.046a.146.146,0,0,0,.039-.17.124.124,0,0,0-.231.01.216.216,0,0,0-.016.054.139.139,0,0,1-.044-.035q.105-.2.2-.4a.075.075,0,0,0-.029-.085.233.233,0,0,0-.319.044.431.431,0,0,0-.026.415l.011.027q-.134.241-.29.469l-.03-.029a.67.67,0,0,1-.081-.1,1.4,1.4,0,0,0,.089-.39.074.074,0,0,0-.105-.072.314.314,0,0,0-.2.34.406.406,0,0,0,.045.143,1.3,1.3,0,0,1-.112.2.069.069,0,0,0,.025.1,1.028,1.028,0,0,1-.212.418c-.086.09-.247.158-.351.052a.444.444,0,0,1-.084-.27,4.934,4.934,0,0,1-.031-.669,1.041,1.041,0,0,1,.05-.31c.023-.062.054-.155.083-.049a.581.581,0,0,1-.011.186.074.074,0,0,0,.147.011c.011-.111.061-.343-.065-.413-.1-.057-.2.025-.248.111a1.973,1.973,0,0,0-.078,1.075c.021.269.121.6.465.514.324-.08.442-.489.517-.769a.071.071,0,0,0,0-.047l0-.007.041.041c.02.017.037.035.055.053q-.051.071-.1.14a.075.075,0,0,0,.005.09.173.173,0,0,0,.3-.127.243.243,0,0,0-.025-.1q.154-.22.287-.454a.23.23,0,0,0,.176.071h.015a.561.561,0,0,0,.128.1.844.844,0,0,1,.127.069c.07.057.049.122.034.2a3.25,3.25,0,0,1-.085.317.664.664,0,0,0-.343.363.37.37,0,0,0,.127.415.075.075,0,0,0,.111-.037c.083-.205.159-.414.224-.626,0-.007,0-.014.006-.02.131-.074.268-.14.4-.216a7.557,7.557,0,0,0,.975-.689C2066.876,1119.185,2066.779,1119.075,2066.707,1119.135Zm-1.786-.2c0-.059.029-.167.1-.165-.031.066-.062.132-.1.2C2064.922,1118.961,2064.921,1118.95,2064.921,1118.94Zm.187,1.7c-.062-.116.026-.259.128-.353C2065.2,1120.4,2065.153,1120.521,2065.107,1120.638Z" transform="translate(-2027.396 -1107.609)"/><g transform="translate(8.517 6.32)"><path class="r" d="M1486.849,1130.829l-.633.123-.474-2.44-.71.381-.1-.515,1.229-.73.068-.013Z" transform="translate(-1484.931 -1122.507)"/><path class="r" d="M1564.19,1114.789a2.192,2.192,0,0,1,.042.632,1.19,1.19,0,0,1-.137.467.859.859,0,0,1-.292.31,1.306,1.306,0,0,1-.878.171.867.867,0,0,1-.388-.178,1.185,1.185,0,0,1-.3-.381,2.154,2.154,0,0,1-.2-.6l-.108-.554a2.183,2.183,0,0,1-.042-.632,1.171,1.171,0,0,1,.138-.466.876.876,0,0,1,.292-.309,1.3,1.3,0,0,1,.877-.171.873.873,0,0,1,.388.177,1.181,1.181,0,0,1,.3.38,2.155,2.155,0,0,1,.2.6Zm-.758-.521a1.83,1.83,0,0,0-.1-.351.686.686,0,0,0-.133-.216.353.353,0,0,0-.167-.1.443.443,0,0,0-.2,0,.433.433,0,0,0-.18.076.349.349,0,0,0-.114.155.7.7,0,0,0-.041.25,1.85,1.85,0,0,0,.04.363l.141.727a1.846,1.846,0,0,0,.1.355.739.739,0,0,0,.134.221.34.34,0,0,0,.166.1.478.478,0,0,0,.377-.073.337.337,0,0,0,.114-.157.746.746,0,0,0,.039-.254,1.936,1.936,0,0,0-.041-.367Z" transform="translate(-1558.229 -1108.621)"/><path class="r" d="M1618.478,1104.232a2.183,2.183,0,0,1,.042.632,1.19,1.19,0,0,1-.137.467.857.857,0,0,1-.292.31,1.3,1.3,0,0,1-.878.171.869.869,0,0,1-.388-.178,1.189,1.189,0,0,1-.3-.381,2.162,2.162,0,0,1-.2-.6l-.108-.554a2.176,2.176,0,0,1-.042-.632,1.167,1.167,0,0,1,.138-.466.876.876,0,0,1,.292-.309,1.208,1.208,0,0,1,.423-.163,1.223,1.223,0,0,1,.454-.008.872.872,0,0,1,.388.177,1.179,1.179,0,0,1,.3.38,2.163,2.163,0,0,1,.2.6Zm-.758-.521a1.849,1.849,0,0,0-.1-.351.686.686,0,0,0-.133-.216.352.352,0,0,0-.166-.1.444.444,0,0,0-.2,0,.437.437,0,0,0-.18.076.349.349,0,0,0-.114.155.7.7,0,0,0-.041.25,1.849,1.849,0,0,0,.04.363l.142.727a1.809,1.809,0,0,0,.1.356.745.745,0,0,0,.134.221.341.341,0,0,0,.166.1.478.478,0,0,0,.377-.073.334.334,0,0,0,.114-.157.741.741,0,0,0,.039-.254,1.918,1.918,0,0,0-.041-.367Z" transform="translate(-1609.944 -1098.565)"/><path class="r" d="M1672.766,1093.676a2.2,2.2,0,0,1,.042.632,1.2,1.2,0,0,1-.137.466.858.858,0,0,1-.292.31,1.3,1.3,0,0,1-.878.171.869.869,0,0,1-.388-.178,1.193,1.193,0,0,1-.3-.381,2.149,2.149,0,0,1-.2-.6l-.108-.554a2.184,2.184,0,0,1-.042-.632,1.17,1.17,0,0,1,.138-.466.879.879,0,0,1,.292-.309,1.3,1.3,0,0,1,.877-.171.872.872,0,0,1,.388.177,1.184,1.184,0,0,1,.3.38,2.158,2.158,0,0,1,.2.6Zm-.758-.521a1.828,1.828,0,0,0-.1-.351.68.68,0,0,0-.133-.216.354.354,0,0,0-.166-.1.444.444,0,0,0-.2,0,.437.437,0,0,0-.18.076.349.349,0,0,0-.114.156.694.694,0,0,0-.041.25,1.866,1.866,0,0,0,.04.363l.142.727a1.847,1.847,0,0,0,.1.355.749.749,0,0,0,.134.221.342.342,0,0,0,.165.1.448.448,0,0,0,.2,0,.441.441,0,0,0,.181-.076.338.338,0,0,0,.114-.157.747.747,0,0,0,.039-.254,1.9,1.9,0,0,0-.041-.367Z" transform="translate(-1661.658 -1088.508)"/><path class="r" d="M1750.617,1078.537a2.174,2.174,0,0,1,.042.632,1.186,1.186,0,0,1-.136.467.859.859,0,0,1-.292.31,1.3,1.3,0,0,1-.878.171.869.869,0,0,1-.388-.178,1.2,1.2,0,0,1-.3-.381,2.147,2.147,0,0,1-.2-.6l-.108-.554a2.183,2.183,0,0,1-.042-.632,1.172,1.172,0,0,1,.138-.466.878.878,0,0,1,.292-.309,1.3,1.3,0,0,1,.877-.171.874.874,0,0,1,.388.177,1.176,1.176,0,0,1,.3.38,2.154,2.154,0,0,1,.2.6Zm-.758-.521a1.873,1.873,0,0,0-.1-.351.683.683,0,0,0-.133-.216.352.352,0,0,0-.166-.1.444.444,0,0,0-.2,0,.437.437,0,0,0-.18.076.347.347,0,0,0-.114.155.7.7,0,0,0-.041.25,1.849,1.849,0,0,0,.04.363l.141.727a1.827,1.827,0,0,0,.1.355.738.738,0,0,0,.134.221.341.341,0,0,0,.166.1.478.478,0,0,0,.377-.073.334.334,0,0,0,.114-.157.742.742,0,0,0,.039-.254,1.9,1.9,0,0,0-.041-.367Z" transform="translate(-1735.818 -1074.088)"/><path class="r" d="M1804.9,1067.98a2.183,2.183,0,0,1,.042.632,1.19,1.19,0,0,1-.137.467.857.857,0,0,1-.292.31,1.3,1.3,0,0,1-.878.171.864.864,0,0,1-.388-.178,1.189,1.189,0,0,1-.3-.381,2.16,2.16,0,0,1-.2-.6l-.108-.554a2.183,2.183,0,0,1-.042-.632,1.163,1.163,0,0,1,.138-.466.874.874,0,0,1,.292-.309,1.2,1.2,0,0,1,.423-.163,1.218,1.218,0,0,1,.454-.008.871.871,0,0,1,.388.177,1.18,1.18,0,0,1,.3.38,2.165,2.165,0,0,1,.2.6Zm-.758-.521a1.863,1.863,0,0,0-.1-.351.681.681,0,0,0-.133-.216.35.35,0,0,0-.166-.1.444.444,0,0,0-.2,0,.439.439,0,0,0-.18.076.353.353,0,0,0-.114.155.7.7,0,0,0-.041.25,1.869,1.869,0,0,0,.04.363l.142.727a1.846,1.846,0,0,0,.1.356.74.74,0,0,0,.134.221.342.342,0,0,0,.166.1.481.481,0,0,0,.376-.073.336.336,0,0,0,.114-.158.74.74,0,0,0,.039-.254,1.9,1.9,0,0,0-.041-.367Z" transform="translate(-1787.533 -1064.032)"/><path class="r" d="M1859.193,1057.424a2.19,2.19,0,0,1,.042.632,1.184,1.184,0,0,1-.136.466.861.861,0,0,1-.292.31,1.3,1.3,0,0,1-.878.171.863.863,0,0,1-.388-.178,1.194,1.194,0,0,1-.3-.381,2.16,2.16,0,0,1-.2-.6l-.108-.554a2.185,2.185,0,0,1-.042-.632,1.165,1.165,0,0,1,.138-.465.875.875,0,0,1,.292-.309,1.3,1.3,0,0,1,.877-.171.871.871,0,0,1,.388.177,1.179,1.179,0,0,1,.3.38,2.155,2.155,0,0,1,.2.6Zm-.758-.521a1.845,1.845,0,0,0-.1-.351.684.684,0,0,0-.133-.216.355.355,0,0,0-.166-.1.443.443,0,0,0-.2,0,.438.438,0,0,0-.18.076.351.351,0,0,0-.114.156.7.7,0,0,0-.041.25,1.829,1.829,0,0,0,.04.363l.141.727a1.847,1.847,0,0,0,.1.355.736.736,0,0,0,.134.22.34.34,0,0,0,.166.1.447.447,0,0,0,.2,0,.442.442,0,0,0,.181-.076.338.338,0,0,0,.114-.157.747.747,0,0,0,.039-.254,1.921,1.921,0,0,0-.041-.367Z" transform="translate(-1839.247 -1053.975)"/><path class="r" d="M1937.044,1042.285a2.183,2.183,0,0,1,.042.632,1.189,1.189,0,0,1-.137.467.857.857,0,0,1-.292.31,1.3,1.3,0,0,1-.878.171.866.866,0,0,1-.388-.178,1.194,1.194,0,0,1-.3-.381,2.152,2.152,0,0,1-.2-.6l-.108-.554a2.19,2.19,0,0,1-.042-.632,1.163,1.163,0,0,1,.138-.466.874.874,0,0,1,.292-.309,1.213,1.213,0,0,1,.423-.163,1.226,1.226,0,0,1,.454-.008.874.874,0,0,1,.388.177,1.182,1.182,0,0,1,.3.38,2.162,2.162,0,0,1,.2.6Zm-.758-.521a1.851,1.851,0,0,0-.1-.352.69.69,0,0,0-.133-.216.353.353,0,0,0-.166-.1.475.475,0,0,0-.376.073.35.35,0,0,0-.114.155.7.7,0,0,0-.041.25,1.833,1.833,0,0,0,.04.363l.141.727a1.858,1.858,0,0,0,.1.355.738.738,0,0,0,.134.221.341.341,0,0,0,.166.1.439.439,0,0,0,.2,0,.445.445,0,0,0,.181-.076.334.334,0,0,0,.114-.157.736.736,0,0,0,.039-.254,1.878,1.878,0,0,0-.041-.367Z" transform="translate(-1913.407 -1039.555)"/><path class="r" d="M1991.332,1031.728a2.174,2.174,0,0,1,.042.632,1.184,1.184,0,0,1-.137.467.858.858,0,0,1-.292.31,1.3,1.3,0,0,1-.878.171.863.863,0,0,1-.388-.178,1.194,1.194,0,0,1-.3-.381,2.163,2.163,0,0,1-.2-.6l-.108-.554a2.194,2.194,0,0,1-.042-.632,1.167,1.167,0,0,1,.138-.466.875.875,0,0,1,.292-.309,1.3,1.3,0,0,1,.877-.171.874.874,0,0,1,.388.177,1.185,1.185,0,0,1,.3.38,2.162,2.162,0,0,1,.2.6Zm-.758-.521a1.842,1.842,0,0,0-.1-.351.691.691,0,0,0-.133-.216.354.354,0,0,0-.166-.1.443.443,0,0,0-.2,0,.438.438,0,0,0-.18.076.349.349,0,0,0-.114.155.693.693,0,0,0-.041.25,1.831,1.831,0,0,0,.04.363l.141.727a1.841,1.841,0,0,0,.1.355.743.743,0,0,0,.134.221.342.342,0,0,0,.166.1.479.479,0,0,0,.377-.073.338.338,0,0,0,.114-.158.728.728,0,0,0,.039-.254,1.876,1.876,0,0,0-.041-.367Z" transform="translate(-1965.122 -1029.498)"/><path class="r" d="M2045.62,1021.172a2.182,2.182,0,0,1,.042.632,1.182,1.182,0,0,1-.137.467.856.856,0,0,1-.292.31,1.3,1.3,0,0,1-.878.171.869.869,0,0,1-.388-.178,1.191,1.191,0,0,1-.3-.381,2.156,2.156,0,0,1-.2-.6l-.108-.554a2.183,2.183,0,0,1-.042-.632,1.166,1.166,0,0,1,.138-.466.874.874,0,0,1,.292-.309,1.3,1.3,0,0,1,.877-.171.872.872,0,0,1,.388.177,1.18,1.18,0,0,1,.3.38,2.152,2.152,0,0,1,.2.6Zm-.758-.521a1.844,1.844,0,0,0-.1-.351.684.684,0,0,0-.133-.216.353.353,0,0,0-.166-.1.441.441,0,0,0-.2,0,.432.432,0,0,0-.18.076.348.348,0,0,0-.114.155.7.7,0,0,0-.041.25,1.831,1.831,0,0,0,.04.363l.141.727a1.846,1.846,0,0,0,.1.355.737.737,0,0,0,.134.221.34.34,0,0,0,.166.1.478.478,0,0,0,.377-.073A.337.337,0,0,0,2045,1022a.742.742,0,0,0,.039-.254,1.919,1.919,0,0,0-.041-.367Z" transform="translate(-2016.836 -1019.442)"/></g><g transform="translate(36.312 2.816)"><g transform="translate(0.49 1.748)"><path class="j" d="M2083.059,982.456l.02.1a.04.04,0,0,1,0,.023.037.037,0,0,1-.013.018l-.006,0-.006,0-1.411.271a.037.037,0,0,1-.041-.03v0l-.02-.1v0a.037.037,0,0,1,.027-.044l1.411-.271h.012a.036.036,0,0,1,.019.012A.041.041,0,0,1,2083.059,982.456Z" transform="translate(-2081.579 -982.423)"/><path class="n" d="M2082.392,985.64l-.756.145a.036.036,0,0,1-.038-.031v0l-.02-.1v0a.036.036,0,0,1,.023-.043l.663-.127A.338.338,0,0,0,2082.392,985.64Z" transform="translate(-2081.577 -985.334)"/><g transform="translate(0.512)"><rect class="m" width="0.016" height="0.182" transform="translate(0.773 0.03) rotate(-10.841)"/><rect class="m" width="0.016" height="0.182" transform="translate(0.719 0.04) rotate(-10.892)"/><rect class="m" width="0.016" height="0.182" transform="translate(0.654 0.053) rotate(-10.841)"/><rect class="m" width="0.016" height="0.182" transform="translate(0.554 0.072) rotate(-10.844)"/><rect class="m" width="0.016" height="0.182" transform="translate(0.449 0.092) rotate(-10.892)"/><rect class="m" width="0.016" height="0.182" transform="translate(0.287 0.123) rotate(-10.872)"/><rect class="m" width="0.016" height="0.182" transform="translate(0.135 0.153) rotate(-10.844)"/><rect class="m" width="0.016" height="0.182" transform="translate(0 0.179) rotate(-10.889)"/><rect class="m" width="0.016" height="0.182" transform="translate(0.825 0.02) rotate(-10.889)"/><rect class="m" width="0.016" height="0.182" transform="matrix(0.982, -0.188, 0.188, 0.982, 0.859, 0.014)"/><rect class="m" width="0.016" height="0.182" transform="translate(0.892 0.007) rotate(-10.889)"/><path class="m" d="M2111.715,982.425l.034.179.01,0,.006,0-.034-.178h-.006Z" transform="translate(-2110.798 -982.423)"/><path class="m" d="M2112.2,982.44l.034.174a.04.04,0,0,0,.013-.018l-.028-.145A.037.037,0,0,0,2112.2,982.44Z" transform="translate(-2111.256 -982.439)"/></g></g><g transform="translate(0.305 1.598)"><path class="j" d="M2079.148,979.287l.02.1a.04.04,0,0,1,0,.023.039.039,0,0,1-.013.018l-.006,0-.005,0-1.412.271a.037.037,0,0,1-.041-.03v0l-.02-.1a0,0,0,0,0,0,0,.038.038,0,0,1,.027-.044l1.411-.271h.025l0,0h0l0,0A.043.043,0,0,1,2079.148,979.287Z" transform="translate(-2077.668 -979.254)"/><path class="n" d="M2078.48,982.471l-.756.145a.035.035,0,0,1-.038-.031v0l-.02-.1a0,0,0,0,0,0,0,.036.036,0,0,1,.023-.043l.664-.127a.007.007,0,0,0,0,0A.337.337,0,0,0,2078.48,982.471Z" transform="translate(-2077.666 -982.165)"/><g transform="translate(0.512 0)"><rect class="m" width="0.016" height="0.182" transform="matrix(0.982, -0.188, 0.188, 0.982, 0.773, 0.03)"/><rect class="m" width="0.016" height="0.182" transform="translate(0.719 0.04) rotate(-10.872)"/><rect class="m" width="0.016" height="0.182" transform="translate(0.654 0.053) rotate(-10.889)"/><rect class="m" width="0.016" height="0.182" transform="matrix(0.982, -0.188, 0.188, 0.982, 0.554, 0.072)"/><path class="m" d="M2097.959,981.32l.016,0-.034-.179-.016,0Z" transform="translate(-2097.476 -981.049)"/><rect class="m" width="0.016" height="0.182" transform="matrix(0.982, -0.189, 0.189, 0.982, 0.287, 0.123)"/><rect class="m" width="0.016" height="0.182" transform="translate(0.135 0.152) rotate(-10.889)"/><rect class="m" width="0.016" height="0.182" transform="translate(0 0.178) rotate(-10.841)"/><rect class="m" width="0.016" height="0.182" transform="matrix(0.982, -0.189, 0.189, 0.982, 0.825, 0.02)"/><rect class="m" width="0.016" height="0.182" transform="translate(0.859 0.014) rotate(-10.872)"/><rect class="m" width="0.016" height="0.182" transform="translate(0.892 0.007) rotate(-10.841)"/><path class="m" d="M2107.8,979.257l.035.179.01,0,.006,0-.034-.178h-.006Z" transform="translate(-2106.887 -979.255)"/><path class="m" d="M2108.285,979.271l.033.174a.038.038,0,0,0,.013-.018l-.028-.145A.038.038,0,0,0,2108.285,979.271Z" transform="translate(-2107.346 -979.27)"/></g></g><path class="j" d="M2079.977,975.306l.02.1a.045.045,0,0,1,0,.023.042.042,0,0,1-.013.018l-.006,0-.006,0-1.411.271a.029.029,0,0,1-.016,0,.04.04,0,0,1-.024-.027,0,0,0,0,1,0,0v0l-.02-.1a0,0,0,0,1,0,0,.037.037,0,0,1,.027-.044l1.412-.271h.013a.036.036,0,0,1,.019.012A.047.047,0,0,1,2079.977,975.306Z" transform="translate(-2078.153 -973.864)"/><path class="n" d="M2079.31,978.49l-.756.145a.024.024,0,0,1-.015,0,.042.042,0,0,1-.023-.028v0l-.02-.1a0,0,0,0,0,0,0,.036.036,0,0,1,.023-.043l.663-.127a.341.341,0,0,0,.122.158Z" transform="translate(-2078.151 -976.775)"/><path class="j" d="M2079.962,971.389l.019.1a.04.04,0,0,1,0,.023.035.035,0,0,1-.013.018l-.006,0-.006,0-1.411.271a.037.037,0,0,1-.041-.03v0l-.02-.1a0,0,0,0,0,0,0,.038.038,0,0,1,.023-.042h0l1.411-.271h.013a.038.038,0,0,1,.019.012A.045.045,0,0,1,2079.962,971.389Z" transform="translate(-2078.139 -970.132)"/><path class="n" d="M2079.295,974.573l-.756.145a.036.036,0,0,1-.038-.031v0l-.02-.1a0,0,0,0,0,0,0,.037.037,0,0,1,.02-.042h0l.663-.127h0A.338.338,0,0,0,2079.295,974.573Z" transform="translate(-2078.137 -973.043)"/><path class="j" d="M2077.046,968.036l.02.1a.04.04,0,0,1,0,.023.039.039,0,0,1-.013.018l-.006,0h-.006l-1.411.271a.038.038,0,0,1-.042-.031v0l-.02-.1v0a.038.038,0,0,1,.019-.039h0l.007,0L2077,968h.013a.037.037,0,0,1,.019.012A.038.038,0,0,1,2077.046,968.036Z" transform="translate(-2075.361 -966.938)"/><path class="n" d="M2076.379,971.22l-.756.145a.036.036,0,0,1-.038-.031,0,0,0,0,0,0,0l-.02-.1v0a.038.038,0,0,1,.017-.039h0l.005,0,.663-.127h0a.339.339,0,0,0,.126.16Z" transform="translate(-2075.359 -969.849)"/><path class="j" d="M2076.262,964.295l.02.1a.04.04,0,0,1,0,.023.039.039,0,0,1-.013.018l0,0h0l-.006,0-1.411.271h-.006a.038.038,0,0,1-.035-.031v0l-.02-.1a0,0,0,0,1,0,0,.037.037,0,0,1,.027-.043l1.412-.271h.025l0,0h0v0A.047.047,0,0,1,2076.262,964.295Z" transform="translate(-2074.614 -963.375)"/><path class="n" d="M2075.594,967.479l-.756.145h0a.037.037,0,0,1-.034-.031v0l-.019-.1a0,0,0,0,0,0,0,.036.036,0,0,1,.023-.043l.663-.127,0,0a.333.333,0,0,0,.124.156Z" transform="translate(-2074.611 -966.286)"/><path class="j" d="M2077.089,960.313l.02.1a.04.04,0,0,1,0,.023.036.036,0,0,1-.013.018l-.006,0-.005,0-1.412.271a.032.032,0,0,1-.017,0,.039.039,0,0,1-.024-.027v0a0,0,0,0,1,0,0l-.02-.1a0,0,0,0,1,0,0,.037.037,0,0,1,.027-.044l1.412-.271h.013a.036.036,0,0,1,.019.011A.043.043,0,0,1,2077.089,960.313Z" transform="translate(-2075.402 -959.581)"/><path class="j" d="M2078.768,956.055l.02.1a.044.044,0,0,1,0,.023.035.035,0,0,1-.013.018l-.006,0-.005,0-1.412.271a.032.032,0,0,1-.017,0,.041.041,0,0,1-.024-.027,0,0,0,0,1,0,0v0l-.02-.1v0a.038.038,0,0,1,.027-.044l1.412-.271h.013a.035.035,0,0,1,.019.012A.042.042,0,0,1,2078.768,956.055Z" transform="translate(-2077 -955.525)"/><path class="n" d="M2076.422,963.5l-.756.145a.026.026,0,0,1-.015,0,.041.041,0,0,1-.022-.028v0a.006.006,0,0,1,0,0l-.02-.1a0,0,0,0,1,0,0,.036.036,0,0,1,.023-.043l.663-.127a.345.345,0,0,0,.122.158Z" transform="translate(-2075.4 -962.493)"/><path class="n" d="M2078.126,959.25l-.756.145a.036.036,0,0,1-.038-.031,0,0,0,0,1,0,0l-.02-.1a0,0,0,0,1,0,0,.037.037,0,0,1,.02-.042h0l.663-.127h0A.338.338,0,0,0,2078.126,959.25Z" transform="translate(-2077.024 -958.447)"/><path class="j" d="M2074.16,953.043l.02.1a.04.04,0,0,1,0,.023.035.035,0,0,1-.013.018l-.006,0h-.006l-1.411.271a.037.037,0,0,1-.041-.03,0,0,0,0,1,0,0l-.02-.1v0a.038.038,0,0,1,.027-.044l1.411-.271h.022l0,0h0l0,0A.042.042,0,0,1,2074.16,953.043Z" transform="translate(-2072.611 -952.656)"/><path class="n" d="M2073.492,956.227l-.756.145a.036.036,0,0,1-.037-.031.006.006,0,0,1,0,0l-.02-.1v0a.035.035,0,0,1,.023-.043l.663-.127,0,0a.34.34,0,0,0,.125.157Z" transform="translate(-2072.61 -955.567)"/><path class="j" d="M2075.637,948.931l.019.1a.038.038,0,0,1,0,.023.034.034,0,0,1-.013.018l-.006,0-.006,0-1.411.271a.029.029,0,0,1-.016,0,.041.041,0,0,1-.025-.028v0l-.02-.1v0a.038.038,0,0,1,.023-.042h0l1.411-.271h.013a.038.038,0,0,1,.019.011A.045.045,0,0,1,2075.637,948.931Z" transform="translate(-2074.019 -948.739)"/><path class="n" d="M2074.969,952.115l-.756.145a.027.027,0,0,1-.015,0,.042.042,0,0,1-.023-.028v0l-.02-.1v0a.037.037,0,0,1,.02-.042h0l.663-.127h0a.339.339,0,0,0,.122.158Z" transform="translate(-2074.017 -951.649)"/><path class="j" d="M2072.722,945.577l.02.1a.04.04,0,0,1,0,.023.038.038,0,0,1-.013.018l-.006,0h-.006l-1.411.271a.038.038,0,0,1-.042-.031v0l-.02-.1v0a.037.037,0,0,1,.027-.044l1.411-.271h.013a.037.037,0,0,1,.019.012A.038.038,0,0,1,2072.722,945.577Z" transform="translate(-2071.242 -945.544)"/><path class="n" d="M2072.054,948.761l-.756.145a.035.035,0,0,1-.038-.031v0l-.02-.1a0,0,0,0,0,0,0,.036.036,0,0,1,.023-.043l.664-.127a.332.332,0,0,0,.127.161Z" transform="translate(-2071.24 -948.455)"/><g transform="translate(0.512)"><rect class="m" width="0.016" height="0.182" transform="translate(1.117 1.439) rotate(-10.844)"/><rect class="m" width="0.016" height="0.182" transform="translate(1.063 1.45) rotate(-10.939)"/><rect class="m" width="0.016" height="0.182" transform="matrix(0.982, -0.189, 0.189, 0.982, 0.998, 1.462)"/><rect class="m" width="0.016" height="0.182" transform="translate(0.898 1.481) rotate(-10.841)"/><rect class="m" width="0.016" height="0.182" transform="matrix(0.982, -0.188, 0.188, 0.982, 0.792, 1.502)"/><rect class="m" width="0.016" height="0.182" transform="translate(0.631 1.533) rotate(-10.889)"/><rect class="m" width="0.016" height="0.182" transform="translate(0.479 1.562) rotate(-10.841)"/><rect class="m" width="0.016" height="0.182" transform="translate(0.344 1.588) rotate(-10.889)"/><rect class="m" width="0.016" height="0.182" transform="translate(1.169 1.429) rotate(-10.889)"/><rect class="m" width="0.016" height="0.182" transform="matrix(0.982, -0.188, 0.188, 0.982, 1.203, 1.423)"/><rect class="m" width="0.016" height="0.182" transform="translate(1.236 1.417) rotate(-10.889)"/><path class="m" d="M2108.633,975.276l.034.179.01,0,.006,0-.035-.178h-.006Z" transform="translate(-2107.373 -973.864)"/><path class="m" d="M2109.113,975.29l.034.174a.039.039,0,0,0,.013-.018l-.028-.145A.035.035,0,0,0,2109.113,975.29Z" transform="translate(-2107.83 -973.88)"/><rect class="m" width="0.016" height="0.182" transform="translate(0.63 1.347) rotate(-10.872)"/><rect class="m" width="0.016" height="0.182" transform="translate(0.479 1.376) rotate(-10.872)"/><rect class="m" width="0.016" height="0.182" transform="translate(0.343 1.402) rotate(-10.889)"/><path class="m" d="M2104.678,961.213l-.034-.179-.014,0h0l-.035-.178h0l-.033-.174h.006l.033.172a.036.036,0,0,0,.013-.018l-.028-.145a.038.038,0,0,0-.015-.01h.006l-.034-.179-.016,0,.034.175h-.005l-.01,0,.035.179h0l.034.178.01,0h0l.035.179Z" transform="translate(-2103.46 -959.793)"/><rect class="m" width="0.016" height="0.182" transform="translate(1.202 1.237) rotate(-10.892)"/><rect class="m" width="0.016" height="0.182" transform="translate(1.235 1.231) rotate(-10.889)"/><path class="m" d="M2108.617,971.358l.035.179.01,0,.005,0-.034-.178h-.006Z" transform="translate(-2107.357 -970.132)"/><path class="m" d="M2109.1,971.373l.033.174a.036.036,0,0,0,.013-.018l-.028-.145A.039.039,0,0,0,2109.1,971.373Z" transform="translate(-2107.816 -970.148)"/><path class="m" d="M2101.975,965.188l-.034-.179h0l-.034-.178-.016,0,.035.179h0l.034.178-.016,0,.035.179.016,0-.035-.179Z" transform="translate(-2100.947 -963.917)"/><path class="m" d="M2099.422,965.492h0l-.034-.178-.016,0,.035.179h0l.034.178h0l.035.179.016,0-.035-.179h0Z" transform="translate(-2098.548 -964.377)"/><path class="m" d="M2106.183,968.02l.033.174a.04.04,0,0,0,.013-.018l-.028-.145A.037.037,0,0,0,2106.183,968.02Z" transform="translate(-2105.039 -966.955)"/><path class="m" d="M2097.281,965.718l-.016,0,.035.179h0l.035.178h0l.035.179.016,0-.035-.179h0l-.035-.179h0Z" transform="translate(-2096.543 -964.762)"/><path class="m" d="M2095.055,966.146l-.016,0,.035.179h0l.034.178.016,0-.035-.179h0Z" transform="translate(-2094.423 -965.169)"/><path class="m" d="M2091.642,966.8l-.016,0,.035.179h0l.034.178.016,0-.035-.179h0Z" transform="translate(-2091.171 -965.793)"/><path class="m" d="M2088.447,967.415l-.016,0,.035.179h0l.034.178.016,0-.035-.179h0Z" transform="translate(-2088.128 -966.378)"/><path class="m" d="M2085.593,967.962l-.016,0,.035.179h0l.034.178.017,0-.035-.179h0Z" transform="translate(-2085.409 -966.9)"/><path class="m" d="M2102.995,964.622l-.016,0,.035.179h0l.034.178h0l.035.179.016,0-.034-.179h0l-.035-.179h0Z" transform="translate(-2101.986 -963.717)"/><path class="m" d="M2102.779,961.021l-.013,0-.034-.175-.016,0,.035.179.013,0,.034.175h0l.034.178.016,0-.035-.179h0Z" transform="translate(-2101.737 -960.123)"/><rect class="m" width="0.016" height="0.182" transform="translate(0.926 0.739) rotate(-10.776)"/><path class="m" d="M2100.232,961.51h-.008l.034.179h0l.034.178.016,0-.035-.179h0l-.033-.174h.008l-.035-.179-.016,0Z" transform="translate(-2099.337 -960.584)"/><rect class="m" width="0.016" height="0.182" transform="translate(0.761 0.771) rotate(-10.889)"/><rect class="m" width="0.016" height="0.182" transform="translate(0.656 0.791) rotate(-10.86)"/><rect class="m" width="0.016" height="0.182" transform="translate(0.494 0.822) rotate(-10.807)"/><rect class="m" width="0.016" height="0.182" transform="translate(0.342 0.851) rotate(-10.841)"/><rect class="m" width="0.016" height="0.182" transform="translate(0.207 0.877) rotate(-10.841)"/><path class="m" d="M2103.84,960.817h-.006l.035.179h0l.034.178-.015,0,.034.179.016,0-.034-.179.015,0-.035-.179h0l-.033-.173h.006l-.034-.179-.016,0Z" transform="translate(-2102.775 -959.924)"/><rect class="m" width="0.016" height="0.182" transform="translate(1.099 0.706) rotate(-10.872)"/><path class="m" d="M2105.8,960.458l-.034-.178h-.006l-.01,0,.034.179.01,0Z" transform="translate(-2104.621 -959.581)"/><path class="m" d="M2106.273,960.453l-.028-.145a.036.036,0,0,0-.019-.011l.033.174A.032.032,0,0,0,2106.273,960.453Z" transform="translate(-2105.081 -959.598)"/><rect class="m" width="0.016" height="0.182" transform="translate(1.061 0.527) rotate(-10.954)"/><rect class="m" width="0.016" height="0.182" transform="matrix(0.982, -0.188, 0.188, 0.982, 0.842, 0.569)"/><rect class="m" width="0.016" height="0.182" transform="matrix(0.982, -0.187, 0.187, 0.982, 0.737, 0.59)"/><rect class="m" width="0.016" height="0.182" transform="translate(0.575 0.621) rotate(-10.872)"/><rect class="m" width="0.016" height="0.182" transform="matrix(0.982, -0.188, 0.188, 0.982, 0.423, 0.65)"/><rect class="m" width="0.016" height="0.182" transform="translate(0.288 0.676) rotate(-10.872)"/><rect class="m" width="0.016" height="0.182" transform="translate(1.113 0.517) rotate(-10.872)"/><rect class="m" width="0.016" height="0.182" transform="translate(1.147 0.511) rotate(-10.872)"/><rect class="m" width="0.016" height="0.182" transform="translate(1.18 0.504) rotate(-10.841)"/><path class="m" d="M2107.45,956.036l.035.179.01,0,.006,0-.034-.178h-.007Z" transform="translate(-2106.245 -955.537)"/><path class="m" d="M2107.932,956.051l.033.174a.036.036,0,0,0,.013-.018l-.028-.145A.036.036,0,0,0,2107.932,956.051Z" transform="translate(-2106.704 -955.553)"/><path class="m" d="M2098.451,946.649l.016,0-.035-.179-.016,0-.033-.175.016,0-.035-.179-.016,0,.035.179-.016,0,.034.179.016,0Z" transform="translate(-2097.576 -946.086)"/><rect class="m" width="0.016" height="0.182" transform="matrix(0.982, -0.188, 0.188, 0.982, 0.787, 0.394)"/><rect class="m" width="0.016" height="0.182" transform="translate(0.517 0.446) rotate(-10.889)"/><rect class="m" width="0.016" height="0.182" transform="translate(0.355 0.477) rotate(-10.892)"/><path class="m" d="M2086.364,956.344l.016,0-.035-.179-.016,0Z" transform="translate(-2086.126 -955.659)"/><rect class="n" width="0.016" height="0.182" transform="matrix(0.982, -0.188, 0.188, 0.982, 0.068, 0.532)"/><path class="m" d="M2099.542,946.439l.014,0,.034.179.016,0-.034-.179-.015,0-.035-.179h0l-.034-.175h0l-.035-.179-.016,0,.034.179h0l.035.179h0Z" transform="translate(-2098.614 -945.886)"/><rect class="m" width="0.016" height="0.182" transform="matrix(0.982, -0.189, 0.189, 0.982, 0.927, 0.368)"/><path class="m" d="M2100.973,945.9h0l-.034-.179-.014,0h0l-.034-.178h-.006l-.01,0,.035.179.01,0h0l.034.175h0l-.01,0,.035.179-.009,0-.034-.179-.015,0-.033-.175.015,0-.034-.179-.016,0,.034.179-.015,0,.034.179.015,0,.034.175.012,0,.034.179.016,0-.034-.179h.007l.006,0-.034-.174h.007l.033.172a.039.039,0,0,0,.013-.018l-.028-.145A.037.037,0,0,0,2100.973,945.9Z" transform="translate(-2099.962 -945.544)"/><rect class="m" width="0.016" height="0.182" transform="translate(0.425 0.282) rotate(-10.889)"/><rect class="m" width="0.016" height="0.182" transform="translate(0.273 0.312) rotate(-10.872)"/><rect class="m" width="0.016" height="0.182" transform="translate(0.138 0.337) rotate(-10.86)"/><rect class="m" width="0.016" height="0.182" transform="translate(0.997 0.173) rotate(-10.86)"/><rect class="m" width="0.016" height="0.182" transform="translate(1.03 0.166) rotate(-10.841)"/><path class="m" d="M2104.293,948.9l.035.179.01,0,.005,0-.034-.178h-.006Z" transform="translate(-2103.238 -948.739)"/><path class="m" d="M2104.82,949.071l-.028-.145a.036.036,0,0,0-.019-.011l.033.174A.034.034,0,0,0,2104.82,949.071Z" transform="translate(-2103.696 -948.755)"/><rect class="m" width="0.016" height="0.182" transform="translate(0.719 0.04) rotate(-10.889)"/><path class="m" d="M2095.846,946.6l-.016,0,.035.179h0l.033.175h0l.035.179.016,0-.034-.175h0l-.034-.179h0Z" transform="translate(-2095.176 -946.546)"/><path class="m" d="M2093.759,947.182h0l.035.179h0l.034.175.016,0-.035-.179h0l-.034-.175h0l-.034-.179-.016,0Z" transform="translate(-2093.171 -946.931)"/><rect class="m" width="0.016" height="0.182" transform="translate(0.449 0.092) rotate(-10.86)"/><rect class="m" width="0.016" height="0.182" transform="matrix(0.982, -0.189, 0.189, 0.982, 0.287, 0.123)"/><rect class="m" width="0.016" height="0.182" transform="translate(0.135 0.153) rotate(-10.872)"/><rect class="m" width="0.016" height="0.182" transform="translate(0 0.178) rotate(-10.889)"/><rect class="m" width="0.016" height="0.182" transform="translate(0.859 0.014) rotate(-10.889)"/><path class="m" d="M2101.858,945.561l.033.174a.034.034,0,0,0,.014-.018l-.028-.145A.039.039,0,0,0,2101.858,945.561Z" transform="translate(-2100.918 -945.56)"/></g></g><g transform="translate(34.679 3.501)"><g transform="translate(0.422 1.394)"><path class="j" d="M2047.191,989.431l.02.1a.045.045,0,0,1,0,.023.038.038,0,0,1-.013.018l-.006,0-.006,0-1.411.271a.038.038,0,0,1-.041-.031v0l-.02-.1v0a.037.037,0,0,1,.027-.043l1.411-.271h.013a.037.037,0,0,1,.019.011A.042.042,0,0,1,2047.191,989.431Z" transform="translate(-2045.711 -989.398)"/><path class="n" d="M2046.523,992.615l-.756.145a.036.036,0,0,1-.038-.031v0l-.02-.1v0a.036.036,0,0,1,.023-.043l.663-.127A.342.342,0,0,0,2046.523,992.615Z" transform="translate(-2045.709 -992.309)"/><g transform="translate(0.512)"><rect class="m" width="0.016" height="0.182" transform="translate(0.773 0.03) rotate(-10.872)"/><rect class="m" width="0.016" height="0.182" transform="translate(0.719 0.04) rotate(-10.889)"/><rect class="m" width="0.016" height="0.182" transform="translate(0.654 0.053) rotate(-10.844)"/><rect class="m" width="0.016" height="0.182" transform="translate(0.554 0.072) rotate(-10.841)"/><rect class="m" width="0.016" height="0.182" transform="translate(0.449 0.092) rotate(-10.807)"/><rect class="m" width="0.016" height="0.182" transform="translate(0.287 0.123) rotate(-10.889)"/><rect class="m" width="0.016" height="0.182" transform="translate(0.135 0.153) rotate(-10.954)"/><rect class="m" width="0.016" height="0.182" transform="translate(0 0.178) rotate(-10.86)"/><rect class="m" width="0.016" height="0.182" transform="translate(0.825 0.02) rotate(-10.86)"/><rect class="m" width="0.016" height="0.182" transform="translate(0.859 0.014) rotate(-10.889)"/><rect class="m" width="0.016" height="0.182" transform="translate(0.892 0.007) rotate(-10.86)"/><path class="m" d="M2075.847,989.4l.035.179.01,0,.005,0-.034-.178h-.006Z" transform="translate(-2074.93 -989.398)"/><path class="m" d="M2076.327,989.415l.033.174a.036.036,0,0,0,.013-.018l-.028-.145A.041.041,0,0,0,2076.327,989.415Z" transform="translate(-2075.387 -989.414)"/></g></g><g transform="translate(0.237 1.244)"><path class="j" d="M2043.28,986.263l.02.1a.045.045,0,0,1,0,.023.034.034,0,0,1-.013.018l-.006,0-.006,0-1.411.271a.037.037,0,0,1-.041-.031v0l-.02-.1v0a.037.037,0,0,1,.027-.043l1.411-.271h.025l0,0h0l0,0A.044.044,0,0,1,2043.28,986.263Z" transform="translate(-2041.8 -986.23)"/><path class="n" d="M2042.613,989.446l-.756.145a.036.036,0,0,1-.038-.031.006.006,0,0,0,0,0l-.02-.1v0a.036.036,0,0,1,.023-.043l.663-.127,0,0A.339.339,0,0,0,2042.613,989.446Z" transform="translate(-2041.798 -989.14)"/><g transform="translate(0.512)"><rect class="m" width="0.016" height="0.182" transform="translate(0.773 0.03) rotate(-10.889)"/><rect class="m" width="0.016" height="0.182" transform="translate(0.719 0.04) rotate(-10.844)"/><rect class="m" width="0.016" height="0.182" transform="translate(0.654 0.053) rotate(-10.826)"/><rect class="m" width="0.016" height="0.182" transform="matrix(0.982, -0.188, 0.188, 0.982, 0.554, 0.072)"/><rect class="m" width="0.016" height="0.182" transform="matrix(0.982, -0.189, 0.189, 0.982, 0.449, 0.092)"/><rect class="m" width="0.016" height="0.182" transform="translate(0.287 0.123) rotate(-10.844)"/><rect class="m" width="0.016" height="0.182" transform="translate(0.135 0.153) rotate(-10.889)"/><rect class="n" width="0.016" height="0.182" transform="translate(0 0.179) rotate(-10.954)"/><rect class="m" width="0.016" height="0.182" transform="translate(0.825 0.02) rotate(-10.841)"/><rect class="m" width="0.016" height="0.182" transform="translate(0.859 0.014) rotate(-10.872)"/><rect class="m" width="0.016" height="0.182" transform="translate(0.892 0.007) rotate(-10.872)"/><path class="m" d="M2071.935,986.232l.035.179.01,0,.006,0-.035-.178h-.006Z" transform="translate(-2071.018 -986.23)"/><path class="m" d="M2072.416,986.247l.033.174a.036.036,0,0,0,.013-.018l-.028-.145A.035.035,0,0,0,2072.416,986.247Z" transform="translate(-2071.477 -986.246)"/></g></g><path class="j" d="M2044.109,982.281l.02.1a.045.045,0,0,1,0,.023.037.037,0,0,1-.013.018l-.006,0-.006,0-1.411.271a.031.031,0,0,1-.017,0,.04.04,0,0,1-.024-.027v0l-.02-.1v0a.037.037,0,0,1,.027-.044l1.411-.271h.013a.037.037,0,0,1,.019.011A.043.043,0,0,1,2044.109,982.281Z" transform="translate(-2042.353 -981.193)"/><path class="n" d="M2043.441,985.465l-.756.145a.027.027,0,0,1-.015,0,.041.041,0,0,1-.022-.028v0l-.02-.1v0a.035.035,0,0,1,.023-.043l.663-.127a.342.342,0,0,0,.122.158Z" transform="translate(-2042.351 -984.104)"/><path class="j" d="M2044.094,978.364l.02.1a.045.045,0,0,1,0,.023.036.036,0,0,1-.013.018l-.006,0-.006,0-1.411.271a.037.037,0,0,1-.041-.03v0l-.02-.1v0a.038.038,0,0,1,.023-.042h0l1.411-.271h.013a.035.035,0,0,1,.019.011A.041.041,0,0,1,2044.094,978.364Z" transform="translate(-2042.338 -977.461)"/><path class="n" d="M2043.426,981.548l-.756.145a.036.036,0,0,1-.037-.031v0l-.02-.1v0a.037.037,0,0,1,.02-.042h0l.663-.127h0A.337.337,0,0,0,2043.426,981.548Z" transform="translate(-2042.336 -980.372)"/><path class="j" d="M2041.178,975.011l.02.1a.045.045,0,0,1,0,.023.036.036,0,0,1-.013.018l-.006,0h-.006l-1.411.271a.038.038,0,0,1-.041-.031v0l-.02-.1v0a.038.038,0,0,1,.019-.039h0l.007,0,1.411-.271h.013a.035.035,0,0,1,.019.012A.04.04,0,0,1,2041.178,975.011Z" transform="translate(-2039.561 -974.267)"/><path class="n" d="M2040.51,978.194l-.756.145a.036.036,0,0,1-.038-.031v0l-.02-.1v0a.038.038,0,0,1,.017-.039h0l.005,0,.663-.127h0a.342.342,0,0,0,.126.16Z" transform="translate(-2039.559 -977.177)"/><path class="j" d="M2040.393,971.27l.019.1a.04.04,0,0,1,0,.023.036.036,0,0,1-.013.018l0,0h0l-.006,0-1.411.271h-.006a.038.038,0,0,1-.035-.031v0l-.02-.1v0a.037.037,0,0,1,.027-.044l1.411-.271h.025l0,0h0l0,0A.047.047,0,0,1,2040.393,971.27Z" transform="translate(-2038.813 -970.704)"/><path class="n" d="M2039.725,974.453l-.756.145h0a.038.038,0,0,1-.034-.032v0l-.019-.1v0a.035.035,0,0,1,.023-.043l.663-.127,0,0a.341.341,0,0,0,.124.156Z" transform="translate(-2038.811 -973.614)"/><path class="j" d="M2041.222,967.288l.02.1a.045.045,0,0,1,0,.023.037.037,0,0,1-.013.018l-.006,0-.006,0-1.411.271a.032.032,0,0,1-.017,0,.04.04,0,0,1-.024-.027v0l-.02-.1v0a.037.037,0,0,1,.027-.043l1.411-.271h.013a.036.036,0,0,1,.019.011A.045.045,0,0,1,2041.222,967.288Z" transform="translate(-2039.602 -966.91)"/><path class="j" d="M2042.9,963.03l.02.1a.043.043,0,0,1,0,.023.04.04,0,0,1-.013.018l-.006,0-.006,0-1.411.271a.032.032,0,0,1-.017,0,.04.04,0,0,1-.024-.027v0l-.02-.1a.005.005,0,0,0,0,0,.037.037,0,0,1,.027-.044l1.411-.271h.013a.036.036,0,0,1,.019.011A.041.041,0,0,1,2042.9,963.03Z" transform="translate(-2041.2 -962.854)"/><path class="n" d="M2040.554,970.471l-.756.145a.026.026,0,0,1-.015,0,.042.042,0,0,1-.022-.028v0l-.02-.1v0a.036.036,0,0,1,.023-.043l.663-.127a.341.341,0,0,0,.122.158Z" transform="translate(-2039.6 -969.821)"/><path class="n" d="M2042.259,966.225l-.756.145a.036.036,0,0,1-.037-.031v0l-.02-.1v0a.037.037,0,0,1,.02-.042h0l.663-.127h0A.34.34,0,0,0,2042.259,966.225Z" transform="translate(-2041.224 -965.776)"/><path class="j" d="M2038.291,960.018l.02.1a.045.045,0,0,1,0,.023.037.037,0,0,1-.013.018l-.006,0h-.006l-1.411.271a.037.037,0,0,1-.041-.03v0l-.02-.1v0a.038.038,0,0,1,.027-.044l1.411-.271h.022l0,0h0l0,0A.043.043,0,0,1,2038.291,960.018Z" transform="translate(-2036.81 -959.985)"/><path class="n" d="M2037.623,963.2l-.756.145a.036.036,0,0,1-.038-.031v0l-.02-.1v0a.036.036,0,0,1,.023-.043l.663-.127v0a.341.341,0,0,0,.125.157Z" transform="translate(-2036.808 -962.896)"/><g transform="translate(0.512 0.01)"><rect class="m" width="0.016" height="0.182" transform="translate(1.049 1.075) rotate(-10.872)"/><rect class="m" width="0.016" height="0.182" transform="matrix(0.982, -0.188, 0.188, 0.982, 0.995, 1.085)"/><rect class="m" width="0.016" height="0.182" transform="translate(0.929 1.098) rotate(-10.954)"/><rect class="m" width="0.016" height="0.182" transform="matrix(0.982, -0.19, 0.19, 0.982, 0.83, 1.117)"/><rect class="m" width="0.016" height="0.182" transform="translate(0.724 1.137) rotate(-10.889)"/><rect class="m" width="0.016" height="0.182" transform="translate(0.562 1.168) rotate(-10.872)"/><rect class="m" width="0.016" height="0.182" transform="translate(0.411 1.197) rotate(-10.841)"/><rect class="n" width="0.016" height="0.182" transform="translate(0.276 1.223) rotate(-10.807)"/><rect class="m" width="0.016" height="0.182" transform="translate(1.101 1.065) rotate(-10.939)"/><path class="m" d="M2071.58,982.651l.016,0-.034-.179-.016,0Z" transform="translate(-2070.41 -981.413)"/><rect class="m" width="0.016" height="0.182" transform="translate(1.168 1.052) rotate(-10.892)"/><path class="m" d="M2072.764,982.25l.035.179.01,0,.006,0-.035-.178h-.006Z" transform="translate(-2071.572 -981.203)"/><path class="m" d="M2073.244,982.265l.034.174a.039.039,0,0,0,.013-.018l-.028-.145A.036.036,0,0,0,2073.244,982.265Z" transform="translate(-2072.028 -981.219)"/><rect class="m" width="0.016" height="0.182" transform="matrix(0.982, -0.188, 0.188, 0.982, 0.562, 0.983)"/><rect class="m" width="0.016" height="0.182" transform="translate(0.41 1.012) rotate(-10.841)"/><rect class="n" width="0.016" height="0.182" transform="matrix(0.982, -0.188, 0.188, 0.982, 0.275, 1.038)"/><path class="m" d="M2068.811,968.187l-.035-.179-.014,0h0l-.034-.178h0l-.034-.174h.006l.033.172a.034.034,0,0,0,.013-.018l-.028-.145a.037.037,0,0,0-.015-.01h.006l-.035-.179-.016,0,.034.175h-.005l-.01,0,.034.179h0l.034.178.01,0h0l.035.179Z" transform="translate(-2067.66 -967.131)"/><rect class="m" width="0.016" height="0.182" transform="matrix(0.982, -0.188, 0.188, 0.982, 1.134, 0.873)"/><rect class="m" width="0.016" height="0.182" transform="translate(1.167 0.866) rotate(-10.889)"/><path class="m" d="M2072.749,978.333l.035.179.01,0,.006,0-.035-.178h-.006Z" transform="translate(-2071.557 -977.472)"/><path class="m" d="M2073.23,978.348l.033.174a.037.037,0,0,0,.013-.018l-.028-.145A.037.037,0,0,0,2073.23,978.348Z" transform="translate(-2072.015 -977.488)"/><path class="m" d="M2066.107,972.162l-.034-.179h0l-.034-.178-.016,0,.035.179h0l.034.177-.016,0,.035.179.016,0-.035-.179Z" transform="translate(-2065.147 -971.256)"/><path class="m" d="M2063.554,972.466h0l-.034-.178-.016,0,.035.179h0l.034.178h0l.035.179.016,0-.035-.179h0Z" transform="translate(-2062.747 -971.716)"/><path class="m" d="M2070.315,974.995l.033.174a.039.039,0,0,0,.013-.018l-.028-.145A.038.038,0,0,0,2070.315,974.995Z" transform="translate(-2069.239 -974.294)"/><path class="m" d="M2061.413,972.693l-.016,0,.035.179h0l.034.178h0l.035.179.016,0-.034-.179h0l-.035-.179h0Z" transform="translate(-2060.743 -972.101)"/><path class="m" d="M2059.186,973.121l-.016,0,.035.179h0l.034.178.016,0-.035-.179h0Z" transform="translate(-2058.622 -972.508)"/><path class="m" d="M2055.774,973.776l-.016,0,.035.179h0l.034.178.016,0-.035-.179h0Z" transform="translate(-2055.371 -973.133)"/><path class="n" d="M2052.579,974.389l-.016,0,.035.179h0l.035.178.016,0-.035-.179h0Z" transform="translate(-2052.328 -973.717)"/><path class="n" d="M2049.725,974.938l-.016,0,.035.179h0l.034.178.017,0-.035-.179h0Z" transform="translate(-2049.609 -974.239)"/><path class="m" d="M2067.125,971.6l-.016,0,.035.179h0l.034.178h0l.035.179.016,0-.034-.179h0l-.035-.179h0Z" transform="translate(-2066.185 -971.056)"/><path class="m" d="M2066.91,968l-.013,0-.034-.175-.016,0,.035.179.013,0,.034.175h0l.034.178.016,0-.035-.179h0Z" transform="translate(-2065.936 -967.463)"/><rect class="m" width="0.016" height="0.182" transform="translate(0.858 0.375) rotate(-10.86)"/><path class="m" d="M2064.364,968.485h-.008l.034.179h0l.034.178.016,0-.035-.179h0l-.033-.173h.008l-.035-.179-.016,0Z" transform="translate(-2063.538 -967.923)"/><rect class="m" width="0.016" height="0.182" transform="matrix(0.982, -0.188, 0.188, 0.982, 0.693, 0.406)"/><rect class="m" width="0.016" height="0.182" transform="translate(0.587 0.427) rotate(-10.872)"/><rect class="m" width="0.016" height="0.182" transform="translate(0.426 0.458) rotate(-10.889)"/><rect class="m" width="0.016" height="0.182" transform="translate(0.274 0.487) rotate(-10.779)"/><rect class="m" width="0.016" height="0.182" transform="translate(0.139 0.513) rotate(-10.872)"/><path class="m" d="M2067.971,967.792h-.006l.035.179h0l.034.178-.015,0,.035.179.016,0-.035-.179.015,0-.035-.179h0l-.034-.173h.006l-.035-.179-.016,0Z" transform="translate(-2066.974 -967.263)"/><rect class="m" width="0.016" height="0.182" transform="translate(1.031 0.341) rotate(-10.872)"/><path class="m" d="M2069.927,967.433l-.034-.178h-.006l-.01,0,.034.179.01,0Z" transform="translate(-2068.821 -966.921)"/><path class="m" d="M2070.4,967.428l-.028-.145a.036.036,0,0,0-.019-.011l.033.174A.034.034,0,0,0,2070.4,967.428Z" transform="translate(-2069.278 -966.937)"/><rect class="m" width="0.016" height="0.182" transform="translate(0.993 0.163) rotate(-10.889)"/><rect class="m" width="0.016" height="0.182" transform="translate(0.774 0.205) rotate(-10.911)"/><rect class="m" width="0.016" height="0.182" transform="translate(0.668 0.225) rotate(-10.763)"/><rect class="m" width="0.016" height="0.182" transform="translate(0.506 0.256) rotate(-10.86)"/><rect class="m" width="0.016" height="0.182" transform="matrix(0.982, -0.188, 0.188, 0.982, 0.355, 0.285)"/><rect class="m" width="0.016" height="0.182" transform="translate(0.22 0.311) rotate(-10.872)"/><rect class="m" width="0.016" height="0.182" transform="translate(1.045 0.153) rotate(-10.844)"/><rect class="m" width="0.016" height="0.182" transform="translate(1.079 0.146) rotate(-10.841)"/><rect class="m" width="0.016" height="0.182" transform="translate(1.112 0.14) rotate(-10.841)"/><path class="m" d="M2071.582,963.011l.035.179.01,0,.006,0-.034-.178h-.006Z" transform="translate(-2070.445 -962.876)"/><path class="m" d="M2072.063,963.025l.034.174a.036.036,0,0,0,.013-.018l-.028-.145A.034.034,0,0,0,2072.063,963.025Z" transform="translate(-2070.904 -962.891)"/><rect class="m" width="0.016" height="0.182" transform="translate(0.719 0.03) rotate(-10.86)"/><rect class="m" width="0.016" height="0.182" transform="translate(0.448 0.082) rotate(-10.889)"/><rect class="m" width="0.016" height="0.182" transform="matrix(0.982, -0.188, 0.188, 0.982, 0.287, 0.113)"/><rect class="m" width="0.016" height="0.182" transform="translate(0.135 0.142) rotate(-10.954)"/><rect class="m" width="0.016" height="0.182" transform="translate(0 0.168) rotate(-10.86)"/><rect class="m" width="0.016" height="0.182" transform="translate(0.859 0.003) rotate(-10.881)"/></g></g><g transform="translate(33.851 4.027)"><path class="s" d="M2020.47,972.216a.7.7,0,1,1,.979.088A.7.7,0,0,1,2020.47,972.216Z" transform="translate(-2020.261 -971.075)"/><path class="j" d="M2020.49,973.105l-.023.02-.008.006-.018.014a.7.7,0,0,1-.927-1.031.758.758,0,0,1,.061-.057l.024-.019.024-.017a.666.666,0,0,1,.075-.046.682.682,0,0,1,.119-.049.7.7,0,0,1,.673,1.179Z" transform="translate(-2019.326 -971.857)"/><g transform="translate(0.052 0.093)"><path class="n" d="M2021.082,974.324l-.059,0a.645.645,0,0,1-.439-.23h0a.648.648,0,1,1,.5.233Zm0-1.229a.581.581,0,0,0-.445.954h0a.581.581,0,1,0,.5-.952Z" transform="translate(-2020.432 -973.027)"/></g><g transform="translate(0.384 0.314)"><path class="n" d="M2028.063,978.135l-.006-.009a.182.182,0,0,0-.053-.051.186.186,0,0,0-.073-.026.316.316,0,0,0-.086,0c-.031,0-.064.01-.1.018a.47.47,0,0,1-.057.01.14.14,0,0,1-.042,0,.072.072,0,0,1-.03-.013.1.1,0,0,1-.022-.025.1.1,0,0,1-.014-.031.071.071,0,0,1,.013-.062.11.11,0,0,1,.031-.028.089.089,0,0,1,.074-.014.129.129,0,0,1,.07.06l.128-.083a.263.263,0,0,0-.06-.067.2.2,0,0,0-.149-.041.262.262,0,0,0-.082.025l-.058-.089-.071.046.057.088a.284.284,0,0,0-.061.064.221.221,0,0,0-.033.071.181.181,0,0,0,0,.073.2.2,0,0,0,.029.072.179.179,0,0,0,.132.086.318.318,0,0,0,.086,0c.031,0,.064-.011.1-.02a.375.375,0,0,1,.054-.01.146.146,0,0,1,.041,0,.079.079,0,0,1,.032.013.106.106,0,0,1,.025.027.086.086,0,0,1,.013.032.075.075,0,0,1,0,.033.09.09,0,0,1-.017.032.135.135,0,0,1-.033.029.167.167,0,0,1-.039.019.1.1,0,0,1-.042,0,.1.1,0,0,1-.042-.017.148.148,0,0,1-.04-.043l-.128.083a.243.243,0,0,0,.071.074.2.2,0,0,0,.081.031.225.225,0,0,0,.084,0,.365.365,0,0,0,.082-.03l.053.082.071-.046-.054-.082a.309.309,0,0,0,.062-.064.215.215,0,0,0,.032-.065v-.005a.176.176,0,0,0,0-.073A.193.193,0,0,0,2028.063,978.135Z" transform="translate(-2027.434 -977.708)"/></g></g></g><g transform="translate(1024.824 677.405)"><path class="q" d="M2476.177,959.489a1.527,1.527,0,0,1-.887.284,1.383,1.383,0,0,1-.729-.349l-.257.5a2.007,2.007,0,0,0,1.2.227,1.4,1.4,0,0,0,.549-.351A.478.478,0,0,0,2476.177,959.489Z" transform="translate(-2474.304 -958.151)"/><path class="l" d="M2502.01,932.907l.69-.324a2.29,2.29,0,0,1-1.216,1.969S2502.2,933.516,2502.01,932.907Z" transform="translate(-2500.196 -932.583)"/></g><g transform="translate(959.584 693.323)"><path class="q" d="M1099.52,1270.709a2.149,2.149,0,0,1-.728-1.089,1.947,1.947,0,0,1,.19-1.12l-.775-.154a2.821,2.821,0,0,0,.159,1.712,1.963,1.963,0,0,0,.687.606A.671.671,0,0,0,1099.52,1270.709Z" transform="translate(-1098.14 -1268.346)"/><path class="l" d="M1106.888,1306.759l.705.807a3.218,3.218,0,0,1-3.132-.881S1106.139,1307.252,1106.888,1306.759Z" transform="translate(-1104.161 -1304.868)"/></g><g transform="translate(997.945 697.013)"><path class="q" d="M1907.815,1348.678a2.055,2.055,0,0,1,.259-1.225,1.859,1.859,0,0,1,.892-.618l-.412-.633a2.7,2.7,0,0,0-1.064,1.252,1.875,1.875,0,0,0,.045.874A.64.64,0,0,0,1907.815,1348.678Z" transform="translate(-1907.45 -1346.202)"/><path class="l" d="M1908.907,1376.869l-.081,1.021a3.076,3.076,0,0,1-1.49-2.729S1908.07,1376.686,1908.907,1376.869Z" transform="translate(-1907.32 -1373.787)"/></g><g transform="translate(982.607 691.508)"><path class="r" d="M1587.673,1230.309s.172-.37.935-.175c.342.087.935.59.151,1.015-.815.442-1.072-.02-1.072-.02s1.313-.216,1.125-.746c-.207-.585-.756.048-.756.048A1.011,1.011,0,0,0,1587.673,1230.309Z" transform="translate(-1587.488 -1230.077)"/><path class="n" d="M1583.828,1241.328a.407.407,0,0,1,.088-.519l.325.26s-.317.25,0,.474C1584.242,1241.543,1583.874,1241.442,1583.828,1241.328Z" transform="translate(-1583.771 -1240.3)"/></g><g transform="translate(1008.037 668.755)"><path class="i" d="M2138.857,750.57a1.245,1.245,0,0,1,1.755.342l.827-.356s-.408-.644-2.01-.359C2138.38,750.385,2138.857,750.57,2138.857,750.57Z" transform="translate(-2137.87 -750.125)"/><path class="t" d="M2120.206,751.251s.273-1.127,2.329-1.127a2.012,2.012,0,0,0-1.373,1.073Z" transform="translate(-2120.206 -750.124)"/></g><path class="g" d="M1976.915,839.469s-.554-.35-.612-.772c0,0-.539.029-.656.2,0,0,.248.7.539.831C1976.186,839.732,1976.842,839.775,1976.915,839.469Z" transform="translate(-974.463 -165.743)"/><path class="g" d="M1154.907,1138.008s-.554-.35-.612-.772c0,0-.539.029-.656.2,0,0,.248.7.539.831C1154.178,1138.271,1154.834,1138.314,1154.907,1138.008Z" transform="translate(-191.424 -450.129)"/><path class="f" d="M1649.409,1520.318s.568-.359.628-.793c0,0,.554.03.673.209,0,0-.254.718-.553.853C1650.157,1520.587,1649.484,1520.632,1649.409,1520.318Z" transform="translate(-663.691 -814.295)"/><path class="j" d="M2168.064,986.614a2.591,2.591,0,0,1-.193-1.2,2.054,2.054,0,0,0,.018-.754,7.341,7.341,0,0,0-.747-1.084c.01.054.036.329.077.587-.159-.114-.321-.223-.337-.2-.057.09.212.348.4.495a.511.511,0,0,0,.066.161.219.219,0,0,1,.044.225c-.006.037-.012.071-.017.1-.176-.107-.462-.241-.487-.181-.037.086.244.342.342.562a.438.438,0,0,0,.21.251c.071.027.132.195.132.195l.187.96Z" transform="translate(-1156.625 -303.751)"/><path class="d" d="M1240.646,1140.424s3.479,3.648,4.011,4.519l.434-1.256s-2.2-4.2-2.32-4.232A2.052,2.052,0,0,0,1240.646,1140.424Z" transform="translate(-274.306 -452.192)"/><path class="d" d="M1324.592,1229.786s.124.417.365.511.741,1.157.924,1.222c0,0-.073.145-.394,0a12.289,12.289,0,0,1-1.385-1.073Z" transform="translate(-353.807 -538.292)"/><path class="d" d="M1319.982,1242.835a1.558,1.558,0,0,1,.128.382c.073.857.173.992.307,1.2a2.5,2.5,0,0,0,.942.648l-.3-1.542c-.027-.136-.179-.972-.179-.972l-1.161-.385Z" transform="translate(-349.63 -550.084)"/><path class="d" d="M1499.807,708.79c0,.052.1,5.224.1,5.224s1.488.581,2.038.1l-1.345-5.77Z" transform="translate(-521.181 -41.573)"/><path class="d" d="M1492.071,683.545a4.623,4.623,0,0,1-.328-.906c-.015-.321-.2-.627.183-.736a1.263,1.263,0,0,1,.969.073,1.973,1.973,0,0,1,.185.931c-.061.183-.193.486-.193.486Z" transform="translate(-513.445 -16.329)"/><path class="d" d="M1487.987,690.181a4.124,4.124,0,0,0-.515-.408c-.147-.073-.031-.923,0-.96,0,0,.179,0,.163.376a.333.333,0,0,0,.209.324Z" transform="translate(-509.361 -22.964)"/></g></svg>