import * as React from 'react';
import Button from '@mui/material/Button';
import { useSnackbar } from 'notistack';
import Dialog from '@mui/material/Dialog';
import DialogActions from '@mui/material/DialogActions';
import DialogContent from '@mui/material/DialogContent';
import DialogTitle from '@mui/material/DialogTitle';
import { Autocomplete, Grid, IconButton, InputAdornment, TextField} from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import { SelectDropdown, TextInput } from '../../../../components';
import { CONFIG } from '../../../../appconfig';
import Textarea from '@mui/material/TextareaAutosize';
import BookingDetails from './BookingDetails';
import {GetBookingDetailsForCreditChangeService, CreateCreditChangeRequest,ValidateReferenceLeadService,IsUserEligibleService} from '../../../../layouts/SV/components/Sidebar/helper/sidebarHelper';

export default function CreateCreditChangeRequestPopup({ open, onClose, ProductSelect,AgentList,ReasonList }) {
    const [bookingID, setBookingID] = React.useState();
    const [bookingData, setbookingData] = React.useState([]);
    const [SelectedAgent, setSelectedAgent] = React.useState(0);
    const [SelectedAgentName, setSelectedAgentName] = React.useState("Select");
    const [AgentTypeListMapping, setAgentTypeListMapping] = React.useState([]);
    const [SelectedAgentType, setSelectedAgentType] = React.useState('');
    const [Selectdropwnstate, setSelectdropwnstate] = React.useState(true);
    const [SelectAgentTypedropwnstate, setSelectAgentTypedropwnstate] = React.useState(true);
    const [SelectReasondropwnstate, setSelectReasondropwnstate] = React.useState(true);
    const [SelectedReason, setSelectedReason] = React.useState('');
    const [FilteredReasonList, setFilteredReasonList] = React.useState([]);
    const [Comments, setComments] = React.useState('');
    const [isValidatedRequest, setisValidatedRequest] = React.useState(false);
    const [BookingAgentUserID, setBookingAgentUserID] = React.useState(0);
    const [SecondaryAgentUserID, setSecondaryAgentUserID] = React.useState(0);
    const [BookingDate, setBookingDate] = React.useState(0);
    const [ReferenceId, setReferenceId] = React.useState('');
    const [IsOldFosAgent,setIsOldFosAgent] = React.useState(0);
    const [IsUserEligible,setIsUserEligible] = React.useState(true);
    const [EligibiltyMessage, setEligibiltyMessage] = React.useState('');
    const [NewRequestData, setNewRequestData] = React.useState({
        BookingID: '',
        NewAgentID: '',
        AgentTypeID: '',
        ReasonID: '',
        RequestorRemarks: '',
        ReferenceId: ''
    });
    const { enqueueSnackbar } = useSnackbar();


    const AgentTypeList = [
        {
          label: "Primary - Call Center",//call centre
          value: 1
        },
        {
          label: "Secondary - Call Center",//call centre
          value: 2
        },
        {
          label: "Primary - FOS", //FOS   
          value: 3
        }
      ];
    
    const AgentMapping = AgentList.map(item => ({
        value: item.UserId,
        label: `${item.UserName} - ${item.EmployeeId}`
    }));
    
      React.useEffect(() => {
        if (open) {
            // Reset all state variables when the dialog is opened
            setBookingID('');
            setIsOldFosAgent(0);
            setbookingData([]);
            setSelectedAgent('');
            setSelectedAgentName("Select");
            setAgentTypeListMapping([]);
            setSelectedAgentType('');
            setSelectdropwnstate(true);
            setSelectAgentTypedropwnstate(true);
            setSelectReasondropwnstate(true);
            setSelectedReason('');
            setFilteredReasonList([]);
            setComments('');
            setisValidatedRequest(false);
            setReferenceId('');
            setNewRequestData({
                BookingID: '',
                NewAgentID: '',
                AgentTypeID: '',
                ReasonID: '',
                RequestorRemarks: '',
                ReferenceId:''
            });
        }
    }, [open]);

    function isBookingValid(dateStr, AgentTypeID) {
        const selectedUser = AgentList.find(user => user.UserId == SelectedAgent);
        if(selectedUser.UserProductId == 147){
            let ThresholdDays = 90;
            const givenDate = new Date(dateStr);

            if (isNaN(givenDate.getTime())) {
                throw new Error("Invalid date format");
            }
            const currentDate = new Date();
            const diffInMs = currentDate - givenDate;
        
            const diffInDays = diffInMs / (1000 * 60 * 60 * 24); // milliseconds to days
            return diffInDays > ThresholdDays;
        }
        else if(AgentTypeID == 1){
            let ThresholdDays = 30;
            const givenDate = new Date(dateStr);

            if (isNaN(givenDate.getTime())) {
                throw new Error("Invalid date format");
            }
            const currentDate = new Date();
            const diffInMs = currentDate - givenDate;
        
            const diffInDays = diffInMs / (1000 * 60 * 60 * 24); // milliseconds to days
            return diffInDays > ThresholdDays;
        }
        else{
            const givenDate = new Date(dateStr);

            const lastDateOfMonth = new Date(givenDate.getFullYear(), givenDate.getMonth() + 1, 0);

            var lastDate = new Date(lastDateOfMonth);
            lastDate.setDate(lastDate.getDate() + 46);

            const currentDate = new Date();

            const diffInMs = currentDate - lastDate;
            const diffInDays = diffInMs / (1000 * 60 * 60 * 24);

            return diffInDays > 0;
        }
    }
    function isBookingValidMotor(dateStr) {
        
        const givenDate = new Date(dateStr);

        const lastDateOfMonth = new Date(givenDate.getFullYear(), givenDate.getMonth() + 1, 0);

        var lastDate = new Date(lastDateOfMonth);
        lastDate.setDate(lastDate.getDate() + 46);

        const currentDate = new Date();

        const diffInMs = currentDate - lastDate;
        const diffInDays = diffInMs / (1000 * 60 * 60 * 24);

        return diffInDays > 0;
    }

    function isBookingValidTermInv(dateStr) {
        
        let ThresholdDays = 3;
        if(ProductSelect == 7){
            ThresholdDays = 10;
            if([2].includes(SelectedAgentType)){
                // ThresholdDays = 60;
            }
        }

        if([145,146,147,148].includes(SelectedReason)){
            ThresholdDays = 60;
        }

        const givenDate = new Date(dateStr);

        if (isNaN(givenDate.getTime())) {
            throw new Error("Invalid date format");
        }
        const currentDate = new Date();
        const diffInMs = currentDate - givenDate;
    
        const diffInDays = diffInMs / (1000 * 60 * 60 * 24); // milliseconds to days
        return diffInDays > ThresholdDays;
    }

    const FetchBookingDetails = () => {
        GetBookingDetailsForCreditChangeService(bookingID, ProductSelect).then((result) => {
            if(result && result.length > 0)
            {
                let details = result[0];
                if(details && Array.isArray(details) && details[0].productId > 0 && !details[0].DifferentProduct && !details[0].LeadNotBooked)
                {
                    setbookingData(details);
                    setBookingDate(details[0].OfferCreatedON);
                    setBookingAgentUserID(details[0].BookingAgentUserID);
                    setSecondaryAgentUserID(details[0].SecondaryAgentUserID);
                    setSelectdropwnstate(false);
                    setIsOldFosAgent(details[0].IsFosAgent);
                }
                else if(details[0].DifferentProduct){
                    enqueueSnackbar("BookingId is of Different Product!", { variant: 'error', autoHideDuration: 3000, });
                }
                else if(details[0].LeadNotBooked){
                    enqueueSnackbar("Lead is not booked!", { variant: 'error', autoHideDuration: 3000, });
                }
            }
            else
            {
                enqueueSnackbar("Please enter a valid BookingID", { variant: 'error', autoHideDuration: 3000, });
                setbookingData([]); 
            }
      });
    }

    const ValidateReferenceLead = () =>{
        if(ReferenceId == bookingID){
            enqueueSnackbar("Reference Lead and BookingId cannot be same.", { variant: 'error', autoHideDuration: 3000, });
            return;
        }
        ValidateReferenceLeadService(bookingID,ReferenceId).then((result) => {
            if(result){
                enqueueSnackbar("Reference Lead is Valid!", { variant: 'success', autoHideDuration: 3000, });
                setSelectdropwnstate(false);
            }
            else{
                enqueueSnackbar("The reference lead is either invalid or associated with a different product", { variant: 'error', autoHideDuration: 3000, });
            }
        })
    }

    React.useEffect(() => {
        if(ReferenceId == '' || ReferenceId == 0){
            setSelectdropwnstate(false);
        }
        else{
            setSelectdropwnstate(true);
        }
    },[ReferenceId])

    React.useEffect(() => {
        var reasons = ReasonList;
        let selectedUser = AgentList.find((user) => user.UserId == SelectedAgent);
        if(selectedUser && selectedUser.UserId == 124){
            reasons = reasons.filter((reason) => reason.ReasonID == 89);
        }
        else if(selectedUser && selectedUser.UserProductId == 147){
            reasons = reasons.filter((reason) => reason.ProductId == 147 && reason.AgentTypeID == SelectedAgentType);//For health renewal
        }
        else{
            reasons = reasons.filter((reason) => reason.ProductId == ProductSelect && reason.AgentTypeID == SelectedAgentType)
        }
        setFilteredReasonList(reasons);

        if([117].indexOf(ProductSelect) > -1){
            IsUserEligibleMethod(bookingID,ReferenceId,ProductSelect,SelectedAgent,SelectedAgentType)
        }
    },[SelectedAgentType])

    React.useEffect(() => {
        if([7,115].includes(ProductSelect)){
            if([92,94,100,102,108,110,116,118,124,126,132,134,145,146,147,148].includes(SelectedReason)){//Reasons for term/Inv which needs to bypass check of 2 min
                setIsUserEligible(true);
                setEligibiltyMessage('');
            }
            else{
                IsUserEligibleMethod(bookingID,ReferenceId,ProductSelect,SelectedAgent,SelectedAgentType)
            }
        }
    },[SelectedReason])

    const IsUserEligibleMethod = (BookingId,ReferenceId,ProductSelect,SelectedAgent,AgentType) => {
        if(ReferenceId < 1){
            ReferenceId = 0
        }
        if(AgentType > 0){
            IsUserEligibleService(BookingId,ReferenceId,ProductSelect,SelectedAgent,AgentType).then((result) =>{
                if(result){
                    if(result.status == false){
                        setIsUserEligible(false);
                        setEligibiltyMessage(result.message);
                    }
                    else{
                        setIsUserEligible(true);
                    }
                }
            }).catch((err) => {
                setIsUserEligible(false);
                let ErrorMsg = "Not able to fetch data! Please try again after sometime!";
                enqueueSnackbar(ErrorMsg, { variant: 'error', autoHideDuration: 3000, });
                setEligibiltyMessage(ErrorMsg);
            })
        }
    }

    const handleChange = (event) => {
        let value = event.target.value;
        const name = event.target.name;
        switch (name) {
            case 'BookingID':
                value = value.trim();
                setBookingID(value);
                setSelectedAgent(0);
                setSelectedAgentName("Select");
                setSelectedAgentType('');
                setSelectedReason('');
                setSelectdropwnstate(true);
                setAgentTypeListMapping([]);
                setSelectAgentTypedropwnstate(true);
                setSelectReasondropwnstate(true);
                setFilteredReasonList([]);
                setReferenceId('');
                setNewRequestData({ ...NewRequestData, BookingID: value,
                    ReferenceId:'',
                    NewAgentID:0,
                    AgentTypeID: 0,
                    ReasonID: 0,
                    RequestorRemarks : "" });
                break;
            case 'ReferenceId':
                value = value.trim();
                setReferenceId(value);
                setSelectdropwnstate(true);
                setSelectedAgent(0);
                setSelectedAgentName("Select");
                setSelectedAgentType('');
                setSelectedReason('');
                setAgentTypeListMapping([]);
                setSelectAgentTypedropwnstate(true);
                setSelectReasondropwnstate(true);
                setFilteredReasonList([]);
                setNewRequestData({ ...NewRequestData, ReferenceId: value,
                    NewAgentID:0,
                    AgentTypeID: 0,
                    ReasonID: 0,
                    RequestorRemarks : "" });
                break;
            case 'NewAdvisor':
                setSelectedAgent(value.value);
                const selectedUser = AgentList.find(user => user.UserId === value.value);
                setSelectedAgentName(`${selectedUser.UserName} - ${selectedUser.EmployeeId}`);
                let AgentTypeData = [];
                if(selectedUser && selectedUser.UserProductId == 147){//Condition for renewals, 
                    AgentTypeData = AgentTypeList.filter((type) => type.value != 3);
                    setIsOldFosAgent(true);
                }
                else{
                    if (selectedUser && selectedUser.IsFosAgent) {
                        AgentTypeData = AgentTypeList.filter((type) => type.value == 3);
                    } else {
                        AgentTypeData = AgentTypeList.filter((type) => type.value != 3);
                        if(IsOldFosAgent == true){
                            AgentTypeData = AgentTypeList.filter((type) => type.value == 2);
                        }
                    }
                }
                setAgentTypeListMapping(AgentTypeData);
                setSelectAgentTypedropwnstate(false);
                setSelectedAgentType('');
                setSelectedReason('');
                setNewRequestData({ ...NewRequestData,NewAgentID:value.value,
                    AgentTypeID: 0,
                    ReasonID: 0,
                    RequestorRemarks : "" });
                setIsUserEligible(true);
                setComments('')
                 break;
            case 'AgentType':
                setSelectedAgentType(value);
                setSelectedReason('');
                setEligibiltyMessage('');
                setComments('');
                setNewRequestData({ ...NewRequestData, AgentTypeID: value,
                    ReasonID: 0});
                break;
            case 'reason':
                setSelectedReason(value);
                setComments('');
                setNewRequestData({ ...NewRequestData,ReasonID: value});
                setSelectReasondropwnstate(false);
                break;
            case 'Remarks':
                setComments(value);
                setNewRequestData({ ...NewRequestData,RequestorRemarks : value});
            default: 
                break;
        }
    };

    React.useEffect(() => {
        setSelectedAgent(0);
        setSelectedAgentName("Select");
        setSelectedAgentType('');
        setSelectedReason('');
        setAgentTypeListMapping([]);
        setSelectAgentTypedropwnstate(true);
        setFilteredReasonList([]);
        setNewRequestData({ ...NewRequestData, NewAgentID:0,
            AgentTypeID: 0,
            ReasonID: 0,
            RequestorRemarks : "" });
    }, [bookingID]);


    React.useEffect(() => {
        if(ProductSelect == 2){
            ValidateSubmitRequestHealth();
        }
        else if([117].indexOf(ProductSelect) > -1){
            ValidateSubmitRequestMotor();
        }
        else if([7,115].indexOf(ProductSelect) > -1){
            ValidateSubmitRequestTermInv();
        }
    }, [bookingID, SelectedAgent, SelectedReason, Comments, ReferenceId]);

    const ValidateSubmitRequestHealth = () =>
    {
        if(SelectedAgentType == 2 && IsOldFosAgent == false){
            enqueueSnackbar('Request for the "Secondary Advisor" cannot be raised because the primary booking is not mapped to the FOS advisor.', { variant: 'error', autoHideDuration: 2000, });
            setisValidatedRequest(false);
            return;
        }
        else if(isNaN(ReferenceId)){
            enqueueSnackbar("Please enter a valid Reference Id.",{ variant: 'error', autoHideDuration: 2000, });
            setisValidatedRequest(false);
            return;
        }
        else if(BookingDate != 0 && SelectedAgentType!='' && isBookingValid(BookingDate,SelectedAgentType) == true)
        {
            const selectedUser = AgentList.find(user => user.UserId === SelectedAgent);
            if(SelectedAgentType == 1)
            {
                let thresholdday = 30;
                enqueueSnackbar('Credit change request cannot be created for bookings older than ' + thresholdday + ' days', { variant: 'error', autoHideDuration: 2000, });
            }
            else if(SelectedAgentType == 2 && selectedUser.UserProductId == 147){
                let thresholdday = 90;
                enqueueSnackbar('Credit change request cannot be created for bookings older than ' + thresholdday + ' days', { variant: 'error', autoHideDuration: 2000, });
            }
            else{
                enqueueSnackbar('Credit requests for Incentive month bookings must be submitted by the 15th of the Payout month', { variant: 'error', autoHideDuration: 2000, });
            }
            return;
        }       
        else if((BookingAgentUserID == SelectedAgent && (SelectedAgentType == 1 || SelectedAgentType == 3 )) || (SecondaryAgentUserID == SelectedAgent && SelectedAgentType == 2 ))
        {
            enqueueSnackbar("This Booking is already mapped to the selected advisor",{ variant: 'error', autoHideDuration: 2000, });
            setisValidatedRequest(false);
            return;
        }
        if(bookingID !== 0 && SelectedAgent !== 0 && SelectedAgentType !== '' && SelectedReason !== '' 
            && Comments !== '' && Comments.length >= 10 && Comments.length<= 400)
        {
                setNewRequestData({ ...NewRequestData,RequestorRemarks : Comments.trim()});
                setisValidatedRequest(true);
                return;
        }
        setisValidatedRequest(false);
    }
    const ValidateSubmitRequestMotor = () => {
        if(isNaN(ReferenceId)){
            enqueueSnackbar("Please enter a valid Reference Id.",{ variant: 'error', autoHideDuration: 2000, });
            setisValidatedRequest(false);
            return;
        }
        else if(BookingDate != 0 && SelectedAgentType!='' && isBookingValidMotor(BookingDate,SelectedAgentType) == true)
        {
            enqueueSnackbar('Credit requests for Incentive month bookings must be submitted by the 15th of the Payout month', { variant: 'error', autoHideDuration: 2000, });
            return;
        }
        else if(!IsUserEligible && SelectedAgentType > 0 && EligibiltyMessage){
            setisValidatedRequest(false);
            enqueueSnackbar(EligibiltyMessage, { variant: 'error', autoHideDuration: 3000, });
            return;
        }
        else if(SelectedAgentType == 2 && IsOldFosAgent == false){
            enqueueSnackbar('Request for the "Secondary Advisor" cannot be raised because the primary booking is not mapped to the FOS advisor.', { variant: 'error', autoHideDuration: 2000, });
            setisValidatedRequest(false);
            return;
        }
        else if((BookingAgentUserID == SelectedAgent && (SelectedAgentType == 1 || SelectedAgentType == 3 )) || (SecondaryAgentUserID == SelectedAgent && SelectedAgentType == 2 ))
        {
                enqueueSnackbar("This Booking is already mapped to the selected advisor",{ variant: 'error', autoHideDuration: 2000, });
                setisValidatedRequest(false);
                return;
        }
        

        if(bookingID !== 0 && SelectedAgent !== 0 && SelectedAgentType !== '' && SelectedReason !== '' 
            && Comments !== '' && Comments.length >= 10 && Comments.length<= 400)
        {
                setNewRequestData({ ...NewRequestData,RequestorRemarks : Comments.trim()});
                setisValidatedRequest(true);
                return;
        }
        setisValidatedRequest(false);
    }
    const ValidateSubmitRequestTermInv = () => {
        if(isNaN(ReferenceId)){
            enqueueSnackbar("Please enter a valid Reference Id.",{ variant: 'error', autoHideDuration: 2000, });
            setisValidatedRequest(false);
            return;
        }
        else if(BookingDate != 0 && SelectedAgentType!='' && isBookingValidTermInv(BookingDate,SelectedAgentType) == true)
        {
            let ThresholdDays = 3;
            if(ProductSelect == 7){
                ThresholdDays = 10;
                if([2].includes(SelectedAgentType)){
                    // ThresholdDays = 60;
                }
            }
            
            if([145,146,147,148].includes(SelectedReason)){
                ThresholdDays = 60;
            }
            enqueueSnackbar(`Credit change request cannot be created for bookings older than ${ThresholdDays} days`, { variant: 'error', autoHideDuration: 2000, });
            return;
        }
        else if(!IsUserEligible && SelectedAgentType > 0 && EligibiltyMessage && Comments.length > 0){
            setisValidatedRequest(false);
            enqueueSnackbar(EligibiltyMessage, { variant: 'error', autoHideDuration: 3000, });
            return;
        }
        else if(SelectedAgentType == 2 && IsOldFosAgent == false){
            enqueueSnackbar('Request for the "Secondary Advisor" cannot be raised because the primary booking is not mapped to the FOS advisor.', { variant: 'error', autoHideDuration: 2000, });
            setisValidatedRequest(false);
            return;
        }
        else if((BookingAgentUserID == SelectedAgent && (SelectedAgentType == 1 || SelectedAgentType == 3 )) || (SecondaryAgentUserID == SelectedAgent && SelectedAgentType == 2 ))
        {
                enqueueSnackbar("This Booking is already mapped to the selected advisor",{ variant: 'error', autoHideDuration: 2000, });
                setisValidatedRequest(false);
                return;
        }
        

        if(bookingID !== 0 && SelectedAgent !== 0 && SelectedAgentType !== '' && SelectedReason !== '' 
            && Comments !== '' && Comments.length >= 10 && Comments.length<= 400)
        {
                setNewRequestData({ ...NewRequestData,RequestorRemarks : Comments.trim()});
                setisValidatedRequest(true);
                return;
        }
        setisValidatedRequest(false);
    }
    const SubmitRequest = () => {
        if (isValidatedRequest) {
            var Data = NewRequestData;
            CreateCreditChangeRequest(Data).then((res) => {
                if (res && res.status == true) {
                    enqueueSnackbar(res.message, { variant: 'success', autoHideDuration: 2000, });
                    onClose();
                }
                else if(res && res.status != true){
                    enqueueSnackbar(res.message, { variant: 'error', autoHideDuration: 2000, });
                    onClose();
                }
                else{
                    enqueueSnackbar("Issue creating Credit Change Request, please try again", { variant: 'error', autoHideDuration: 2000, });
                    onClose();
                }
            }).catch((error) => {
                console.log("error in SaveCoreAddressUsageService");
                enqueueSnackbar("There is some error occured while creating the request", { variant: 'error', autoHideDuration: 2000, });
                onClose();
            })
        }
    }
    return (

        <Dialog
            open={open}
            onClose={onClose}
            className="CreateCreditChangeRequestPopup"
        >
            <DialogTitle>Credit change request</DialogTitle>
            <IconButton
                aria-label="close"
                onClick={onClose}
                sx={{
                    position: 'absolute',
                    right: 8,
                    top: 8,
                    color: (theme) => theme.palette.grey[500],
                }}
            >
                <CloseIcon />
            </IconButton>
            <DialogContent>
                <Grid container spacing={2}>
                    <Grid item sm={5} md={5} xs={12} >
                        <label>Booking ID</label>
                        <TextInput
                            name="BookingID"
                            sm={12} md={12} xs={12}
                            placeholder="Enter Booking ID"
                            value={bookingID}
                            onChange={handleChange}
                            InputProps={{
                                endAdornment: (
                                    <InputAdornment position="end">
                                        <Button onClick={FetchBookingDetails}>
                                            <img src={CONFIG.PUBLIC_URL + "/images/salesview/fetch.svg"} alt="Fetch" />&nbsp;Fetch
                                        </Button>
                                    </InputAdornment>
                                ),
                            }}

                        />

                        <label>Reference Lead ID (Optional)</label>
                        <TextInput
                            name="ReferenceId"
                            sm={12} md={12} xs={12}
                            placeholder="Enter Reference ID (Optional)"
                            value={ReferenceId}
                            onChange={handleChange}
                            InputProps={{
                                endAdornment: (
                                    <InputAdornment position="end">
                                        <Button onClick={ValidateReferenceLead}>
                                            <img src={CONFIG.PUBLIC_URL + "/images/salesview/fetch.svg"} alt="Fetch" />&nbsp;Validate
                                        </Button>
                                    </InputAdornment>
                                ),
                            }}
                        />

                        <label>New advisor</label>
                        <p>Search for the advisor you want to assign this booking to</p>
                        <Grid container spacing={2}>
                            <Grid item sm={12} md={12} xs={12} >
                            <Autocomplete
                                onChange={(event, value) => handleChange({ target: { name: 'NewAdvisor', value } })}
                                options={AgentMapping}
                                name="NewAdvisor"
                                value={SelectedAgentName}
                                sm={12} md={12} xs={12}
                                disabled={Selectdropwnstate}
                                renderInput={(params) =>
                                    <TextField {...params}
                                        variant='outlined' 
                                        />}
                            />
                            </Grid>
                        </Grid>
                        <Grid container spacing={2}>
                            <Grid item sm={6} md={6} xs={12} >
                                <label>Agent Type</label>
                                <SelectDropdown
                                    name="AgentType"
                                    placeholder="Select"
                                    options={AgentTypeListMapping}
                                    labelKeyInOptions='label'
                                    valueKeyInOptions='value'
                                    handleChange={handleChange}
                                    value={SelectedAgentType}
                                    sm={12} md={12} xs={12}
                                    disabled={SelectAgentTypedropwnstate}
                                />
                            </Grid>
                            <Grid item sm={6} md={6} xs={12} >
                                <label>Reason for change</label>
                                <SelectDropdown
                                    name="reason"
                                    placeholder="Select"
                                    options={FilteredReasonList}
                                    handleChange={handleChange}
                                    labelKeyInOptions='Reason'
                                    valueKeyInOptions='ReasonID'
                                    value={SelectedReason}
                                    sm={12} md={12} xs={12}
                                    disabled={SelectAgentTypedropwnstate}
                                />
                            </Grid>
                        </Grid>
                        <label>Remarks</label>
                        <Textarea
                            name="Remarks"
                            minRows={3}
                            placeholder="Enter Remarks here(10-400 characters)…"
                            onChange={handleChange}
                            value={Comments}
                            disabled={SelectReasondropwnstate}
                        />
                    </Grid>
                    <Grid item sm={7} md={7} xs={12}>
                     <BookingDetails data= {bookingData} />
                    </Grid>
                </Grid>

            </DialogContent>

            <DialogActions>
                 <p>All fields are mandatory for creating a ticket</p>           
                <Button className={isValidatedRequest && (Comments && Comments != '' && Comments.trim().length > 9 && Comments.trim().length < 400) ? "CreateBtn" : "disableBtn"}
                onClick={() => { SubmitRequest() }}
                disabled={!(isValidatedRequest && (Comments && Comments != '' && Comments.trim().length > 9 && Comments.trim().length < 400))}>
                Create Request</Button>
            </DialogActions>
        </Dialog>

    );
}