import React,{useEffect, useState} from "react";
import ModalPopup from "../../../components/Dialogs/ModalPopup";
import { Typography, Box } from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";
import { IconButton } from "@mui/material";
import { CONFIG } from "../../../appconfig";

const MaturityOverCustomerPopup = (props) => {
    let lead = props.lead;

    const [leadDetails, setleadDetails] = useState({
            InsurerId:0,
            InsurerImageURL:'',
            TotalInvestment:0,
            AbsoluteReturn:'',
            PolicyStatus:''
        });

    useEffect(()=>{
        if(props.open == true){
            if(lead && lead.UTM_Medium && lead.Utm_term){
                let MediumParts = lead.UTM_Medium.split(":");
                let TermParts = lead.Utm_term.split(":");
                let SourceParts = lead.Utm_source.split('_');
                if(MediumParts && Array.isArray(MediumParts) && MediumParts.length > 2 && TermParts && Array.isArray(TermParts) && TermParts.length > 1 && SourceParts && Array.isArray(SourceParts) && SourceParts.length > 3)
                {
                    
                let SubMediumParts = MediumParts[1].split("&");
                    let insurerid = SubMediumParts[0].trim();
                    let url = '';
                    
                    switch(insurerid){
                        case "3":
                            url = `${CONFIG.PUBLIC_URL}/images/salesview/Insurericon/Bajaj_Allianz.png`;
                            break;
                        case "4":
                            url = `${CONFIG.PUBLIC_URL}/images/salesview/Insurericon/Aditya_Birla.png`;
                            break;
                        case "6":
                            url = `${CONFIG.PUBLIC_URL}/images/salesview/Insurericon/HDFC_ERGO.png`;
                            break;
                        case "7":
                            url = `${CONFIG.PUBLIC_URL}/images/salesview/Insurericon/ICICI_Lombard.png`;
                            break;
                        case "9":
                            url = `${CONFIG.PUBLIC_URL}/images/salesview/Insurericon/Kotak_General_Insurance.png`;
                            break;
                        case "10":
                            url = `${CONFIG.PUBLIC_URL}/images/salesview/LICLogo.svg`;
                            break;
                        case "11":
                            // url = `${CONFIG.PUBLIC_URL}`;
                            break;
                        case "12":
                            // url = `${CONFIG.PUBLIC_URL}`;
                            break;
                        case "14":
                            url = `${CONFIG.PUBLIC_URL}/images/salesview/Insurericon/SBI.png`;
                            break;
                        case "15":
                            url = `${CONFIG.PUBLIC_URL}/images/salesview/TATA_AIA 1.svg`;
                            break;
                        case "16":
                            // url = `${CONFIG.PUBLIC_URL}`;
                            break;                      
                    }

                    if(url != '' && MediumParts[2] > 0 && TermParts[1] != '' && SourceParts[2] != ''){
                        setleadDetails({...leadDetails,
                        InsurerId : insurerid,
                        InsurerImageURL:url,
                        TotalInvestment:MediumParts[2],
                        AbsoluteReturn:TermParts[1],
                        PolicyStatus:SourceParts[2] + " Over"
                    });
                    }
                    else{
                        props.handleClose()
                    }
                }
                else{
                    props.handleClose()
                }
            }
            else{
                props.handleClose();
            }
        }
    },[props.open]);

    return (
        <ModalPopup open={props.open} handleClose={props.handleClose} className="MaturityOverCustomerPopup">
            <Box className="maturity-popup-container">
                {/* Close Icon */}
                 <IconButton onClick={props.handleClose} size="large">
                    <CloseIcon />
                </IconButton>
                {/* Logo and Title */}
                <Box className="maturity-header">

                    <img src={leadDetails.InsurerImageURL} alt="TATA AIA" className="maturity-logo" />
                    {/* <Typography className="maturity-title">TATA AIA i SIP</Typography> */}
                </Box>
                {/* Info Cards */}
                <Box className="maturity-info-row">
                    <Box className="maturity-info-card">

                        <img
                            src={`${CONFIG.PUBLIC_URL}/images/salesview/total-Inv.svg`}
                            alt="Total Investment"
                            className="maturity-Icon"
                        />

                        <Box className="maturity-info-right">
                            <Typography className="maturity-info-label">Total Investment</Typography>
                            <Typography className="maturity-info-value">₹{leadDetails.TotalInvestment}</Typography>
                        </Box>
                    </Box>
                    <Box className="maturity-info-card">

                        <img src={`${CONFIG.PUBLIC_URL}/images/salesview/return.svg`} alt="Absolute Return" className="maturity-Icon" />

                        <Box className="maturity-info-right">
                            <Typography className="maturity-info-label">Absolute Return</Typography>
                            <Typography className="maturity-info-value">{leadDetails.AbsoluteReturn}</Typography>
                        </Box>
                    </Box>
                    <Box className="maturity-info-card">

                        <img src={`${CONFIG.PUBLIC_URL}/images/salesview/policy.svg`} alt="Policy Status" className="maturity-Icon" />

                        <Box className="maturity-info-right">
                            <Typography className="maturity-info-label">Policy Status</Typography>
                            <Typography className="maturity-info-value ">{leadDetails.PolicyStatus}</Typography>
                        </Box>
                    </Box>


                </Box>
                {/* CTA Banner */}
                <Box className="maturity-cta-banner">
                    Time to reinvest- Don't let your customer's money sit idle
                </Box>
            </Box>
        </ModalPopup>
    );
};

export default MaturityOverCustomerPopup;