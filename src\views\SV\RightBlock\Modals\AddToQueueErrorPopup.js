import React, { use<PERSON>allback, useEffect, useState } from "react";
import { Grid, Icon<PERSON>utton } from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";
import { useDispatch, useSelector } from "react-redux";
import ModalPopup from "../../../../components/Dialogs/ModalPopup";
import { updateStateInRedux } from "../../../../store/actions";
import { CONFIG, SV_CONFIG } from "../../../../appconfig";
import { useSnackbar } from "notistack";
import { checkUserGroup } from "../../../../services/Common";


const AddToQErrorList = [
    {
        ErrorIdentifier: "unanswered calls",
        Title: "Unanswered Limit",
        Img: CONFIG.PUBLIC_URL + "/images/salesview/callRestrictions/maxCallAttempt.png"
    },
    {
        ErrorIdentifier: "2 unanswered attempts within 30 minutes",
        Title: "Retry Later",
        Img: CONFIG.PUBLIC_URL + "/images/salesview/callRestrictions/maxCallAttempt.png"
    },
    {
        ErrorIdentifier: "Do Not Call",
        Title: "DO NOT CALL",
        Img: CONFIG.PUBLIC_URL + "/images/salesview/callRestrictions/callRestrictions.png"
    },
    {
        ErrorIdentifier: "manual attempts",
        Title: "Manual Call Limit",
        Img: CONFIG.PUBLIC_URL + "/images/salesview/callRestrictions/manualAttempt.png"
    }
]


export const AddToQueueErrorPopup = () => {
    const ErrorData = useSelector(state => state.salesview.AddToQueueError);

    const { enqueueSnackbar } = useSnackbar()
    const [open, setOpen] = useState(false);
    const [data, setData] = useState({
        ErrorIdentifier: "",
        Title: "",
        Message: "",
        Img: ""
    })
    const dispatch = useDispatch();

    const handleClose = useCallback(() => {
        setOpen(false);
        dispatch(updateStateInRedux({ key: 'AddToQueueError', value: null }))
    }, [dispatch])

    useEffect(() => {
        if (ErrorData) {

            let ShowPopup = false;
            if (ErrorData && ErrorData.message && ErrorData.message != ''
                && !SV_CONFIG.disableAddToQErr
                && (SV_CONFIG.showAddToQErrAll || checkUserGroup(SV_CONFIG.AddToQErrorGrps))
            ) {
                for (let i = 0; i < AddToQErrorList.length; i++) {
                    let err = AddToQErrorList[i];
                    if (ErrorData.message.includes(err.ErrorIdentifier)) {
                        setData({ ...err, "Message": ErrorData.message, LeadID: ErrorData.LeadID })
                        setOpen(true);
                        ShowPopup = true;
                        break;
                    }
                }
            }

            if (!ShowPopup) {

                let error = (ErrorData && ErrorData.message && ErrorData.message != '')
                    ? ErrorData.LeadID + " : " + ErrorData.message
                    : "Error while adding lead: " + ErrorData.LeadID;

                enqueueSnackbar(error, { variant: 'error', autoHideDuration: 2000, });
            }
            setTimeout(() => {
                handleClose();
            }, 10 * 1000);
        }
        else {
            setOpen(false);
        }

    }, [ErrorData, dispatch, enqueueSnackbar, handleClose])

    return (
        <ModalPopup className="AddToQueueErrorPopup" open={open} handleClose={handleClose}>
            {/* <div className="popupWrapper"> */}

            <IconButton onClick={handleClose} size="large">
                <CloseIcon />
            </IconButton>

            <Grid container spacing={1}>
                <Grid item md={8} sm={8} xs={12}>

                    <h2>{data.Title}</h2>
                    <p>{data.Message}</p>
                    <p className="LeadId">{data.LeadID ? <> LeadId: {data.LeadID}</> : null}</p>


                </Grid>
                <Grid item md={4} sm={4} xs={12}>
                    <img src={data.Img} alt={data.Title} />

                </Grid>
            </Grid>
            {/* </div> */}
        </ModalPopup>
    );
};
