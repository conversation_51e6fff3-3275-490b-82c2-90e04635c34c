
import { LinearProgress, Paper, Table, TableBody, TableCell, TableContainer, TableHead, TableRow } from "@mui/material";
import React from "react";
import ModalPopup from "../../../../../../components/Dialogs/ModalPopup";
import { JsonToNormalDate } from "../../../../../../utils/utility";
import dayjs from "dayjs";
import { CALL_API } from '../../../../../../services';
import User from '../../../../../../services/user.service';
import { useSnackbar } from "notistack";
import { CSVLink } from "react-csv";

export const RejectedLeads = (props) => {
    let rows = props.data !== undefined ? props.data : [];
    const { enqueueSnackbar } = useSnackbar();

    const handleCellClick = (leadId) => {
        try
        {
            GetNewSvUrl(leadId, User.UserId).then((result) => {
                if (result)
                {
                    if(result.StatusCode == 200 && result.URL)
                    {
                        const split = result.URL.split('/');
                        const baseUrl = "/pgv/newsv/leadview/";
                        const leadviewURL  = baseUrl+split[split.length-1];
                        window.open(leadviewURL);
                    }
                    else
                    {
                        enqueueSnackbar("Lead view URL is not found", { variant: 'error', autoHideDuration: 3000, });
                    }
                }
            });
        }
        catch(ex)
        {
            console.log(ex.message);
        }
    };

    const GetNewSvUrl = (leadId, userId) => {
        const input = {
            url: `api/Bms/GetNewSVURL/${leadId}/${userId}/SalesView`,
            method: 'GET', service: 'MatrixCoreAPI',
        };
        return CALL_API(input);
    };
    const filterColumns = (data) => {
        // Get column names
        //const columns = Object.keys(data[0]);
       
        let headers = [];
        headers.push({ label: "LeadId", key: 'LeadID' });
        headers.push({ label: "Customer Name", key: 'Name' });
        headers.push({ label: "Status", key: 'StatusName' });
        headers.push({ label: "Created On", key: 'CreatedOn' });
        headers.push({ label: "Last Connected Date", key: 'LastCallDate' });
        headers.push({ label: "Rejection Date", key: 'RejectedOn' });
        headers.push({ label: "Total Attempts", key: 'Attempts' });
        headers.push({ label: "Rejected By", key: 'RejectedBy' });
        // columns.forEach((col, idx) => {
        //     // OR if (idx !== 0)
        //     headers.push({ label: camelCase(col), key: col });
        // });

    return headers;
    };
    const filterRows = (data) => {
        let updatedData = JSON.parse(JSON.stringify(data));
        updatedData.forEach((row, idx) => {
            if(row.CreatedOn !== undefined) 
            {
                row.CreatedOn = dayjs(row.CreatedOn).format('DD/MM/YYYY h:mm a');
            }
            if(row.LastCallDate !== undefined) 
            {
                row.LastCallDate = dayjs(row.LastCallDate).format('DD/MM/YYYY h:mm a');
            }
            if(row.RejectedOn !== undefined) 
            {
                row.RejectedOn = dayjs(row.RejectedOn).format('DD/MM/YYYY h:mm a');
            }
        });

    return updatedData;
    };

    return (
        <ModalPopup open={props.open} title='Rejected Leads' handleClose={props.handleClose} className="addmoreLeadPopup">          
                {(props.rejectedLeadloader) ? 
                    <LinearProgress color="secondary" />
                    :
                (rows.length === 0) ? <h3 className="nodataFound">No Data Found</h3> :
                <div>
                <CSVLink data={filterRows(rows)} headers={filterColumns(rows)} filename={"RejectedLeads.xlsx"} class="excelexport">
                Export to Excel
                </CSVLink>
                    <TableContainer component={Paper} className="reassigned-table">
                        <Table aria-label="simple table" >
                            <TableHead>
                                <TableRow>
                                    <TableCell align="left">Sno</TableCell>
                                    <TableCell align="left">LeadId</TableCell>
                                    <TableCell align="left">Customer Name</TableCell>
                                    <TableCell align="left">Status </TableCell>
                                    <TableCell align="left">Created On</TableCell>
                                    <TableCell align="left">Last Connected Date</TableCell>
                                    <TableCell align="left">Rejection Date</TableCell>
                                    <TableCell align="left">Total Attempts</TableCell>
                                    <TableCell align="left">Rejected By</TableCell>
                                </TableRow>
                            </TableHead>
                            <TableBody>
                                {rows.map((row, i) => (
                                    <TableRow key={i} className="moreDeatilsData">
                                        <TableCell align="left">{i + 1}</TableCell>
                                        <TableCell align="left"><a onClick={() => {handleCellClick(row.LeadID)}}>{row.LeadID !== undefined ? row.LeadID : ""}</a></TableCell>
                                        <TableCell align="left">{row.Name ? row.Name : ""}</TableCell>
                                        <TableCell align="left">{row.StatusName ? row.StatusName : ""}</TableCell>
                                        <TableCell align="left">
                                            {row.CreatedOn !== undefined ?
                                                dayjs(row.CreatedOn).format('DD/MM/YYYY h:mm a') : ""}
                                        </TableCell>
                                        <TableCell align="left">
                                            {row.LastCallDate !== undefined ?
                                                dayjs(row.LastCallDate).format('DD/MM/YYYY h:mm a') : ""}
                                        </TableCell>
                                        <TableCell align="left">
                                            {row.RejectedOn !== undefined ?
                                                dayjs(row.RejectedOn).format('DD/MM/YYYY h:mm a') : ""}
                                        </TableCell>
                                        <TableCell align="left">{row.Attempts ? row.Attempts : ""}</TableCell>
                                        <TableCell align="left">{row.RejectedBy ? row.RejectedBy : ""}</TableCell>
                                    </TableRow>
                                ))}
                            </TableBody>
                        </Table>
                    </TableContainer>
                </div>
            }
        </ModalPopup>
    )
};




