import ExpandMore from '@mui/icons-material/ExpandMore';
import React, { useEffect, useState } from "react";
import { FormControlLabel, Button, Grid, TextareaAutosize, TextField, Radio, RadioGroup, Tooltip } from "@mui/material";
import rootScopeService from '../../../services/rootScopeService';
import masterService from '../../../services/masterService';
import User from '../../../services/user.service';
import { useSelector } from 'react-redux';
import { CALL_API } from '../../../services';
import { SelectDropdown } from '../../../components';
import { useSnackbar } from 'notistack';
import { GetCallDetails } from '../../../services/Common';
import { FOSpopUp } from '../RightBlock/Modals/FOSpopUp';
import { getCurrentTalkTime } from '../../../services/Common';
import { IsLeadDataSetActive } from '../../../services/Common';
import InfoIcon from '@mui/icons-material/Info';
import { useDispatch } from "react-redux";
import { updateStateInRedux } from "../../../store/actions/SalesView/SalesView";

const Monthlist = [
    { "ID": 0, Name: "Month" }, { "ID": 1, Name: "January" }, { "ID": 2, Name: "February" },
    { "ID": 3, Name: "March" }, { "ID": 4, Name: "April" }, { "ID": 5, Name: "May" },
    { "ID": 6, Name: "June" }, { "ID": 7, Name: "July" }, { "ID": 8, Name: "August" },
    { "ID": 9, Name: "September" }, { "ID": 10, Name: "October" }, { "ID": 11, Name: "November" },
    { "ID": 12, Name: "December" }
]
var dateObj = new Date();
var month = dateObj.getUTCMonth();
var day = dateObj.getUTCDate();
var year = dateObj.getUTCFullYear();

const CrossSellAppointmentProducts = [2, 7, 115, 1000];
var travelDefaultStartDate = new Date();
travelDefaultStartDate.setDate(dateObj.getDate() + 10);

export default function CrossSell(props) {
    let [RefreshLead, IsRenewal] = useSelector(({ salesview }) => [salesview.RefreshLead, salesview.IsRenewal]);
    const [show, setShow] = useState(props.isModal ? true : false);
    const [CrossSellProducts, setCrossSellProducts] = useState([]);
    const [annualIncomeList, setAnnualIncomeList] = useState([]);
    const [makeList, setMakeList] = useState([]);
    const [subProductList, setSubProductList] = useState([]);
    const [modelList, setModelList] = useState([]);
    const [startYear, setStartYear] = useState([]);
    const [startDay, setStartDay] = useState([]);
    const [endYear, setEndYear] = useState([]);
    const [endDay, setEndDay] = useState([]);
    const [isSaveVisible, setIsSaveVisible] = useState(true);
    const [showIncome, setShowIncome] = useState(false);
    const [showMakeModel, setShowMakeModel] = useState(false);
    const [showexpirymodel, setshowexpirymodel] = useState(false);
    const { enqueueSnackbar } = useSnackbar();
    const [AppointmentCreateEligibility, setAppointmentCreateEligibility] = useState(false);
    const [FOSPopupOpen, setFOSPopupOpen] = useState(false);
    const [FOSLeadId, setFOSLeadId] = useState(null);
    const [FOSProductId, setFOSProductId] = useState(null);
    const [ShowInfoIcon, setShowInfoIcon] = useState(false);
    const reduxDispatch = useDispatch();

    let [AllLeads, parentLeadId] = useSelector(state => {
        let { allLeads, parentLeadId } = state.salesview;
        return [allLeads, parentLeadId]
    });
    //const parentLeadId = useSelector(state => state.salesview.parentLeadId)
    const [isHealthRenewal, setIsHealthRenewal] = useState(0);
    const [AppointmentCreation, setAppointmentCreation] = useState('')
    //Inputs State
    let [inputs, setInputs] = useState({
        product: "", comment: "", travelType: "",
        noOfMembers: 0, sumAssured: "", subProductId: '',
        annualIncome: '', makeId: '', modelId: '',
        startYear: '', startMonth: (travelDefaultStartDate.getUTCMonth() + 1), startDay: travelDefaultStartDate.getUTCDate(),
        endYear: '', endMonth: 0, endDay: 0,
        propertyType: "", policyExpiryDay: "", expiryMonth: 0,

    })

    const options = {
        travelType: [{ label: 'Individual', value: 1 }, { label: 'Family', value: 2 }, { label: 'Student', value: 3 }],
        noOfMembers: [{ label: 'Select', value: 0 }, { label: 'one', value: 1 }, { label: 'More than One', value: 2 }],
        sumAssured: [
            { label: '10 lakhs', value: 1000000 }, { label: '20 lakhs', value: 2000000 }, { label: '30 lakhs', value: 3000000 },
            { label: '40 lakhs', value: 4000000 }, { label: '50 lakhs', value: 5000000 }, { label: '75 lakhs', value: 7500000 },
            { label: '1 crore', value: 10000000 }, { label: '2 crore', value: 20000000 },
            { label: '5 crore', value: 50000000 }, { label: '10 crore', value: ********* },
        ],
        propertyType: [{ label: 'Rented', value: 1 }, { label: 'owned', value: 2 }]
    }

    //insert empty input
    if (annualIncomeList[0] && annualIncomeList[0].AnnualIncomevalue != 0) {
        setAnnualIncomeList([{ "AnnualIncomeName": "Select", "AnnualIncomevalue": 0 }, ...annualIncomeList]);
        setInputs({ ...inputs, annualIncome: 0 });
    }
    if (makeList && makeList[0] && makeList[0].ID != "0") {
        setMakeList([{ "Name": "Select", "ID": "0" }, ...makeList]);
        setInputs({ ...inputs, makeId: 0 });
    }
    if (subProductList[0] && subProductList[0].ID != "0") {
        setSubProductList([{ "Name": "Select", "ID": "0" }, ...subProductList]);
        setInputs({ ...inputs, subProductId: 0 });
    }
    ///

    const BindTravelStartEndDate = () => {

        /*set start year */
        setStartYear([{ "ID": 0, Name: "Select" }, { "ID": year, Name: year }, { "ID": (year + 1), Name: (year + 1) }]);
        BindDayddl('StartDay');

        /*Set End year*/
        setEndYear([{ "ID": 0, Name: "Select" }, { "ID": year, Name: year }, { "ID": (year + 1), Name: (year + 1) }, { "ID": (year + 2), Name: (year + 2) }])
        BindDayddl('EndDay');
    };

    const BindDayddl = function (daytype) {
        let month, year;
        if (daytype == "StartDay") {
            month = inputs.startMonth;
            year = inputs.startYear;
        }
        else {
            month = inputs.endMonth;
            year = inputs.endYear;
        }
        var dayUpperLimit = 30;
        if (month == 1 || month == 3 || month == 5 || month == 7 || month == 8 || month == 10 || month == 12) {
            dayUpperLimit = 31;
        }
        else if (month == 2) {
            if (year % 4 != 0) {
                dayUpperLimit = 28;
            }
            else {
                dayUpperLimit = 29;
            }
        }

        if (daytype == "StartDay") {
            let dayArray = [];
            dayArray.push({ "ID": 0, Name: "Day" });
            for (var i = 1; i <= dayUpperLimit; i++) {
                dayArray.push({ "ID": i, Name: i });
            }
            setStartDay(dayArray);

            if (inputs.startDay > dayUpperLimit) {
                setInputs({ ...inputs, startDay: 0 })
            }
        }

        if (daytype == "EndDay") {
            let dayArray = [];
            dayArray.push({ "ID": 0, Name: "Day" });
            for (var i = 1; i <= dayUpperLimit; i++) {
                dayArray.push({ "ID": i, Name: i });
            }
            setEndDay(dayArray);

            if (inputs.endDay > dayUpperLimit) {
                setInputs({ ...inputs, endDay: 0 })
            }
        }

        return false;
    };

    const getProductList = async () => {

        try {
            let ProductId = rootScopeService.getProductId();
            let { RoleId } = User;
            let result = await masterService.getCrossSellProducts(ProductId, RoleId)
            result = [{ "ProductName": "Select ", "ProductId": 0 }, ...result]

            if(props && props.isModal && result){
                result = result.filter(item => item.ProductId != ProductId);
            }

            setCrossSellProducts(result);

            if (IsRenewal == 1 && rootScopeService.getProductId() == 2) {
                setIsHealthRenewal(1);
                // result.push({ "ProductName": "Health", "ProductId": 2 });
                setCrossSellProducts(result);
                //CrossSellProducts.push({ "ProductName": "Health", "ProductId": 2 });
            }
            else {
                setIsHealthRenewal(0);
            }

            console.log(CrossSellProducts);

        }
        catch (err) {
            //
        }
    }

    const getAppointmentCreateEligibility = () => {
        let IsActive = IsLeadDataSetActive(AllLeads);

        if (IsActive) {
            if (IsBookedLeadStatus()) {
                setAppointmentCreateEligibility(true);
            }
            else {
                getTotalTalktime();
            }
        }

    }

    const onProductChange = () => {
        setShowIncome(false);
        setShowMakeModel(false);
        setshowexpirymodel(false);
        setAppointmentCreation('');
        setFOSPopupOpen(false);
        setFOSLeadId(null);
        setFOSProductId(null);
        setShowInfoIcon(false);
        if ([2, 7, 106, 115, 116, 118, 130].indexOf(inputs.product) != -1) {
            setShowIncome(true);
            masterService.getAnnualIncomeListByPrdId(inputs.product).then(function (annualIncomes) {
                setAnnualIncomeList(annualIncomes);
            });
        }
        if ([117].indexOf(inputs.product) != -1) {
            setShowMakeModel(true);
            setshowexpirymodel(true);
            setModelList([]);
            masterService.getVehicalMakeListByProduct(inputs.product).then(function (MakeList) {
                setMakeList(MakeList);
            });
        }
        if ([114].indexOf(inputs.product) != -1) {
            setShowMakeModel(true);
            setshowexpirymodel(false);
            setModelList([]);
            masterService.getVehicalMakeListByProduct(inputs.product).then(function (MakeList) {
                setMakeList(MakeList);
            });
        }
        if (inputs.product == 115) {
            masterService.getSubProductType(inputs.product).then((SubProduct) => {
                setSubProductList(SubProduct);
            })
        }
        if (inputs.product == 131) {
            setShowInfoIcon(true);
            const idsToKeep = new Set([1, 5, 13, 14, 15, 16, 19, 12, 21, 20]);
            masterService.getSubProductByProductID(inputs.product).then(res => {
                const filteredProducts = res?.filter(e => idsToKeep.has(e.ID));
                setSubProductList(filteredProducts);
            });
        }
        if (inputs.product == 3) {
            BindTravelStartEndDate();
        }
        if (CrossSellAppointmentProducts.indexOf(inputs.product) > -1) {
            // let res =
            getAppointmentCreateEligibility();
            //setAppointmentCreateEligibility(res);
        }
    }

    const onMakeChange = () => {
        masterService.getMakeModelListByProduct(inputs.product, inputs.makeId)
            .then((ModelArr) => {
                setModelList([{ "Name": "Select", "ID": "0" }, ...ModelArr]);
                setInputs({ ...inputs, modelId: 0 })
                setShowInfoIcon(false);
            })
    }

    const CancelCrossSell = () => {
        setIsSaveVisible(true);
        setInputs({ ...inputs, comment: '', product: '' });
        setShowMakeModel(false);
        setshowexpirymodel(false);
        setShowIncome(false);
        setShowInfoIcon(false);
    };

    const ValidateCrossSell = () => {
        var ret = true;
        var alertMsg = '';
        var prdId = inputs.product;
        if (prdId > 0) {

            if (prdId == "131") {
                if (inputs.subProductId == "0") {
                    alertMsg = alertMsg + "Please select Sub-Product" + "\n";
                }
            }
            if (prdId == "115") {
                if (inputs.subProductId == "0") {
                    alertMsg = alertMsg + "Please select Product Type" + "\n";
                }
            }
            else if (prdId == 3) {
                if (inputs.startDay == 0) {
                    alertMsg = alertMsg + "-> Enter Start Date - Day" + "\n";
                }
                if (inputs.startMonth == "0") {
                    alertMsg = alertMsg + "-> Enter Start Date - Month" + "\n";
                }
                if (inputs.startYear == "0" || inputs.startYear == "") {
                    alertMsg = alertMsg + "-> Enter Start Date - Year" + "\n";
                }
                if (inputs.endDay == "0") {
                    alertMsg = alertMsg + "-> Enter End Date - Day" + "\n";
                }
                if (inputs.endMonth == "0") {
                    alertMsg = alertMsg + "-> Enter End Date - Month" + "\n";
                }
                if (inputs.endYear == "0" || inputs.endYear == "") {
                    alertMsg = alertMsg + "-> Enter End Date - Year" + "\n";
                }
                var StartDate = Date.UTC(inputs.startYear, (inputs.startMonth - 1), inputs.startDay, 0, 0);
                var EndDate = Date.UTC(inputs.endYear, (inputs.endMonth - 1), inputs.endDay, 0, 0);
                if (StartDate > EndDate && EndDate > 0 && StartDate > 0) {
                    alertMsg = alertMsg + "-> Travel End Date cannot be less than Start Date" + "\n";
                }
            }
            else if (prdId == 117) {//Motor
                if (inputs.modelId == 0 || inputs.makeId == 0) {
                    alertMsg = alertMsg + "-> Select Make & Model" + "\n";
                }
                if (inputs.expiryMonth == 0) {
                    alertMsg = alertMsg + "-> Select Prev. Policy Expiry Month" + "\n";
                }
                /*Expiry check*/
                var localdate = new Date();
                var policyExpiryDay = "1";  //Set default 1.
                var policyExpiryMonth = parseInt(inputs.expiryMonth);
                localdate = new Date(localdate.getFullYear(), localdate.getMonth(), localdate.getDate(), 23);
                if (inputs.policyExpiryDay != "") {
                    var numericReg = /^\d*[0-9](|.\d*[0-9]|,\d*[0-9])?$/;
                    if (!numericReg.test(inputs.policyExpiryDay)) {
                        alertMsg = alertMsg + "-> Enter Valid Day" + "\n";
                    }
                    else {
                        policyExpiryDay = parseInt(inputs.policyExpiryDay, 10);
                        if ((policyExpiryMonth == 1 || policyExpiryMonth == 3 || policyExpiryMonth == 5 || policyExpiryMonth == 7 || policyExpiryMonth == 8 || policyExpiryMonth == 10 || policyExpiryMonth == 12) && (policyExpiryDay < 1 || policyExpiryDay > 31)) {
                            alertMsg = alertMsg + "->Enter valid Day  1 to 31 " + "\n";
                        }
                        else if (policyExpiryMonth == 2) {
                            if (localdate.getFullYear() % 4 != 0 && (policyExpiryDay < 1 || policyExpiryDay > 28)) {
                                alertMsg = alertMsg + "->Enter valid Day  1 to 28 " + "\n";
                            }
                            else if (policyExpiryDay < 1 || policyExpiryDay > 29) {
                                alertMsg = alertMsg + "->Enter valid Day  1 to 29 " + "\n";
                            }
                        }
                        else if ((policyExpiryMonth == 2 || policyExpiryMonth == 4 || policyExpiryMonth == 6 || policyExpiryMonth == 9 || policyExpiryMonth == 11) && (policyExpiryDay < 1 || policyExpiryDay > 30)) {
                            alertMsg = alertMsg + "->Enter valid Day  1 to 30 " + "\n";
                        }
                    }
                }
                /*Expiry check*/
            }
            else if (prdId == 114) {//Two-wheeler
                if (inputs.modelId == 0 || inputs.makeId == 0) {
                    alertMsg = alertMsg + "-> Select Make & Model" + "\n";
                }
            }
            if (AppointmentCreation == "" && (CrossSellAppointmentProducts.indexOf(inputs.product) > -1) && AppointmentCreateEligibility) {
                alertMsg = alertMsg + "Please select Yes or No if you want to create an appointment or not"
            }
        }
        else {
            alertMsg = " Please Select Product";
        }
        if (alertMsg != "") {
            ret = false;
            alert(alertMsg);
        }
        return ret;
    };

    const handleAppointmentChange = (e) => {
        setAppointmentCreation(e.target.value);
        setFOSPopupOpen(false);
    }

    const getPolicyExpiryDate = () => {
        let prevPolicyExpiryDate = "";
        let localdate = new Date();
        let policyExpiryDay = "1";  //Set default 1.
        let policyExpiryMonth = parseInt(inputs.expiryMonth);
        localdate = new Date(localdate.getFullYear(), localdate.getMonth(), localdate.getDate(), 23);

        if (inputs.policyExpiryDay != "") {
            policyExpiryDay = inputs.policyExpiryDay;
        }

        if ((policyExpiryMonth - 1) == localdate.getMonth()) {
            prevPolicyExpiryDate = new Date(localdate.getFullYear(), localdate.getMonth(), localdate.getDate() + 3);
        }
        else {
            prevPolicyExpiryDate = new Date(localdate.getFullYear(), (policyExpiryMonth - 1), policyExpiryDay);
        }
        if (prevPolicyExpiryDate < localdate) {
            prevPolicyExpiryDate = new Date(localdate.getFullYear() + 1, (policyExpiryMonth - 1), policyExpiryDay);
        }
        return prevPolicyExpiryDate;
    };

    const GetUtmSource = function (productId) {
        let utmSource = '';
        if (isHealthRenewal === 1)
            utmSource = 'CrossSellfreshsales';

        if (rootScopeService.getProductId() === 131) {
            switch (productId) {
                case 2://Health
                    utmSource = 'HealthOnCorp'
                    break;
                case 3: //Travel
                    utmSource = 'TravelOnCorp'
                    break;
                case 7: //Term
                    utmSource = 'TermOnCorp'
                    break;
                case 114: //Two Wheeler
                    utmSource = 'TwowheelerOnCorp'
                    break;
                case 115: //Investment
                    utmSource = 'InvestmentOnCorp'
                    break;
                case 117: //Motor
                    utmSource = 'MotorOnCorp'
                    break;
                case 139: //Commercial
                    utmSource = 'CommercialOnCorp'
                default:
                    break;
            }
        }
        return utmSource;
    };

    const SetCrossSellValues = function (productId) {
        var json = {
            CustomerId: rootScopeService.getCustomerId(),
            LeadId: (props && props.selectedLeadId) ? props.selectedLeadId : parentLeadId,
            CoreLeadId: -1,
            NeedId: -1,
            ProductID: productId,
            ProductName: CrossSellProducts.filter((p) => p.ProductId == inputs.product)[0].ProductName,
            UserId: User.UserId,
            Comments: inputs.comment,
            UTMsource: GetUtmSource(productId),
            UTMMedium: (props && props.isModal) ? "CrosssellOnRejection" : ''
        };
        switch (productId) {
            case 2://Health
                json = {
                    ...json,
                    NoOfMembers: inputs.noOfMembers,
                    AnnualIncome: inputs.annualIncome
                };
                break;
            case 106://CI
                json = {
                    ...json,
                    NoOfMembers: inputs.noOfMembers,
                    AnnualIncome: inputs.annualIncome
                };
                break;
            case 118://PA
                json = {
                    ...json,
                    NoOfMembers: inputs.noOfMembers,
                    AnnualIncome: inputs.annualIncome
                };
                break;
            case 3://Travel
                //Year,month,day,hr
                var TravelStartDate = null;
                TravelStartDate = new Date(inputs.startYear, (inputs.startMonth - 1), inputs.startDay, 0, 0);
                var TravelEndDate = null;
                if (inputs.endYear > 0 && inputs.endMonth > 0) {
                    TravelEndDate = new Date(inputs.endYear, (inputs.endMonth - 1), inputs.endDay, 0, 0);
                }
                json = {
                    ...json,
                    TravelType: parseInt(inputs.travelType),
                    TravelStartDate: TravelStartDate,
                    TravelEndDate: TravelEndDate,
                };
                break;
            case 101://Homeloan
                json = {
                    ...json,
                    PropertyType: inputs.propertyType,
                };
                break;
            case 115://Investment

                json = {
                    ...json,
                    AnnualIncome: inputs.annualIncome,
                    ProductType: inputs.subProductId,
                };
                break;
            case 116://PHC
                json = {
                    ...json,
                    AnnualIncome: inputs.annualIncome,
                    NoOfMembers: inputs.noOfMembers,
                };
                break;
            case 114://Two-Wheeler
                json = {
                    ...json,
                    VehicleModelID: inputs.modelId,
                    VehicleMakeID: inputs.makeId,
                };
                break;
            case 117://New-Car
                json = {
                    ...json,
                    AnnualIncome: (inputs.annualIncome == "" ? undefined : inputs.annualIncome),
                    PolicyExpiryDate: getPolicyExpiryDate(),
                    VehicleModelID: inputs.modelId,
                    VehicleMakeID: inputs.makeId,
                };
                break;
            case 130://STU
                json = {
                    ...json,
                    NoOfMembers: inputs.noOfMembers,
                    AnnualIncome: inputs.annualIncome,
                };
                break;
            case 7://Term
                json = {
                    ...json,
                    AnnualIncome: inputs.annualIncome,
                    SA: inputs.sumAssured,
                };
                break;
            case 131://SME
                json = {
                    ...json,
                    ProductType: inputs.subProductId,
                };
                break;
            default:
                break;
        }
        return json;
    };
    const CreateCrossSellLeadService = (requestData) => {
        const input = {
            url: `coremrs/api/MRSCore/CreateCrossSellLead`,
            method: 'POST', service: 'MatrixCoreAPI',
            requestData
        }
        return CALL_API(input).then((response) => {
            return response;
        })
    }
    const CreateCrossSellLead = () => {

        if (ValidateCrossSell()) {
            setIsSaveVisible(false)
            let reqData = SetCrossSellValues(inputs.product);
            CreateCrossSellLeadService(reqData).then(function (response) {
                let IsNewLeadCreated = false;
                setFOSPopupOpen(false);
                //check
                if (response.IsSaved) {
                    var message = "";
                    if (response.Message == "") {
                        message = "Successfully Saved";
                    }
                    else if (inputs.product == 124) {
                        message = response.Message;
                    }
                    else {
                        let filteredArray = Array.isArray(CrossSellProducts) && CrossSellProducts.filter((p) => p.ProductId == inputs.product);
                        let productName = (filteredArray.length > 0) && filteredArray[0].ProductName;
                        message = "LeadID  " + response.Message + " Created in " + productName;
                        IsNewLeadCreated = true;
                        // message = "LeadID  " + response.Message + " Created successfully ";
                    }
                    // enqueueSnackbar(message, {
                    //     variant: 'success',
                    //     autoHideDuration: 3000,
                    // });

                    let e = window.alert(message);
                    if (IsNewLeadCreated && AppointmentCreation == "Yes" && (CrossSellAppointmentProducts.indexOf(inputs.product) > -1) && AppointmentCreateEligibility) {

                        let LeadId = response.Message;//44275019
                        setFOSLeadId(LeadId);
                        setFOSProductId(inputs.product);
                        console.log("CrossSell Lead", LeadId);
                        setFOSPopupOpen(true)
                    }
                } else {
                    window.alert(response.Message);
                }
                reduxDispatch(updateStateInRedux({ key: "CrossSellRefLeadId", value: parentLeadId }));
                CancelCrossSell();
                if (props.isModal && props.handleClose) {
                    props.handleClose();
                }
            }, function (reason) {
                console.log("CrossSellLeadfailreason", reason)
                setIsSaveVisible(true);
                enqueueSnackbar("Error Creating Cross-Sell lead", {
                    variant: 'error',
                    autoHideDuration: 3000,
                });
            });
        }

    }


    const handleChange = (e) => {
        let value = e.target.value;
        if (["product"].includes(e.target.name)) {
            value = parseInt(e.target.value);
        }
        setInputs({ ...inputs, [e.target.name]: value });
    }

    const IsBookedLeadStatus = () => {
        let IsBooked = false;
        Array.isArray(AllLeads) && AllLeads.forEach(lead => {
            // if (lead.StatusId > 2 && ([5, 6].indexOf(lead.StatusId) === -1)) {
            if (lead.StatusMode == 'P' && lead.StatusId >= 13) {
                IsBooked = true;
            }
        });
        return IsBooked;
    }

    const getTotalTalktime = () => {
        let durationTime = getCurrentTalkTime();
        if (durationTime >= 300) {
            setAppointmentCreateEligibility(true);
        }
        else {
            let HistoryTalktime = 0;
            GetCallDetails(parentLeadId).then(function (resultData) {
                if (resultData != null && !resultData.isError) {

                    HistoryTalktime = parseInt(resultData.HistoryTalktime) || 0;
                    if (HistoryTalktime + durationTime >= 300) {
                        setAppointmentCreateEligibility(true);
                    }
                }
            }).catch((error) => {

            });
        }

    }
    useEffect(() => {
        if (inputs.product > 0) {
            onProductChange();
        }
    }, [inputs.product])

    useEffect(() => {
        if (inputs.makeId > 0) {
            onMakeChange();
        }
    }, [inputs.makeId])

    useEffect(() => {
        BindDayddl('StartDay');
    }, [inputs.startMonth, inputs.startYear])
    useEffect(() => {
        BindDayddl('EndDay');
    }, [inputs.endMonth, inputs.endYear])

    const handleToggle = (e) => {
        setShow(!show);
        if (CrossSellProducts.length == 0) {
            getProductList();
            if (IsRenewal == 1 && rootScopeService.getProductId() == 2) {
                setIsHealthRenewal(1);
            }
            else {
                setIsHealthRenewal(0);
            }
        }
    }

    useEffect(() => {
        if (RefreshLead) {
            setShow(false);
        }
    }, [RefreshLead]);

    useEffect(() => {
        if (props && props.isModal)
            getProductList();
    }, [props && props.isModal]);

    return (
        <Grid item sm={12} md={12} xs={12}>
            <div className={`crossSell-section ${props.isModal ? 'in-modal' : ''}`}>
                {!props.isModal && (
                    <>
                        <h3> Cross-Sell </h3>
                        <span className="subHeading">Sell another plan to customer</span>
                        <div className="expandmoreIcon">
                            <ExpandMore onClick={handleToggle} style={{ transform: show ? 'rotate(180deg)' : 'rotate(0deg)' }} />
                        </div>
                    </>
                )}
                {show && <>
                    <Grid container spacing={3}>
                        <SelectDropdown
                            name="product"
                            label="Select Product"
                            value={inputs.product}
                            options={CrossSellProducts}
                            labelKeyInOptions='ProductName'
                            valueKeyInOptions='ProductId'
                            handleChange={handleChange}
                            show={true}
                            sm={6} md={4} xs={12}
                            native={true}
                        />
                        <Tooltip title="Select to cross-sell corporate policies like Employee benefits, Fire, Marine, Engineering, Liability and so on." arrow placement='top'>
                            {ShowInfoIcon && <InfoIcon className="info" />}
                        </Tooltip>
                        <SelectDropdown
                            name="subProductId"
                            label="Sub-Product"
                            value={inputs.subProductId}
                            options={subProductList}
                            labelKeyInOptions='Name'
                            valueKeyInOptions='ID'
                            handleChange={handleChange}
                            show={inputs.product == 131}
                            sm={6} md={4} xs={12}
                        />
                        <SelectDropdown
                            name="travelType"
                            label="Travel Type"
                            value={inputs.travelType}
                            options={options.travelType}
                            labelKeyInOptions='label'
                            valueKeyInOptions='value'
                            handleChange={handleChange}
                            show={inputs.product == 3}
                            sm={6} md={4} xs={12}
                        />
                        <SelectDropdown
                            name="annualIncome"
                            label="Annual Income"
                            value={inputs.annualIncome}
                            options={annualIncomeList}
                            labelKeyInOptions='AnnualIncomeName'
                            valueKeyInOptions='AnnualIncomevalue'
                            handleChange={handleChange}
                            show={showIncome}
                            sm={6} md={4} xs={12}
                        />
                        <SelectDropdown
                            name="sumAssured"
                            label="Sum Assured:"
                            value={inputs.sumAssured}
                            options={options.sumAssured}
                            labelKeyInOptions='label'
                            valueKeyInOptions='value'
                            handleChange={handleChange}
                            show={inputs.product == 7}
                            sm={6} md={4} xs={12}
                        />

                        <SelectDropdown
                            name="noOfMembers"
                            label="No. of Members"
                            value={inputs.noOfMembers}
                            options={options.noOfMembers}
                            labelKeyInOptions='label'
                            valueKeyInOptions='value'
                            handleChange={handleChange}
                            show={inputs.product == 2 || inputs.product == 116 || inputs.product == 130}
                            sm={6} md={4} xs={12}
                        />

                        <SelectDropdown
                            name="subProductId"
                            label="Investment type:"
                            value={inputs.subProductId}
                            options={subProductList}
                            labelKeyInOptions='Name'
                            valueKeyInOptions='ID'
                            handleChange={handleChange}
                            show={inputs.product == 115}
                            sm={6} md={4} xs={12}
                        />

                        <SelectDropdown
                            name="makeId"
                            label="Make"
                            value={inputs.makeId}
                            options={makeList}
                            labelKeyInOptions='Name'
                            valueKeyInOptions='ID'
                            handleChange={handleChange}
                            show={showMakeModel}
                            sm={6} md={4} xs={12}
                        />

                        <SelectDropdown
                            name="modelId"
                            label="Model"
                            value={inputs.modelId}
                            options={modelList}
                            labelKeyInOptions='Name'
                            valueKeyInOptions='ID'
                            handleChange={handleChange}
                            show={showMakeModel}
                            sm={6} md={4} xs={12}
                        />
                        <SelectDropdown
                            name="propertyType"
                            label="House Type"
                            value={inputs.propertyType}
                            options={options.propertyType}
                            labelKeyInOptions='label'
                            valueKeyInOptions='value'
                            handleChange={handleChange}
                            show={inputs.product == 101}
                            sm={6} md={4} xs={12}
                        />
                        {(inputs.product == 3)
                            && <Grid item xs={12} style={{ margin: "10px 0 -15px 0", fontSize: '14px', color: 'black', fontWeight: 'bold' }}>
                                Travel Start Date
                            </Grid>
                        }
                        {(CrossSellAppointmentProducts.indexOf(inputs.product) > -1) && AppointmentCreateEligibility &&
                            <Grid item sm={12} md={12} xs={12}>
                                <p>Do you want to create an appointment?</p>
                                <div className="radioDesign">
                                    <RadioGroup name="AppointmentCreation" value={inputs.AppointmentCreation} onChange={(e) => { handleAppointmentChange(e) }}>
                                        <FormControlLabel value={"Yes"} control={<Radio />} label="Yes" />
                                        <FormControlLabel value={"No"} control={<Radio />} label="No" />
                                    </RadioGroup>
                                </div>
                            </Grid>

                        }
                        <SelectDropdown
                            name="startDay"
                            label="Day"
                            value={inputs.startDay}
                            options={startDay}
                            labelKeyInOptions='Name'
                            valueKeyInOptions='ID'
                            handleChange={handleChange}
                            show={inputs.product == 3}
                            sm={6} md={4} xs={12}
                        />
                        <SelectDropdown
                            name="startMonth"
                            label="Month"
                            value={inputs.startMonth}
                            options={Monthlist}
                            labelKeyInOptions='Name'
                            valueKeyInOptions='ID'
                            handleChange={handleChange}
                            show={inputs.product == 3}
                            sm={6} md={4} xs={12}
                        />
                        <SelectDropdown
                            name="startYear"
                            label="Year"
                            value={inputs.startYear}
                            options={startYear}
                            labelKeyInOptions='Name'
                            valueKeyInOptions='ID'
                            handleChange={handleChange}
                            show={inputs.product == 3}
                            sm={6} md={4} xs={12}
                        />
                        {(inputs.product == 3)
                            && <Grid item xs={12} style={{ margin: "10px 0 -15px 0", fontSize: '14px', color: 'black', fontWeight: 'bold' }}>
                                Travel End Date
                            </Grid>
                        }

                        <SelectDropdown
                            name="endDay"
                            label="Day"
                            value={inputs.endDay}
                            options={endDay}
                            labelKeyInOptions='Name'
                            valueKeyInOptions='ID'
                            handleChange={handleChange}
                            show={inputs.product == 3}
                            sm={6} md={4} xs={12}
                        />
                        <SelectDropdown
                            name="endMonth"
                            label="Month"
                            value={inputs.endMonth}
                            options={Monthlist}
                            labelKeyInOptions='Name'
                            valueKeyInOptions='ID'
                            handleChange={handleChange}
                            show={inputs.product == 3}
                            sm={6} md={4} xs={12}
                        />
                        <SelectDropdown
                            name="endYear"
                            label="Year"
                            value={inputs.endYear}
                            options={endYear}
                            labelKeyInOptions='Name'
                            valueKeyInOptions='ID'
                            handleChange={handleChange}
                            show={inputs.product == 3}
                            sm={6} md={4} xs={12}
                        />
                        {showexpirymodel &&
                            <Grid item sm={6} md={4} xs={12}>
                                <TextField
                                    name="policyExpiryDay"
                                    label="Prev. Policy Expiry Date:"
                                    value={inputs.policyExpiryDay}
                                    onChange={handleChange}
                                    variant="outlined"
                                    size="small"
                                    fullWidth
                                />
                            </Grid>
                        }
                        <SelectDropdown
                            name="expiryMonth"
                            label="Expiry Month"
                            value={inputs.expiryMonth}
                            options={Monthlist}
                            labelKeyInOptions='Name'
                            valueKeyInOptions='ID'
                            handleChange={handleChange}
                            show={showexpirymodel}
                            sm={6} md={4} xs={12}
                        />

                    </Grid>

                    <Grid container spacing={3}>
                        <Grid item sm={11} md={12} xs={12}>   <TextareaAutosize name="comment" minRows={3} placeholder="Add Comment" value={inputs.comment} onChange={handleChange} /></Grid>
                    </Grid>
                    <Grid item sm={12} md={12} xs={12} className="text-center"><Button onClick={CreateCrossSellLead} disabled={!isSaveVisible} style={{ background: !isSaveVisible ? 'gray' : null }}>Submit</Button> </Grid>
                    {FOSPopupOpen && <FOSpopUp open={FOSPopupOpen} handleClose={() => {
                        setFOSPopupOpen(false);
                    }}
                        parentId={FOSLeadId}
                        customerId={rootScopeService.getCustomerId()}
                        productId={FOSProductId}
                        UserId={User.UserId}
                        disableBackdropClick={true}
                        src="crosssell"
                        CrossSellProcess={true}
                    />}
                </>}
            </div>

        </Grid >

    )
}