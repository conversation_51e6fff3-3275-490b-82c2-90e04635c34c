


/* Custom Close Button */
.custom-close-button {
    position: absolute !important;
    top: 12px !important;
    right: 12px !important;
    z-index: 1000 !important;
    background: transparent !important;
    color: #424242 !important;
    border-radius: 50% !important;
    width: 36px !important;
    height: 36px !important;
    padding: 6px !important;
    min-width: 36px !important;

    &:hover {
        background: rgba(0, 0, 0, 0.04) !important;
        color: #212121 !important;
    }

    .MuiSvgIcon-root {
        font-size: 24px !important;
    }
}

/* Header Section - White background with blue text */
.pitch-header {
    background: white;
    color: #424242;
    padding: 24px;
    text-align: left;
    position: relative;

    .pitch-icon {
        margin-bottom: 6px;

       
    }

    .pitch-title {
        color: #0065FF;
        font-family: Roboto;
        font-size: 24px;
        font-style: normal;
        font-weight: 600;
        line-height: 35px;
    }

    .pitch-content {
        color: rgba(37, 56, 88, 0.89);
        font-family: Roboto;
        font-size: 16px;
        font-style: normal;
        font-weight: 400;
        line-height: 35px;
    }

    // Creative recommendations styling
    .recommendations-list {
        margin-top: 12px;
        display: flex;
        flex-direction: column;
        gap: 10px;
    }

    .recommendation-card {
        position: relative;
        display: flex;
        align-items: flex-start;
        gap: 12px;
        padding: 16px;
        background: linear-gradient(135deg, #ffffff 0%, #f8faff 100%);
        border-radius: 12px;
        border: 1px solid rgba(0, 101, 255, 0.1);
        box-shadow: 0 2px 8px rgba(0, 101, 255, 0.08);
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        overflow: hidden;

        &:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 101, 255, 0.15);
            border-color: rgba(0, 101, 255, 0.2);

            .card-icon .icon {
                transform: scale(1.1);
            }

            .card-accent {
                width: 100%;
            }
        }

        .card-icon {
            background: linear-gradient(135deg, #0065FF 0%, #4285F4 100%);
            width: 36px;
            height: 36px;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-shrink: 0;
            box-shadow: 0 4px 12px rgba(0, 101, 255, 0.3);

            .icon {
                font-size: 18px;
                transition: transform 0.3s ease;
                filter: brightness(1.2);
            }
        }

        .card-content {
            flex: 1;
            padding-top: 2px;

            .recommendation-text {
                color: rgba(37, 56, 88, 0.89) !important;
                font-family: Roboto !important;
                font-size: 14px !important;
                font-weight: 400 !important;
                line-height: 22px !important;
                margin: 0 !important;
            }
        }

        .card-accent {
            position: absolute;
            bottom: 0;
            left: 0;
            height: 3px;
            width: 0;
            background: linear-gradient(90deg, #0065FF 0%, #4285F4 100%);
            transition: width 0.4s ease;
        }

        // Animation delay for staggered effect
        &:nth-child(1) { animation-delay: 0.1s; }
        &:nth-child(2) { animation-delay: 0.2s; }
        &:nth-child(3) { animation-delay: 0.3s; }
        &:nth-child(4) { animation-delay: 0.4s; }
        &:nth-child(5) { animation-delay: 0.5s; }
    }
}

/* Customer Concern Section */
.customer-concern-section {
    border-radius: 8px;
    background: rgba(255, 169, 26, 0.10);
    padding:16px;
  

    .concern-header {
        display: flex;
        align-items: center;
        gap: 8px;
        margin-bottom: 16px;

       svg {
            font-size: 32px;
            color: #FFA91A;
        }

        .concern-title {
            color: #FFA91A;
            font-family: Roboto;
            font-size: 20px;
            font-style: normal;
            font-weight: 600;
            line-height: 32px; 
            padding-left:0px;
        }
    }

    .concern-content {
    

        .concern-subtitle {
            color:  rgba(37, 56, 88, 0.89);
            font-family: Roboto;
            font-size: 20px;
            font-style: normal;
            font-weight: 600;
            line-height: 24px; 
            padding-left:0px;
            margin-bottom: 8px;
        }

        .concern-description {
            color: rgba(37, 56, 88, 0.89);
            font-family: Roboto;
            font-size: 16px;
            font-style: normal;
            font-weight: 400;
            line-height: 24px;

            strong {
              
                font-weight: 600;
            }
        }
    }
}

/* Modal Popup Overrides */
.pitchRecommendationPopup {
    .MuiDialog-paper {
        border-radius: 8px;
        overflow: hidden;
        width: 600px;
        margin: 16px;
    }

  

    .MuiDialogTitle-root {
        display: none !important;
        padding: 0 !important;
        margin: 0 !important;
        height: 0 !important;
    }

   
    .MuiIconButton-root:not(.custom-close-button) {
        display: none !important;
    }
}

/* Responsive Design */
@media (max-width: 600px) {
    .pitch-popup-container {
        min-width: 320px;
        max-width: 90vw;
    }

    .pitch-header {
        padding: 20px;

        .pitch-title {
            font-size: 20px !important;
        }

        .pitch-content {
            font-size: 14px !important;
        }

        .recommendations-list {
            margin-top: 10px;
            gap: 8px;
        }

        .recommendation-card {
            padding: 12px;
            gap: 10px;

            .card-icon {
                width: 32px;
                height: 32px;
                border-radius: 8px;

                .icon {
                    font-size: 16px;
                }
            }

            .card-content .recommendation-text {
                font-size: 13px !important;
                line-height: 20px !important;
            }
        }
    }

    .customer-concern-section {
        padding: 16px 20px;

        .concern-header {
            .concern-title {
                font-size: 16px !important;
            }
        }

        .concern-content {
            .concern-subtitle {
                font-size: 14px !important;
            }

            .concern-description {
                font-size: 13px !important;
            }
        }
    }
}


