import React, { useEffect } from "react";
import { Checkbox, FormControlLabel, Grid, TextField } from "@mui/material";
import { connect } from "react-redux";
import { setRefreshLead, setRefreshCustomerId } from "../../../store/actions/SalesView/SalesView";
import { useState } from "react";
import { SelectDropdown, TextInput } from "../../../components";
import { CONFIG, SV_CONFIG } from "../../../appconfig";
import './referralLead.scss'
import { Autocomplete } from '@mui/material';
import masterService from "../../../services/masterService";
import rootScopeService from "../../../services/rootScopeService";
import { CALL_API } from "../../../services";
import { SmeLeadSources } from "../CreateLeadHelpers/CreateLeadMasters";
import User from "../../../services/user.service";
var SMEUTMSource;
const GetReferralLeadId = (value) => {
    try {
        return value.substring(0, value.indexOf(' ')).trim();
    }
    catch {
        return 0;
    }
}

export const UpdateExitPointUrl = (leadId) => {
    try {
        const input = {
            url: `api/LeadDetails/GetCJExitPointUrl?LeadId=${leadId}&ProductId=131`,
            method: 'GET',
            service: 'MatrixCoreAPI'
        }
        CALL_API(input).then((response) => {
            // Do nothing
        });
    }
    catch {

    }
}

export const SetSmeCreateLeadParams = (data, customerId) => {
    var param = {}
    param.Name = data.ContactPersonName.trim();
    param.ProductId = 131;
    param.LeadSource = SmeLeadSources.filter((e) => e.Id == data.LeadSourceId)[0].Name;
    param.UtmSource = data.UtmSource;
    param.ReferralLead = GetReferralLeadId(data.SmeSelectedLead);
    param.ReferralLead = param.ReferralLead ? param.ReferralLead : 0;
    param.SubProductId = data.SubProductId;
    param.CityID = data.CityId;
    param.CountryId = data.CountryId;
    param.Email = data.EmailId;
    param.CompanyName = data.CompanyName;
    param.UtmMedium = data.UtmSource;
    param.MobileNo = data.IsExistMobileNo ? 0 : data.MobileNo
    param.CustomerId = data.IsExistMobileNo ? customerId : 0
    param.OccupationId = (data.Occupation && data.Occupation.ID) ? parseInt(data.Occupation.ID) : 0;
    param.AssignLead = (data.LeadSourceId == 8) ? "false" : "true";
    return param;
}

export const ValidateSmeData = (data) => {
    let message = '';
    const isValidEmail = /^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$/g;
    if (data) {
        if (!data.IsSmeFosAgent && !data.SmeSelectedLead) {
            message += "Please select a valid Referral Id\n"
        }
        if (!data.SubProductId && data.SubProductId <= 0) {
            message += "Please select Product Type\n"
        }
        if (!data.ContactPersonName) {
            message += "Please enter Contact Person's Name\n"
        }
        if (!data.CompanyName) {
            message += "Please enter Company Name\n"
        }
        if (!data.CountryId || data.CountryId <= 0) {
            message += "Please select a valid Country\n"
        }
        if (!data.CityId || data.CityId <= 0) {
            message += "Please select a valid City\n"
        }
        if (data.OccupationsExists && (!data.Occupation || !data.Occupation.ID)) {
            if (data.SubProductId && data.SubProductId == 14) {
                message += "Please select a valid Specialization\n"
            }
            else {
                message += "Please select a valid Occupation\n"
            }
        }
        if (!(data.IsExistMobileNo)) {
            if (!data.MobileNo) {
                message += "Please enter a valid Mobile No\n"
            }
            else if (data.MobileNo && data.CountryId == 392 && !((data.MobileNo.toString()).length == 10)) {
                message += "Mobile No should be 10 digit numeric\n"
            }
            else if (data.MobileNo && data.CountryId !== 392 && !((data.MobileNo.toString()).length >= 5 && (data.MobileNo.toString()).length <= 13)) {
                message += "Mobile No should be 5 to 13 digit numeric\n"
            }
        }
        if (data.EmailId && !data.EmailId.match(isValidEmail)) {
            message += "Please enter a valid Email\n"
        }
        if(data.LeadSourceId && [3,67].indexOf(data.LeadSourceId)>-1)
        {
            if (!data.UtmSource || (SMEUTMSource && data.UtmSource && (SMEUTMSource.indexOf(data.UtmSource))==-1)) {
                message += "Please enter a valid Utm Source\n"
            }
        }
        
    }
    return message;
};

export const CreateReferralSme = function (props) {
    const [selectedCity, setSelectedCity] = useState({});
    const [Cities, setCities] = useState([]);
    const [InputCountry, setInputCountry] = useState('');
    const [inputValue, setInputValue] = useState('');
    const [LeadIds, setLeadIds] = useState([]);
    const [SubProduct, setSubProduct] = useState([]);
    const [LeadStatus, setLeadStatus] = useState({ "Status": -1, "SubProductId": 0 });
    const [LeadStatusUpdated, setLeadStatusUpdated] = useState(null);
    const [LeadSource, setLeadSource] = useState(SmeLeadSources);
    const [IsSmeFosAgent, setIsSmeFosAgent] = useState(false);
    const [IsSmePreSalesAgent, setIsSmePreSalesAgent] = useState(false);
    const [Occupations, setOccupations] = useState([]);
    const [DisableLeadSource, setDisableLeadSource] = useState(false);
    
    if(props.CreateLeadParams && props.CreateLeadParams.LeadSourceId)
    {
        if(props.CreateLeadParams.LeadSourceId==3)
        {
            SMEUTMSource = SV_CONFIG["SMEReferralUTMSource"] ? SV_CONFIG["SMEReferralUTMSource"] : '';
        }
        else if(props.CreateLeadParams.LeadSourceId==67)
        {
            SMEUTMSource = SV_CONFIG["SMEExternalUTMSource"] ? SV_CONFIG["SMEExternalUTMSource"] : '';
        }
    }
    useEffect(() => {
        GetSubProductByProductId();
        GetSmePreSalesAgent();
        CheckIsSmeFosAgent();
        GetOccupations();
        GetCityList();
        PageLoadProcess();
        GetLeadIds();
    }, [])

    useEffect(() => {
        PageLoadProcess();
    }, [IsSmePreSalesAgent, IsSmeFosAgent])

    useEffect(() => {
        if (props.SmeLeadProcessed && props.SmeLeadProcessed > 0) {
            setLeadStatus({ "Status": -1, "SubProductId": 0 });
            setLeadStatusUpdated(null);
            PageLoadProcess();
        }
    }, [props.SmeLeadProcessed])

    useEffect(()=>{
        if(props.CreateLeadParams && props.CreateLeadParams.LeadSourceId && props.CreateLeadParams.LeadSourceId==3)
        {
            props.setCreateLeadParams((prevState) => ({ ...prevState, IsExistMobileNo: false }));
        }
    },[props.CreateLeadParams.UtmSource])

    useEffect(() => {
        if (props.CreateLeadParams.LeadSourceId == 3) {
            props.setCreateLeadParams((prevState) => ({ ...prevState, IsExistMobileNo: false }));
        }
        else if (!IsSmeFosAgent) {
            props.setCreateLeadParams((prevState) => ({ ...prevState, IsExistMobileNo: true }));
        }

        if (LeadStatus.Status == 1) //For booked Leads
        {
            let utmSource = ''
            // if (props.CreateLeadParams.LeadSourceId == 3) {
            //     utmSource = "Client Referral"
            // }
            if (props.CreateLeadParams.LeadSourceId == 68) {
                utmSource = "Retargetting Customer";
            }
            else if (props.CreateLeadParams.LeadSourceId == 8) {
                utmSource = "Same Agent CrossSell"
            }

            if (utmSource) {
                utmSource = (IsSmeFosAgent || IsSmePreSalesAgent) ? props.CreateLeadParams.UtmSource : utmSource;
                props.setCreateLeadParams((prevState) => ({ ...prevState, UtmSource: utmSource }));
            }
        }
    }, [props.CreateLeadParams.LeadSourceId])

    useEffect(() => {
        UpdateUtmSourceLeadSource();
    }, [LeadStatusUpdated, props.CreateLeadParams.SubProductId])

    const PageLoadProcess = () => {
        setInputCountry({ Country: 'INDIA', CountryID: 392 });

        if (IsSmeFosAgent) {
            props.setCreateLeadParams((prevState) => ({ ...prevState, UtmSource: "matrixfresh", LeadSourceId: 35, IsSmeFosAgent: true }))
            setDisableLeadSource(true);
        }
        else {
            if (props.CreateLeadParams.LeadSourceId == 3) {
                props.setCreateLeadParams((prevState) => ({ ...prevState, IsExistMobileNo: false }));
            }
            else {
                props.setCreateLeadParams((prevState) => ({ ...prevState, IsExistMobileNo: true }));
            }
            setDisableLeadSource(false);
        }

        if (IsSmePreSalesAgent) {
            props.setCreateLeadParams((prevState) => ({ ...prevState, UtmSource: "Pre-sales FOS" }))
        }
    }

    const GetOccupationsBySubProduct = (Id = 0) => {
        try {
            let subProductId = (Id && Id > 0) ? Id : props.CreateLeadParams.SubProductId;
            return Occupations.filter((o) => o.SubProductTypeId == subProductId);
        }
        catch { return []; }
    }

    const GetOccupations = () => {
        try {
            masterService.getSMEOccupationList().then(function (occupations) {
                if (Array.isArray(occupations) && occupations.length > 0) {
                    setOccupations(occupations);
                }
            });
        }
        catch
        {
            // Do nothing
        }
    }

    const EnableUpsell = (newLeadSourceId, oldLeadSourceId) => {
        let enableUpsellSource = (oldLeadSourceId == newLeadSourceId) ||
            (oldLeadSourceId == 5 && newLeadSourceId == 6) ||
            ([16, 17, 18].includes(oldLeadSourceId) && [16, 17, 18].includes(newLeadSourceId)) ||
            ([15, 12, 21, 20].includes(oldLeadSourceId) && [15, 12, 21, 20].includes(newLeadSourceId)) ||
            ([5, 8, 7].includes(oldLeadSourceId) && [5, 8, 7].includes(newLeadSourceId))
        return enableUpsellSource;
    }

    const UpdateUtmSourceLeadSource = () => {
        if (LeadStatus.Status > -1 && props.CreateLeadParams.SubProductId && props.CreateLeadParams.SubProductId > 0) {
            let utmSource = '';
            let leadSourceId = 3;
            let removeLeadSourceFromMaster = [];
            let disableLeadSource = false;
            setDisableLeadSource(disableLeadSource);

            if (LeadStatus.Status == 0) //For active leads
            {
                leadSourceId = (props.CreateLeadParams.SubProductId == LeadStatus.SubProductId) ? 3 : 8;
                if(leadSourceId==8)
                {
                    utmSource = "Client Referral"
                }
                disableLeadSource = true;
            }
            else if (LeadStatus.Status == 1) //For booked Leads
            {
                if (EnableUpsell(props.CreateLeadParams.SubProductId, LeadStatus.SubProductId)) {
                    leadSourceId = 68;
                    utmSource = "Retargetting Customer";
                    removeLeadSourceFromMaster = [16, 35, 8];
                }
                else {
                    leadSourceId = 8;
                    utmSource = "Same Agent CrossSell"
                    removeLeadSourceFromMaster = [16, 35, 68];
                }
                disableLeadSource = false;
            }
            else if (LeadStatus.Status == 2) //For rejected leads
            {
                if (props.CreateLeadParams.SubProductId == LeadStatus.SubProductId) {
                    leadSourceId = 16;
                    utmSource = "Same year lost case";
                    disableLeadSource = true;
                }
                else {
                    removeLeadSourceFromMaster = [16, 68];
                    utmSource = "Referral & Cross Sell"
                }
            }
            if (!IsSmeFosAgent) {
                removeLeadSourceFromMaster.push(35);
            }
            RemoveLeadSourceFromMaster(removeLeadSourceFromMaster);
            utmSource = (IsSmeFosAgent || IsSmePreSalesAgent) ? props.CreateLeadParams.UtmSource : utmSource;
            props.setCreateLeadParams((prevState) => ({ ...prevState, UtmSource: utmSource, LeadSourceId: leadSourceId }));
            setDisableLeadSource(disableLeadSource);
        }
    }

    const RemoveLeadSourceFromMaster = (leadSourceIds) => {
        let leadSources = SmeLeadSources.filter((p) => leadSourceIds.indexOf(p.Id) === -1);
        setLeadSource(leadSources);
    }

    const CheckIsSmeFosAgent = () => {
        try {
            if (User.RoleId === 13) {
                let groupList = User.UserGroupList;
                let smeFosGroupsForBrokerage = SV_CONFIG["SMEFOSGroups"];
                if (Array.isArray(smeFosGroupsForBrokerage) && Array.isArray(groupList)) {
                    for (var i = 0, len = groupList.length; i < len; i++) {
                        if (smeFosGroupsForBrokerage.indexOf(groupList[i].GroupId) > -1 && props.parentLeadId == -1) {
                            setIsSmeFosAgent(true);
                            break;
                        }
                    }
                }
            }
        }
        catch
        {
            // Do nothing
        }
    };

    const GetSmePreSalesAgent = () => {
        try {
            if (User.RoleId === 13) {
                let groupList = User.UserGroupList;
                let preSalesSmeGroups = (window.SV_CONFIG_UNCACHED && window.SV_CONFIG_UNCACHED.PreSalesSmeGroups) || SV_CONFIG.PreSalesSmeGroups;
                if (Array.isArray(preSalesSmeGroups) && Array.isArray(groupList)) {
                    for (var i = 0, len = groupList.length; i < len; i++) {
                        if (preSalesSmeGroups.indexOf(groupList[i].GroupId) > -1) {
                            setIsSmePreSalesAgent(true);
                            break;
                        }
                    }
                }
            }
        }
        catch
        {
            // Do nothing
        }
    };

    const GetCityList = () => {
        masterService.getCities().then(function (result) {
            setCities(result);
        });
    };

    const GetLeadIds = () => {
        const input = {
            url: `api/SalesView/GetLeadIds/${props.parentLeadId}`,
            method: 'GET',
            service: 'MatrixCoreAPI'
        }
        CALL_API(input).then((response) => {
            if (response && Array.isArray(response)) {
                setLeadIds(response);
            }
        });
    };

    const GetSubProductByProductId = () => {
        masterService.getSubProductByProductID(rootScopeService.getProductId()).then(res => {
            setSubProduct(res || []);
        });
    };

    const GetLeadStatus = (value) => {
        const input = {
            url: `api/SalesView/GetLeadStatus/${GetReferralLeadId(value)}`,
            method: 'GET',
            service: 'MatrixCoreAPI'
        }
        CALL_API(input).then((response) => {
            setLeadStatus(response);
            setLeadStatusUpdated(value);
        });
    };

    const handleChange = (e) => {
        let { name, value } = e.target;
        switch (name) {
            case "City":
                setSelectedCity(value);
                props.setCreateLeadParams((prevState) => ({ ...prevState, CityId: (e.target.value && e.target.value.CityID) ? e.target.value.CityID : 0 }))
                break;
            case "Country":
                setInputCountry(value)
                props.setCreateLeadParams((prevState) => ({ ...prevState, CountryId: value ? value.CountryID : 0 }))
                break;
            case "SmeSelectedLead":
                props.setCreateLeadParams((prevState) => ({ ...prevState, SmeSelectedLead: e.target.value ? e.target.value : '' }))
                GetLeadStatus(e.target.value)
                break;
            case "SubProductId":
                let occ = GetOccupationsBySubProduct(e.target.value);
                props.setCreateLeadParams((prevState) => ({ ...prevState, SubProductId: e.target.value ? e.target.value : 0, OccupationsExists: (Array.isArray(occ) && occ.length > 0), Occupation: null }))
                break;
            default:
                break;
        }
    }

    return (
        <>
            <Grid item sm={8} md={7} xs={12}>
                <Grid container spacing={3}>
                    <SelectDropdown
                        name="SmeSelectedLead"
                        label="Referral Id's *"
                        value={props.CreateLeadParams.SmeSelectedLead}
                        handleChange={handleChange}
                        options={LeadIds}
                        labelKeyInOptions="_all"
                        valueKeyInOptions="_all"
                        sm={12} md={12} xs={12}
                        maxLength={100}
                        show={!IsSmeFosAgent}
                    />
                    <SelectDropdown
                        name="SubProductId"
                        label="Product Type *"
                        value={props.CreateLeadParams.SubProductId}
                        handleChange={handleChange}
                        options={SubProduct}
                        labelKeyInOptions="Name"
                        valueKeyInOptions="ID"
                        sm={12} md={12} xs={12}
                    />
                    <SelectDropdown
                        name="LeadSourceId"
                        label="Lead Source *"
                        value={props.CreateLeadParams.LeadSourceId}
                        handleChange={props.handleChange}
                        options={LeadSource}
                        labelKeyInOptions="Name"
                        valueKeyInOptions="Id"
                        sm={12} md={12} xs={12}
                        disabled={DisableLeadSource}
                        maxLength={100}
                    />
                    <TextInput
                        name="UtmSource"
                        label="UTM Source"
                        handleChange={props.handleChange}
                        value={props.CreateLeadParams.UtmSource}
                        sm={12} md={12} xs={12}
                        maxLength={100}
                        disabled={true}
                        show={props.CreateLeadParams && props.CreateLeadParams.LeadSourceId && [3,67].indexOf(props.CreateLeadParams.LeadSourceId) < 0}
                    />
                    <SelectDropdown
                        name="UtmSource"
                        label="UTM Source"
                        value={props.CreateLeadParams.UtmSource}
                        handleChange={props.handleChange}
                        options={SMEUTMSource}
                        labelKeyInOptions="_all"
                        valueKeyInOptions="_all"
                        sm={12} md={12} xs={12}
                        maxLength={100}
                        show={props.CreateLeadParams && props.CreateLeadParams.LeadSourceId && [3,67].indexOf(props.CreateLeadParams.LeadSourceId) > -1}
                    />
                    <Grid item sm={12} md={12} xs={12}>
                        <Autocomplete
                            id="Occupation"
                            onChange={(event, value) => props.handleChange({ target: { name: 'Occupation', value } })}
                            options={GetOccupationsBySubProduct()}
                            name="Occupation"
                            value={props.CreateLeadParams.Occupation || null}
                            getOptionLabel={(option) => (option.Name)}
                            renderInput={(params) =>
                                <TextField {...params}
                                    label={(props.CreateLeadParams.SubProductId && props.CreateLeadParams.SubProductId == 14) ? "Specialization" : "Occupancy/Risk Category"}
                                    variant='outlined'
                                />}
                        />
                    </Grid>
                    <TextInput
                        name="ContactPersonName"
                        label="Contact Person's Name"
                        handleChange={props.handleChange}
                        value={props.CreateLeadParams.ContactPersonName}
                        sm={12} md={12} xs={12}
                        required={true}
                        maxLength={50}
                    />
                    <TextInput
                        name="CompanyName"
                        label="Company Name"
                        handleChange={props.handleChange}
                        value={props.CreateLeadParams.CompanyName}
                        sm={12} md={12} xs={12}
                        required={true}
                        maxLength={100}
                    />
                    <TextInput
                        name="EmailId"
                        label="Email Id"
                        handleChange={props.handleChange}
                        value={props.CreateLeadParams.EmailId}
                        sm={12} md={12} xs={12}
                        maxLength={100}
                    />
                    <Grid item sm={12} md={12} xs={12}>
                        <Autocomplete
                            name="Country"
                            onChange={(event, value) => handleChange({ target: { name: 'Country', value } })}
                            className={"createLeadAutocomplete"}
                            options={props.Countries}
                            value={InputCountry}
                            inputValue={inputValue}
                            getOptionLabel={(option) => option.Country || ""}
                            onInputChange={(event, newValue) => {
                                setInputValue(newValue);
                            }}
                            renderInput={(params) => (
                                <TextField
                                    {...params}
                                    variant="outlined"
                                    label="Country"
                                />
                            )}
                        />
                    </Grid>
                    <Grid item sm={12} md={12} xs={12}>
                        <Autocomplete
                            onChange={(event, value) => handleChange({ target: { name: 'City', value } })}
                            id="combo-box-demo"
                            options={Cities}
                            name="City"
                            value={(props.CreateLeadParams.CityId) ? selectedCity || null : null}
                            getOptionLabel={(option) => (option.CityStateName || '')}
                            renderInput={(params) =>
                                <TextField
                                    {...params}
                                    label="City *"
                                    variant='outlined'
                                />
                            }
                        />
                    </Grid>
                    {!(props.CreateLeadParams.IsExistMobileNo) &&
                        <>
                            <Grid item sm={4} md={4} xs={5} className="pdRight-0">
                                <Autocomplete
                                    disabled={true}
                                    onChange={(event, value) => handleChange({ target: { name: 'Country', value } })}
                                    className={"createLeadAutocomplete"}
                                    name="Country"
                                    options={props.Countries}
                                    value={InputCountry}
                                    inputValue={inputValue ? (inputValue.slice(0, 10)).toUpperCase() : null}
                                    getOptionLabel={(option) => option.Country || ""}
                                    onInputChange={(event, newValue) => {
                                        setInputValue(newValue);
                                    }}
                                    renderInput={(params) => (
                                        <TextField
                                            {...params}
                                            variant="outlined"
                                            disabled={true}
                                        />
                                    )}
                                />
                            </Grid>
                            <Grid item sm={8} md={8} xs={7} className="pdLeft-0">
                                <TextInput
                                    name="MobileNo"
                                    label="Mobile Number"
                                    value={props.CreateLeadParams.MobileNo}
                                    handleChange={props.handleChange}
                                    className="savingmobileNumber"
                                    sm={12} md={12} xs={12}
                                    required={true}
                                    maxLength="13"
                                />
                            </Grid>
                        </>
                    }
                    <Grid item sm={12} md={12} xs={12}>
                        <FormControlLabel className={IsSmeFosAgent || (props.CreateLeadParams.LeadSourceId == 3) ? "useExitMobileNoGrey" : "useExitMobileNo"}
                            name="IsExistMobileNo"
                            control={<Checkbox color="primary" />}
                            label="Use existing mobile number"
                            value={(props.CreateLeadParams.IsExistMobileNo) || false}
                            checked={(props.CreateLeadParams.IsExistMobileNo === true)|| false}
                            onChange={props.handleChange}
                            disabled={IsSmeFosAgent || (props.CreateLeadParams.LeadSourceId == 3)}
                        />
                        {!(props.CreateLeadParams.IsExistMobileNo) &&
                            <div className="differentCustomer">
                                <p>
                                    <img alt="" src={CONFIG.PUBLIC_URL + "/images/salesview/diffrentCus.svg"} />Creating a referral lead for different customer
                                </p>
                            </div>
                        }
                        {(props.CreateLeadParams.IsExistMobileNo) &&
                            <div className="SameCustomer">
                                <p>
                                    <img alt="" src={CONFIG.PUBLIC_URL + "/images/salesview/sameCus.svg"} />Creating a referral lead for the same customer
                                </p>
                            </div>
                        }
                    </Grid>
                </Grid>
            </Grid>
        </>
    )
}

const mapDispatchToProps = (dispatch) => {
    return {
        setRefreshLeadToRedux: (value) => dispatch(setRefreshLead({ RefreshLead: value }))
    };
};

export default connect(() => ({}), mapDispatchToProps)(CreateReferralSme);