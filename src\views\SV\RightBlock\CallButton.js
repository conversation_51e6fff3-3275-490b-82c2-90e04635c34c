import React, { useEffect, useState } from "react";
import { ErrorBoundary } from "../../../hoc";
import SelectCallingNumberPopup from "./Modals/SelectCallingNumberPopup";
import CallIcon from '@mui/icons-material/Call';
import PhoneDisabledIcon from '@mui/icons-material/PhoneDisabled';
// import { checkPageSrc, openingSources } from "../../../helpers";
import { updateStateInRedux } from "../../../store/actions";
import { useDispatch, useSelector } from "react-redux";
import { useSnackbar } from "notistack";
import rootScopeService from "../../../services/rootScopeService";
import User from "../../../services/user.service";
import { getCheckForNoCallingLead, IsValidUserGroup } from "../../../services/Common";
import { SV_CONFIG } from "../../../appconfig";

function CallButton() {
    let [isModalOpen, setIsModalOpen] = useState(false);
    const [showCallBtn, setShowCallBtn] = useState(true);
    let parentLeadId = useSelector(({ salesview }) => salesview.parentLeadId);
    const { enqueueSnackbar } = useSnackbar();
    const dispatch = useDispatch();
    let [leads] = useSelector(({ salesview }) => [salesview.allLeads]);
    let lead = (leads && leads.length > 0) ? leads[0] : {};
    let allLeads = leads;
    const callableVirtualNumber = useSelector(state => state.salesview.callableVirtualNumber)
    const restrictedGroups= SV_CONFIG["SMEFOSGroups"];

    useEffect(() => {
        if(lead && lead.ProductID == 219 && lead.StatusId == 83){
           setShowCallBtn(false);
        }
        else if (lead && lead.ProductID === 131) {
            const isUserInGroup = IsValidUserGroup(restrictedGroups, [13]);
            setShowCallBtn(!isUserInGroup);
        }
        else{
            setShowCallBtn(true);
        }

    }, [lead,lead.StatusId])

    //Call By Virtual Number 
    useEffect(() => {
        if (lead && lead.ProductID == 131) {
            if (callableVirtualNumber && callableVirtualNumber.VirtualNumber) {
                setShowCallBtn(false);
            } else {
                const isUserInGroup = IsValidUserGroup(restrictedGroups, [13]);
                if (isUserInGroup) {
                    setShowCallBtn(false);
                } else {
                    setShowCallBtn(true);
                }
            }
        }
    }, [callableVirtualNumber]);
    
  
    const handleCallClick = () => {
        if( lead.ProductID == 131 && getCheckForNoCallingLead(allLeads, parentLeadId)){ 
            return;
        }
        if (rootScopeService.getPriority() && User.RoleId === 13) {
            if (!parentLeadId) {
                enqueueSnackbar('Please open a lead to make call', { variant: 'error', autoHideDuration: 3000 })
                return;
            }
            var onCall = window.localStorage.getItem("onCall") === "true" ? true : false;
            if (onCall) {
                enqueueSnackbar('Seems you are already on a call, Please try after some time!', { variant: 'error', autoHideDuration: 3000 })
                return;
            }

            setShowCallBtn(false);
            setTimeout(() => {
                setShowCallBtn(true);
            }, 5000)

            // do call via connectCallSf api
            dispatch(updateStateInRedux({ key: "callOnCurrentLead", value: true }));
        }
        else {
            setIsModalOpen(true);
        }
    }

    return (
        <>

            <div className="callBtn" title="" onClick={showCallBtn ? handleCallClick : null} >
                <a>
                    <span className="ico">{showCallBtn ? <CallIcon /> : <PhoneDisabledIcon />}</span>
                    <span className="text">Call</span>
                </a>
            </div>

            <ErrorBoundary name="SelectCallingNumberPopup">
                {isModalOpen
                    ? <SelectCallingNumberPopup
                        open={isModalOpen}
                        handleClose={() => { setIsModalOpen(false) }}
                        setShowCallBtn={setShowCallBtn}
                    />
                    : null}
            </ErrorBoundary>
        </>
    )
}

export default CallButton;