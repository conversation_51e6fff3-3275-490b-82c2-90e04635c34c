import React, { useEffect, useState } from "react";
import ModalPopup from '../../../../components/Dialogs/ModalPopup';
import { CONFIG } from "../../../../appconfig";
import rootScopeService from "../../../../services/rootScopeService";
import { CALL_API } from "../../../../services";
import { LinearProgress } from "@mui/material";

export const HdfcPasaPopup = (props) => {
    let IsNotMaxTerm = [undefined, null, false, ""].indexOf(props.IsMaxTermPopUp) !== -1;
    let productid = rootScopeService.getProductId();
    let CustomerPitchData = props.CustomerPitchData;

    function formatNumber(num) {
        if (parseInt(num) >= 10000000) {
            return String((parseInt(num) / 10000000).toFixed(1)).replace(/\.0$/, '') + ' Crore';
        } else if (parseInt(num) >= 100000) {
            return String((parseInt(num) / 100000).toFixed(1)).replace(/\.0$/, '') + ' Lakh';
        } else if (parseInt(num) >= 1000) {
            return (parseInt(num) / 1000).toLocaleString('en', { maximumFractionDigits: 0 }) + 'K';
        } else {
            return parseInt(num).toString();
        }
    }

    return (
        <>
            <ModalPopup className="HDFCPasaPopupDesign" open={props.open} title="" handleClose={props.handleClose}>
                <div className="imgLogo">
                    {IsNotMaxTerm ?
                        <>
                            {
                                productid == 115 && CustomerPitchData.InsurerID == 3 &&
                                    <img alt="Logo" src={CONFIG.PUBLIC_URL + "/images/Bajajlogo.svg"} />
                            }
                            {
                                productid == 115  && CustomerPitchData.InsurerID == 15 && 
                                    <img alt="Logo" src={CONFIG.PUBLIC_URL + "/images/TATA_AIA.svg"} />
                            }
                            {
                                ([7, 1000].indexOf(productid) !== -1)  && CustomerPitchData.InsurerID == 10 && 
                                    <div className="divMaxNew">
                                        <div>
                                            <img className="imgLogoPreApprove" alt="Logo" src={CONFIG.PUBLIC_URL + "/images/PreApprovedLogo.svg"} />
                                        </div>
                                        <div>
                                            <img className="imgLogoNew" alt="Logo" src={CONFIG.PUBLIC_URL + "/images/MaxTermLife.svg"} />
                                            <h4 className="SmartSecureText">Smart Secure Plus - Housewife</h4>
                                        </div>
                                    </div>
                            }
                            {
                                ([7, 1000].indexOf(productid) !== -1)  && CustomerPitchData.InsurerID == 5 && 
                                    <div className="divMaxNew">
                                        <div>
                                            <img className="imgLogoPreApprove" alt="Logo" src={CONFIG.PUBLIC_URL + "/images/PreApprovedLogo.svg"} />
                                        </div>
                                        <div>
                                            <img className="imgLogoNew" alt="Logo" src={CONFIG.PUBLIC_URL + "/images/ipru.svg"} />
                                            <h4 className="SmartSecureText">iProtect Smart</h4>
                                        </div>
                                    </div>
                            }
                            {
                                ([7, 1000].indexOf(productid) !== -1)  && CustomerPitchData.InsurerID == 11 && 
                                    <div className="divMaxNew">
                                        <div>
                                            <img className="imgLogoPreApprove" alt="Logo" src={CONFIG.PUBLIC_URL + "/images/PreApprovedLogo.svg"} />
                                        </div>
                                        <div>
                                            <img className="imgLogoBajaj" alt="Logo" src={CONFIG.PUBLIC_URL + "/images/Bajajlogo.svg"} />
                                            <h4 className="SmartSecureText">Superwoman Term</h4>
                                        </div>
                                    </div>
                            }
                            {
                                ([7, 1000].indexOf(productid) !== -1)  && CustomerPitchData.InsurerID == 2 && 
                                    <div className="divMaxNew">
                                        <div>
                                            <img className="imgLogoPreApprove" alt="Logo" src={CONFIG.PUBLIC_URL + "/images/PreApprovedLogo.svg"} />
                                        </div>
                                        <div>
                                            <img className="imgLogoTata" alt="Logo" src={CONFIG.PUBLIC_URL + "/images/TATA_AIA.svg"} />
                                        </div>
                                    </div>
                            }
                            {
                                ([7, 1000].indexOf(productid) !== -1)  && CustomerPitchData.InsurerID == 16 && 
                                    <div className="divMaxNew">
                                        <div>
                                            <img className="imgLogoPreApprove" alt="Logo" src={CONFIG.PUBLIC_URL + "/images/PreApprovedLogo.svg"} />
                                        </div>
                                        <div>
                                            <img className="imgLogoBajaj" alt="Logo" src={CONFIG.PUBLIC_URL + "/images/HDFC-Life.svg"} />
                                            <h4 className="SmartSecureText">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Click 2 Protect Super</h4>
                                        </div>
                                    </div>
                            }
                        </>
                        :
                        <>
                            {
                                <img className="imgLogoMax" alt="Logo" src={CONFIG.PUBLIC_URL + "/images/MaxTermLife.svg"} />
                            }
                        </>
                    }
                </div>
                <div className="MAXPASAmsg">
                    {IsNotMaxTerm && ([7, 1000].indexOf(productid) !== -1)  
                        && [5,10,11,16].includes(CustomerPitchData.InsurerID)
                        && Object.keys(CustomerPitchData).length > 0 &&
                        <>
                            {
                                <div>
                                    <h3>Customer has shown interest in</h3>
                                    {props.ISBajajSelf 
                                        ? <h2><b>Pre approved Term Plan for self</b></h2>
                                        : <h2><b>Pre approved Term Plan for {CustomerPitchData.WifeName &&  CustomerPitchData.WifeName.length > 0 ? CustomerPitchData.WifeName : 'housewife'}</b></h2>}
                                                
                                </div>
                            }
                        </>
                    }
                </div>
                <div className="CustomerEligibleTable">
                    {IsNotMaxTerm ?
                        <>
                            {
                                Object.keys(CustomerPitchData).length === 0 ? <LinearProgress color="secondary" /> :
                                    <><ul>
                                        <li>
                                            Sum Assured Allowed
                                            {
                                            ([7, 1000].indexOf(productid) !== -1)  && [5,10,11,16].includes(CustomerPitchData.InsurerID) && !props.ISBajajSelf    ? 
                                                <h1>{CustomerPitchData.WifeEligibleSA ? formatNumber(CustomerPitchData.WifeEligibleSA) : 'N.A'}</h1>
                                            :
                                            <h1>{CustomerPitchData.FinalSA ? formatNumber(CustomerPitchData.FinalSA) : 'N.A'}</h1>
                                            }
                                        </li>
                                        {
                                            ([7, 1000].indexOf(productid) !== -1)  && [5,10,11,16].includes(CustomerPitchData.InsurerID) ? 
                                                <li>
                                                    Income Proof Document
                                                <h1 className="green">Waived-Off</h1>
                                                </li>
                                            :
                                            <li>
                                            Kyc Waiver
                                            <h1 className={(CustomerPitchData.KycWaiver !== undefined) ? parseInt(CustomerPitchData.KycWaiver) === 1 ? "green" : "red" : ""}>{(CustomerPitchData.KycWaiver !== undefined) ? parseInt(CustomerPitchData.KycWaiver) === 1 ? "Yes" : "No" : 'N.A'}</h1>
                                            </li>
                                        }
                                        {
                                            !props.ISBajajSelf ? 
                                            ([7, 1000].indexOf(productid) !== -1)  && [5,10,11,16].includes(CustomerPitchData.InsurerID) ? 
                                                <li>
                                                    KYC of Wife
                                                <h1 className="green">Required</h1>
                                                </li>
                                            :
                                            <li>
                                            Medical Waiver
                                            <h1 className={(CustomerPitchData.MedicalWaiver !== undefined) ? parseInt(CustomerPitchData.MedicalWaiver) === 1 ? "green" : "red" : ""}>{(CustomerPitchData.MedicalWaiver !== undefined) ? parseInt(CustomerPitchData.MedicalWaiver) === 1 ? "Yes" : "No" : 'N.A'}</h1>
                                            </li>
                                            :
                                            <></>
                                        }
                                    </ul>
                                    </>
                            }
                        </>
                        :
                        <div className="CustomerEligibleTableMax">
                            <ul>
                                <li>
                                    Sum Assured Allowed
                                    <h1>{'50 LAKH'}</h1>
                                </li>
                                <li>
                                    Financial Waiver
                                    <h1>{'YES'}</h1>
                                </li>
                            </ul>
                        </div>
                    }
                </div>
            </ModalPopup >
        </>
    )
}