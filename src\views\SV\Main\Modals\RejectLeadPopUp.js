import React, { Fragment, useEffect, useState } from "react";
import ModalPopup from '../../../../components/Dialogs/ModalPopup';
import { Autocomplete, Grid, TextField } from "@mui/material";
import User from "../../../../services/user.service";
import rootScopeService from "../../../../services/rootScopeService";
import { CALL_API } from "../../../../services";
import { useSelector, useDispatch } from "react-redux";
import { useSnackbar } from "notistack";
import { SelectDropdown, TextInput } from "../../../../components";
import Common, { getCurrentTalkTime } from "../../../../services/Common";
import { SV_CONFIG } from "../../../../appconfig";
import { DatePicker } from "../../../../components/DatePicker";
import masterService from "../../../../services/masterService";
import { UpdateAppointmentStatusService } from "../../../Features/FosHelpers/fosServices";
import { gaEventTracker } from "../../../../helpers";
import { GetSubStatusV2, GetRejectLeadsInput, GetRejectLeads, SetCustomerComment } from "../../../../services/Common";
import { default as Data } from "../../../../../src/assets/json/StaticData";
import Textarea from '@mui/material/TextareaAutosize';
import CrossSellModal from './CrossSellModal';
import ErrorBoundary from "../../../../hoc/ErrorBoundary/ErrorBoundary";
import { updateStateInRedux } from "../../../../../src/store/actions/SalesView/SalesView";
import { GetCurrentCallTime } from "../../../Features/FosHelpers/fosCommonHelper";

export const RejectLeadPopUp = (props) => {
    const { enqueueSnackbar } = useSnackbar();
    const [ReasonForRejection, setReasonForRejection] = useState([]);
    const [RejectAll, setRejectAll] = useState(false);
    // let [IsVisible, setIsVisible] = useState(true);
    let [ContactDay, setContactDay] = useState(0);
    let [ContactMonth, setContactMonth] = useState(0);
    let [Months, setMonths] = useState([]);
    let [StatusId, setStatusId] = useState(null);
    let [leadsourceId, setleadsourceId] = useState(0);
    let [SubStatusId, setSubStatusId] = useState(0);
    let [allChecked, setallChecked] = useState(false);
    let [AllLeads, setAllLeads] = useState([]);
    let [LeadSubStatus, setLeadSubStatus] = useState(0);
    const [ReasonInput, setReasonInput] = useState(0);
    const [caseDate, setCaseDate] = useState(null);
    const [properties, setProperties] = useState([]);
    const [propertiesInput, setPropertiesInput] = useState({});
    const [suppliers, setSuppliers] = useState([]);
    const [SubProductsList, setSubProductsList] = useState([]);
    const [selectedCheckboxProperty, setSelectedCheckboxProperty] = useState({});
    const RejectAllSubstatus = SV_CONFIG["RejectAllSubstatus"][SV_CONFIG["environment"]];
    const isRejectAllLeadsMandatory = RejectAllSubstatus.includes(ReasonInput);
    const [IsParentLeadSelected, setIsParentLeadSelected] = useState(false)
    const [IsPitch, setIsPitch] = useState("");
    const [IsAppointmentAvailable, setIsAppointmentAvailable] = useState("");
    let [IsRejectAllDisabled, setIsRejectAllDisabled] = useState(false);
    const [SmeRejectionSubReasons, setSmeRejectionSubReasons] = useState([]);
    let Languages = Data.Languages;
    const [comment, setComment] = useState('');
    const [textCount, setTextCount] = useState('0');
    const [showCrossSellModal, setShowCrossSellModal] = useState(false);
    let CrossSellRefLeadId = useSelector(state => state.salesview.CrossSellRefLeadId);
    const reduxDispatch = useDispatch();

    let leadx = "";
    let _flag = false;
    let Rleads = [];
    let selectedLeadId = props.LeadId;
    let lead = props.lead || {};
    const productId = rootScopeService.getProductId();
    let [allLeads, parentLeadId, CallRestrictRejection, FOSCity, AppointmentData, next5leads] = useSelector(state => {
        let { allLeads, parentLeadId, CallRestrictRejection, FOSCity, AppointmentData, next5leads } = state.salesview;
        return [allLeads, parentLeadId, CallRestrictRejection, FOSCity, AppointmentData, next5leads]
    });

    const supplierList = Array.isArray(suppliers) ? suppliers.filter((supplier) => {
        let valid = true;
        valid = valid && (supplier.ProductId == productId || 0);
        if ([131, 3, 154, 139].indexOf(productId) === -1) {
            valid = (valid && (supplier.SubCategoryId == lead.SubProductTypeId));
        }
        return valid;
    }) : [];

    const validateProperty = (item, isSubmit = false) => {
        let valid = true, errorMsg = '';
        try {
            if (isSubmit) item.value = item.value.trim();
        }
        catch { }

        if (item.ValidationType === "numeric" && item.value != (+item.value)) {
            errorMsg += `${item.PropertyName} must be numeric \n`;
            valid = false;
        }

        if (isSubmit && item.PropertyType === 'checkbox') {
            valid = false;
            Object.keys(selectedCheckboxProperty).forEach((key) => {
                if (selectedCheckboxProperty[key] !== undefined) {
                    valid = true
                }
            })
            if (!valid) errorMsg += `${item.PropertyName} is Mandatory \n`;

        }
        if (isSubmit && ['checkbox'].indexOf(item.PropertyType) === -1 && !item.value) {
            // if (isSubmit && !item.value && item.PropertyType === 'textbox') {
            errorMsg += `${item.PropertyName} is Mandatory \n`;
            valid = false;
        }

        return { valid, errorMsg };
    }
    const getSelectItemThat = (id) => {
        for (var i = 1; i <= 2; i++) {
            document.getElementById(i).checked = false;
        }
        document.getElementById(id).checked = true;
        id == 1 ? setIsPitch(true) : setIsPitch(false)

    }
    const handleChange = (e, isLeadSelection = false) => {
        let e_name = e.target.name;
        let e_val = e.target.value;
        if (e_name === "ReasonForRejection") {
            setComment('');
            const prevReasonValue = ReasonInput;
            try { e_val = parseInt(e_val, 10); }
            catch { }
            if ([11, 184, 1542, 1543].indexOf(e_val) !== -1) {
                if (Months.length < 2) {
                    GetFutureMonths(10);
                }
            }
            setLeadSubStatus(e_val);
            setReasonInput(e_val);
            // set properties for selected input
            let data =
                Array.isArray(ReasonForRejection)
                && ReasonForRejection.find(reason => reason.SubStatusID == e_val);
            data = data && data.properties;
            if (Array.isArray(data)) {
                setProperties(data);
                let _propertiesInput = {};
                if (data) {
                    data.forEach((d) => {
                        let { PropertyId, PropertyName, ValidationType, PropertyType } = d;
                        _propertiesInput[PropertyId] = {
                            PropertyId,
                            PropertyName,
                            PropertyType,
                            value: '',
                            textValue: '',
                            ValidationType
                        }
                    });
                }
                setPropertiesInput(_propertiesInput);
            }
            // all lead rejection is mandatory for
            // Interested in other product - Other Corporate Product
            if (RejectAllSubstatus.includes(e_val)) {
                selectAndDisableAllLeads();
            }

            if (RejectAllSubstatus.includes(prevReasonValue) && !RejectAllSubstatus.includes(e_val)) {
                // choose current lead and enable lead selection
                selectCurrentLeadOnly();
            }

            if (productId == 131) {
                setSmeRejectionSubReasons([]);
            }
        }
        else if (e_name === "ContactDay") {
            setContactDay(e_val);
        }
        else if (e_name === "ContactMonth") {
            setContactMonth(e_val);
        }

        let itemName = e.target.name;
        let checked = e.target.checked;

        if (isLeadSelection) {
            if (isRejectAllLeadsMandatory) {
                enqueueSnackbar('Rejecting all is mandatory, for this reason', {
                    autoHideDuration: 3000,
                    variant: "error",
                    style: { whiteSpace: "pre-line" }
                });
                return;
            }

            let [_AllLeads, _allChecked] = [[], []];
            if (itemName === "checkAll") {
                _allChecked = checked;
                _AllLeads = AllLeads.map(item => ({ ...item, Selected: checked }));
                setRejectAll(true);
            } else {
                _AllLeads = AllLeads.map(item =>
                    item.LeadID == itemName ? { ...item, Selected: checked } : item
                );
                _allChecked = _AllLeads.every(item => item.Selected);
                setRejectAll(true);
            }

            _AllLeads.filter(item => item.LeadID === parentLeadId && item.Selected).length > 0 ? setIsParentLeadSelected(true) : setIsParentLeadSelected(false);
            if (_AllLeads.filter(item => item.LeadID === parentLeadId && item.Selected).length === 0) {
                setIsPitch("");
            }
            setAllLeads(_AllLeads);
            setallChecked(_allChecked)
        }
    };

    const handleChangeForProperties = (e, PropertyId, PropertyName, ValidationType, PropertyType) => {
        let value = e.target.value;
        let updatedProperty = {
            PropertyId,
            PropertyName,
            value,
            PropertyType,
            ValidationType
            // textValue:
        }
        let validationResult = validateProperty(updatedProperty, false);
        if (!validationResult.valid) {
            enqueueSnackbar(validationResult.errorMsg, {
                variant: 'error',
                autoHideDuration: 3000,
                style: { whiteSpace: 'pre-line' }
            });
            return;
        }
        setPropertiesInput({
            ...propertiesInput,
            [PropertyId]: updatedProperty
        });
    }
    useEffect(() => {
        if (props.open) {
            if (rootScopeService.getProductId() === 106) {
                setIsRejectAllDisabled(true);
            }
            getLeads();
            props.LeadId === parentLeadId ? setIsParentLeadSelected(true) : setIsParentLeadSelected(false);
            setIsPitch("");
            IsAppointmentDone(parentLeadId).then((response) => {
                setIsAppointmentAvailable(response)
            })
            // getRejectStatusDetails();
            if (RejectAllSubstatus.includes(ReasonInput)) {
                selectAndDisableAllLeads();
            }
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [props.open]);

    useEffect(() => {
        //will execute when leadsourceId is changed/updated
        if (leadsourceId !== 0 && StatusId !== null) {

            getRejectStatusDetails();
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [leadsourceId, StatusId])

    useEffect(() => {
        let textLength = comment.trim().length;
        setTextCount(textLength);
        if (textLength > 150) {
            enqueueSnackbar("Maximum 150 characters are allowed for the comments", { variant: 'error', autoHideDuration: 3000, });
            let tempText = comment.substring(0, 150);
            setComment(tempText);
            setTextCount(tempText.length);
        }
    }, [comment])

    useEffect(() => {
        if (LeadSubStatus && LeadSubStatus === 2429) {
            setShowCrossSellModal(true);
        }
    }, [LeadSubStatus])

    const getRejectStatusDetails = () => {
        // let statusId = getLeads();
        GetSubStatusData(StatusId);

    }

    const getLeads = () => {
        setAllLeads(allLeads.filter((vdata, key) => {
            return (
                [1, 2, 3, 4, 11].indexOf(vdata.StatusId) !== -1
            )
        }));
        let _statusId = '';

        if (CallRestrictRejection) {
            setRejectAll(true);
            if (AllLeads.length === 1) {
                //setIsVisible(false);
            }
        }
        else {
            if (AllLeads.length === 1) {
                // setIsVisible(false);
                setRejectAll(true);

                allLeads.forEach((gleads) => {
                    if (gleads.LeadID === selectedLeadId) {
                        setStatusId(gleads.StatusId);
                        _statusId = gleads.StatusId
                        setleadsourceId(gleads.LeadSourceId);
                        setSubStatusId(gleads.SubStatusId);
                        gleads.Selected = true;
                    }
                    else {
                        gleads.Selected = false;
                    }
                });
            }
            else {
                //setIsVisible(false);
                allLeads.forEach((gleads) => {
                    if (gleads.LeadID === selectedLeadId) {
                        setStatusId(gleads.StatusId);
                        _statusId = gleads.StatusId
                        setleadsourceId(gleads.LeadSourceId);
                        setSubStatusId(gleads.SubStatusId);
                        gleads.Selected = true;
                    }
                    else {
                        gleads.Selected = false;
                    }
                });
            }
        }
        return _statusId;
    };

    const GetSubStatusData = (statusId) => {
        let StatusId = GetRejectionStatus(statusId);
        let ProductId = rootScopeService.getProductId();
        GetSubStatusV2(StatusId, ProductId, leadsourceId, User.RoleId, User.UserId).then((result) => {
            if (result && Array.isArray(result)) {
                let externalSourcesLeadSourceId = 67;
                let didNotVisitSubStatusID = 47;

                if (rootScopeService.getProductId() === 131 && leadsourceId === externalSourcesLeadSourceId) {
                    result = result.filter((r) => r.SubStatusID !== didNotVisitSubStatusID);
                }

                let reason = GetReason();

                if (reason) {
                    setReasonForRejection([{ StatusMode: "N", SubStatusID: 0, SubStatusName: "--Select--", properties: [] }, reason, ...result]);
                }
                else {
                    setReasonForRejection([{ StatusMode: "N", SubStatusID: 0, SubStatusName: "--Select--", properties: [] }, ...result]);
                }
            }
            else {
                setReasonForRejection([]);
            }
            // if (rootScopeService.getCallRestrictRejection())
            let CallRestrictRejection = '';
            if (CallRestrictRejection) { setLeadSubStatus(1454); }
            else {
                setLeadSubStatus(0);
                // commenting below code as it is used for reopen
                // if (SubStatusId > 0) {
                //     setLeadSubStatus(SubStatusId);
                // }
            }
        });
    }

    const GetFutureMonths = function (n) {

        let months = [];
        let today = new Date();
        today.setDate(today.getDay() + 60);
        // let year = today.getFullYear();
        let month = today.getMonth() + 1;
        // let monthName;
        let arrMonth = ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"];
        for (let i = 0; i < n; i++) {

            let items = {
                Id: String(month - 1),
                Name: arrMonth[month - 1]
            };

            months.push(items);
            if (month === 12) {
                month = 1;
            }
            else {
                month++;
            }
        }
        setMonths(months);
    };

    const GetRejectionStatus = function (val) {
        switch (val) {
            case 1:
                return 5;
            case 2:
                return 6;
            case 3:
                return 7;
            case 4:
                return 12;
            case 11:
                return 14;
            case 13:
                return 16;
            default:
                return;
        }
    };

    const GetReason = () => {
        let reason = null;
        if (rootScopeService.getProductId() == 131) {
            reason = { StatusMode: "N", SubStatusID: 2235, SubStatusName: "Not a business Owner", properties: [] }
        }
        return reason;
    };

    const GetRejectionSubReasons = (productId, subStatusID) => {
        const input = {
            url: `api/SalesView/GetReasonMaster/${productId}/${subStatusID}`,
            method: 'GET', service: 'MatrixCoreAPI',
        };
        return CALL_API(input);
    }

    const IsAppointmentDone = (ParentId) => {
        const input = {
            url: `api/SalesView/IsAppointmentDone/${ParentId}/`,
            method: 'GET', service: 'MatrixCoreAPI',
        };
        return CALL_API(input);
    }
    const SaveFosPitchedData = (reqData) => {
        const _input = {
            url: `api/SalesView/SaveFosPitchedData`,
            method: 'POST', service: 'MatrixCoreAPI',
            requestData: reqData,
            timeout: "l"
        }
        return CALL_API(_input)
    };

    const ValidateRejection = async () => {
        let rejectLead = [];
        let IsCancelAppointment = false;
        console.log("Appointment Data", AppointmentData)
        let index = 0;

        if (productId === 131 && !(allChecked || (AllLeads && AllLeads.some(lead => lead.Selected)))) {
            enqueueSnackbar("Please select the Lead Id for rejection", {
                variant: 'error',
                autoHideDuration: 3000,
            });
            return false;
        }
        if (LeadSubStatus === undefined || LeadSubStatus === 0 || LeadSubStatus === '') {
            enqueueSnackbar("Please select rejection reason", {
                variant: 'error',
                autoHideDuration: 3000,
            });
            return false;
        }
        //For SME reject reason comment for interested leads excluding [1,2,3,4]
        if (isSMEInterestedLead()) {
            if (comment && comment.trim().length > 0) {

            }
            else {
                enqueueSnackbar("Please mention rejection reason in the comment box.", {
                    variant: 'error',
                    autoHideDuration: 3000,
                });
                return false;
            }
        }

        if (IsPitch === "" && IsParentLeadSelected && FOSCity && [2, 115, 7, 1000].indexOf(productId) > -1 && IsAppointmentAvailable === false && User && User.RoleId === 13) {
            enqueueSnackbar("Please submit your response if FOS pitched or not.", {
                variant: 'error',
                autoHideDuration: 3000,
            });
            return false;
        }
        //Covid
        if ([2024, 2025, 2026, 2027, 2028].indexOf(LeadSubStatus) !== -1) {
            if ([undefined, null, 0, "", "0"].indexOf(caseDate) !== -1) {
                alert("Select Covid Case Date");
                return false;
            }
            else {
                try {
                    let covidCaseDate = new Date(caseDate);
                    if (covidCaseDate === "Invalid Date") {
                        alert("Invalid Covid Date");
                        return false;
                    }
                }
                catch (error) {
                    alert("Invalid Covid Date");
                    return false;

                }
            }
        }
        //For Future Prospect
        if ([11, 184, 1542, 1543].indexOf(LeadSubStatus) !== -1) {
            var currentMonth = String(ContactMonth);

            if (currentMonth === 0) {
                enqueueSnackbar("Please select month", { variant: 'error', autoHideDuration: 3000, });
                return false;
            }
            //else if ($scope.ContactDay != "") {
            else if (ContactDay !== "") {
                if (isNaN(ContactDay)) {
                    enqueueSnackbar("Enter Valid Day", { variant: 'error', autoHideDuration: 3000, });
                    return false;
                }
                else if (['0', '2', '4', '6', '7', '9', '11'].indexOf(currentMonth) !== -1) {
                    if (ContactDay >= 32) {
                        enqueueSnackbar("Please enter value between (01-31) in contact day", { variant: 'error', autoHideDuration: 3000, });
                        return false;
                    }
                }
                else if (['3', '5', '8', '10'].indexOf(currentMonth) !== -1) {
                    if (ContactDay >= 31) {
                        enqueueSnackbar("Please enter value between (01-30) in contact day", { variant: 'error', autoHideDuration: 3000, });
                        return false;
                    }
                }
                else if (currentMonth === "1") {
                    var year; var month = new Date().getMonth() + 1;
                    if (month >= "09") {
                        year = new Date().getFullYear() + 1;
                    }
                    else {
                        year = new Date().getFullYear();
                    }
                    var isLeap;
                    isLeap = !(new Date(year, 1, 29).getMonth() - 1)
                    if (isLeap) {
                        if (ContactDay >= 30) {
                            enqueueSnackbar("Please enter value between (01-29) in contact day", { variant: 'error', autoHideDuration: 3000, });
                            return false;
                        }
                    }
                    else {
                        if (ContactDay >= 29) {
                            enqueueSnackbar("Please enter value between (01-28) in contact day", { variant: 'error', autoHideDuration: 3000, });
                            return false;
                        }
                    }
                }
            }
        }
        //Properties
        if (Object.keys(propertiesInput)) {
            let valid = true, msg = '';
            Object.keys(propertiesInput).forEach((key) => {
                let obj = propertiesInput[key];
                let validationResult = validateProperty(obj, true);
                valid = valid && validationResult.valid;
                msg += validationResult.errorMsg;
            });
            if (!valid && msg) {
                enqueueSnackbar(msg, {
                    variant: 'error',
                    autoHideDuration: 3000,
                    style: { whiteSpace: 'pre-line' }
                });
                return;
            }
        }
        //Get all selected leads
        AllLeads.forEach((gleads) => { if (gleads.Selected) rejectLead.push(gleads.LeadID); });
        if (AllLeads.length === rejectLead.length) {
            if (!!AppointmentData && [2124, 2193].indexOf(AppointmentData.subStatusId) > -1) {
                window.alert("Oops! " +
                    "There is an ongoing activity happening on the appointment, please close the appointment activity first to reject the lead")
                return false;
            }
            else if (!!AppointmentData) {

                let e = window.confirm("Attention!! An appointment is already active on this lead, Do you still want to reject it?  " +
                    "Click OK to Reject the lead.")
                if (e) {
                    IsCancelAppointment = true;
                }
                else {
                    IsCancelAppointment = false;
                    return false;
                }
            }

        }

        for (var i = 0; i < rejectLead.length; i++) {
            //index = functiontofindIndexByKeyValue(allLeads, "LeadID", rejectLead[i]);
            AddLeads(rootScopeService.getProductId(), index);
            if (Rleads.length > 1) {
                _flag = true;
            };
            Rleads = [];
        };

        if (productId === 131) {
            if ([2429].indexOf(LeadSubStatus) !== -1) {
                if (CrossSellRefLeadId && (CrossSellRefLeadId == parentLeadId)) {
                    // Valid CrossSell
                }
                else {
                    setShowCrossSellModal(true);
                    enqueueSnackbar("Please create Cross Sell Lead first", { variant: 'error', autoHideDuration: 3000, });
                    return false;
                }
            }

            try {
                if (User && User.RoleId === 13) {
                    const talktime = GetCurrentCallTime(next5leads, parentLeadId);
                    const duration = getCurrentTalkTime();

                    if ((talktime >= 45) || (duration >= 60)) {
                        // Lead can be rejected
                    }
                    else {
                        let res = await IsValidRejection(productId, parentLeadId);
                        if (res && !res.IsValid && res.ErrorMessage) {
                            enqueueSnackbar(res.ErrorMessage, { variant: 'error', autoHideDuration: 3000, });
                            return false;
                        }
                    }
                }
            }
            catch { }
        }

        if (_flag) {
            let e = window.confirm("Do you want to reject all searches for this lead?")

            if (e) {
                RejectLeads(true, IsCancelAppointment);
            } else {
                RejectLeads(false, IsCancelAppointment);
            }

        } else {
            RejectLeads(false, IsCancelAppointment);
        }
    };

    const RejectLeads = function (flag, IsCancelAppointment = false) {
        let LeadIds = '';
        let futureProspectdate = 0;
        let ReasonData = [];
        if ([11, 184, 1542, 1543].indexOf(LeadSubStatus) !== -1) {
            let day = ContactDay;
            if (!day) {
                day = 1;
            }
            let month = parseInt(ContactMonth);
            let currentYear = (new Date()).getFullYear();
            let currentMonth = parseInt((new Date()).getMonth() + 1);

            if (month < currentMonth) {
                currentYear = currentYear + 1;
            }
            futureProspectdate = new Date(currentYear, month, day, 0, 0);
            futureProspectdate = Date.parse(futureProspectdate);
        }
        else {
            futureProspectdate = caseDate ? caseDate.getTime() : 0;
        }

        if (propertiesInput) {
            Object.keys(propertiesInput) && Object.keys(propertiesInput).forEach(key => {
                let data = { ...propertiesInput[key] };
                if (propertiesInput[key].PropertyType === "dropdown") {
                    switch (propertiesInput[key].PropertyName) {
                        case "Insurers":
                            data.value = propertiesInput[key] && propertiesInput[key].value
                                && propertiesInput[key].value.OldSupplierId;
                            data.textValue = propertiesInput[key] && propertiesInput[key].value
                                && propertiesInput[key].value.SupplierDisplayName;
                            break;
                        case "CrossSell":
                            data.value = propertiesInput[key] && propertiesInput[key].value
                                && propertiesInput[key].value.ID;
                            data.textValue = propertiesInput[key] && propertiesInput[key].value
                                && propertiesInput[key].value.Name;
                            break;
                        case 'Rejection Sub Reason':
                            if ([2343, 1621, 1354].indexOf(LeadSubStatus) !== -1) {
                                data.value = propertiesInput[key] && propertiesInput[key].value
                                    && propertiesInput[key].value.ID;
                                data.textValue = propertiesInput[key] && propertiesInput[key].value
                                    && propertiesInput[key].value.Name;
                            }
                            if ([1572, 2234, 1355, 1749, 2329, 2362].indexOf(LeadSubStatus) !== -1) {
                                data.value = propertiesInput[key] && propertiesInput[key].value
                                    && propertiesInput[key].value.ReasonId;
                                data.textValue = propertiesInput[key] && propertiesInput[key].value
                                    && propertiesInput[key].value.Reason;
                            }
                            if ([2429].indexOf(LeadSubStatus) !== -1) {
                                data.value = propertiesInput[key] && propertiesInput[key].value
                                    && propertiesInput[key].value.ProductId;
                                data.textValue = propertiesInput[key] && propertiesInput[key].value
                                    && propertiesInput[key].value.ProductName;
                            }
                            break;
                        default:
                            break;
                    }
                }
                if (propertiesInput[key].PropertyType === "checkbox") {
                    Object.keys(selectedCheckboxProperty).forEach((key1) => {
                        let _data = {
                            ...propertiesInput[key],
                            value: selectedCheckboxProperty[key1].id,
                            textValue: selectedCheckboxProperty[key1].label
                        };
                        ReasonData.push(_data);
                    });
                }
                else {
                    ReasonData.push(data);
                }
            });
        }

        if (flag) {
            LeadIds = leadx;
        } else {
            LeadIds = "";
            AllLeads.forEach((gleads) => {
                if (gleads.Selected)
                    LeadIds = gleads.LeadID + "," + LeadIds;
            });
        };
        if (SV_CONFIG["RejectionAlertProdIds"][SV_CONFIG["environment"]].indexOf(rootScopeService.getProductId()) !== -1) {
            let GroupId = User.GroupId;
            if (User.RoleId !== 13) {
                RejectLeadinDB(LeadIds, futureProspectdate, RejectAll, ReasonData, IsCancelAppointment);
            }
            else if (User.RoleId === 13 && SV_CONFIG["RejectionAlertGroupIds"][SV_CONFIG["environment"]].indexOf(GroupId) !== -1) {
                RejectLeadinDB(LeadIds, futureProspectdate, RejectAll, ReasonData, IsCancelAppointment);
            }
            else {
                GetRejectionMessage(LeadIds, futureProspectdate, RejectAll, ReasonData, IsCancelAppointment);
            }
        }
        else {// means for other product
            RejectLeadinDB(LeadIds, futureProspectdate, RejectAll, ReasonData, IsCancelAppointment);
        }
    }

    const RejectLeadinDB = (LeadIds, futureProspectdate, RejectAll, ReasonData, IsCancelAppointment = false) => {
        let productId = rootScopeService.getProductId();
        let response = GetRejectLeadsInput(LeadIds, LeadSubStatus, productId, User.UserId, futureProspectdate, RejectAll, ReasonData);
        GetRejectLeads(response.input, response.isCoreApi).then((resultData) => {
            if (resultData) {
                /*notification*/
                let _reqData = {
                    parentID: parentLeadId,
                    UserId: User.UserId,
                    action: 6
                };
                let _reqDataPitch = {
                    LeadId: parentLeadId,
                    UserId: User.UserId,
                    Response: IsPitch,
                    Type: 1
                };
                if (IsPitch !== "" && IsParentLeadSelected && FOSCity && [2, 115, 7, 1000].indexOf(productId) > -1 && IsAppointmentAvailable === false && User && User.RoleId === 13) { SaveFosPitchedData(_reqDataPitch); }
                Common.SetNotificationAction(_reqData);

                if (IsCancelAppointment && resultData && resultData.Data) {
                    let gaDump = {};

                    try {
                        gaDump.rejectRequest = response.input;
                        gaDump.rejectResponse = resultData;
                        gaDump = JSON.stringify(gaDump);
                        gaEventTracker('APPOINTMENT_CANCEL_REJECT_LEAD', gaDump, User.UserID);
                    } catch (e) { }

                    const AppData = {
                        LeadID: parentLeadId,
                        UserID: User.UserId,
                        SubStatusId: 2004,
                        Source: "matrix",
                        CancelReasonId: 12
                    }
                    UpdateAppointmentStatusService(AppData).then(() => {
                        //fnSaveAppCancelReason();
                        enqueueSnackbar("Appointment Cancelled successfully", {
                            variant: 'success',
                            autoHideDuration: 3000,
                        })
                    }).catch(() => {
                        console.log("Error occured in Appointment Cancellation")
                    });
                }
                /*notification*/
                enqueueSnackbar("Lead Rejected Successfully!", {
                    variant: 'success',
                    autoHideDuration: 3000,
                });
                //To save comment of rejected interested lead in db
                if (productId == 131 && StatusId == 4 && isSMEInterestedLead()) {
                    SaveComment(comment)
                    enqueueSnackbar("Comment added successfully", {
                        variant: "success",
                        autoHideDuration: 3000,
                    });
                }

                CreateCrossSellLead(ReasonData);
                props.handleClose();
                props.getLeads();
            }
            else {
                enqueueSnackbar("Something went wrong, Please Connect to Support team.", {
                    variant: 'success',
                    autoHideDuration: 3000,
                });
            }
        });
    }

    const CreateCrossSellLead = (reasonData) => {
        try {
            if (productId == 131 && LeadSubStatus == 2343) {
                if (CrossSellRefLeadId && (CrossSellRefLeadId == parentLeadId)) {
                    // Valid CrossSell
                }
                else {
                    CreateCrossSellLeadService(GetRequestDataCreateLead(reasonData)).then((response) => {
                        if (response && response.IsSaved) {
                            enqueueSnackbar("Lead created " + response.Message, {
                                variant: 'success',
                                autoHideDuration: 4000,
                            });
                        } else {
                            enqueueSnackbar(response.Message, {
                                variant: 'success',
                                autoHideDuration: 4000,
                            });
                        }

                        reduxDispatch(updateStateInRedux({ key: "CrossSellRefLeadId", value: parentLeadId }));
                    })
                }
            }
        }
        catch (e) { }
    }

    const GetRequestDataCreateLead = (reasonData) => {
        var json = {
            CustomerId: rootScopeService.getCustomerId(),
            LeadId: selectedLeadId,
            CoreLeadId: -1,
            NeedId: -1,
            ProductID: 131,
            ProductName: 'Corporate Insurance',
            UserId: User.UserId,
            Comments: '',
            UTMsource: '',
            UTMMedium: 'CrosssellOnRejection',
            ProductType: reasonData[0].value
        }
        return json
    }

    const CreateCrossSellLeadService = (requestData) => {
        const input = {
            url: `coremrs/api/MRSCore/CreateCrossSellLead`,
            method: 'POST', service: 'MatrixCoreAPI',
            requestData
        }
        return CALL_API(input).then((response) => {
            return response;
        })
    }

    const IsValidRejection = async (productId, leadId) => {
        const input = {
            url: `coremrs/api/MRSCore/IsValidRejection/${productId}/${leadId}`,
            method: 'GET',
            service: 'MatrixCoreAPI'
        }
        return CALL_API(input);
    }

    const SaveComment = (Comment) => {
        let UserId = User.UserId;
        var requestData = {
            "CustomerId": rootScopeService.getCustomerId(),
            "ProductId": rootScopeService.getProductId(),
            parentLeadId,
            UserId,
            "Comment": Comment,
            "EventType": 69
        }

        SetCustomerComment(requestData);
    };

    const GetRejectionMessage = function (LeadIds, futureProspectdate, RejectAll, ReasonData, IsCancelAppointment = false) {
        let Leadarray = LeadIds.split(',');// as it contains duplicate
        let uniqueLeadId = new Set();
        Leadarray.forEach(function (lead) {
            if (lead && lead !== '')
                uniqueLeadId.add(lead);
        });

        var reqRejectionAlert = {
            ParentId: parentLeadId,
            LeadsToReject: Array.from(uniqueLeadId),
            UserID: User.UserId
        };

        GetRejectionMessageService(reqRejectionAlert).then(function (resultdata) {
            if (resultdata) {
                if (resultdata.IsAlertShow) {// rule matched
                    var message = "";
                    if (resultdata.Message != null && resultdata.Message.length > 0) {
                        resultdata.Message.forEach(function (msg) {
                            message = message + msg + '\n';
                        });
                        //remove html tags
                        message = message && message.replace(/(<([^>]+)>)/ig, '');
                    }
                    if (resultdata.IsRejectionAlert) {// option to reject
                        message += "Click 'OK' to reject leads"

                        if (window.confirm(message) !== false) {
                            RejectLeadinDB(LeadIds, futureProspectdate, RejectAll, ReasonData, IsCancelAppointment);
                        }
                    }
                    else {//onALertMessage that Leads Cant be rejected
                        window.alert(message)
                    }
                    props.handleClose();
                }
                else {// No rule Matched for Alert Message
                    RejectLeadinDB(LeadIds, futureProspectdate, RejectAll, ReasonData, IsCancelAppointment);
                }
            }
            else {
                RejectLeadinDB(LeadIds, futureProspectdate, RejectAll, ReasonData, IsCancelAppointment);
            }
        });
    }

    const GetRejectionMessageService = (requestData) => {
        const input = {
            url: "coremrs/api/LeadRejection/GetRejectionAlert",
            method: 'POST',
            service: "MatrixCoreAPI",
            requestData
        };
        return CALL_API(input);
    }

    const AddLeads = (productId, index) => {
        AllLeads.forEach((vdata) => {
            if (productId === 7) {
                if (vdata.Age === AllLeads[index].Age) {
                    AddLeadToarr(vdata.StatusId, vdata.LeadID);
                }
            }
            else if (productId === 115) {
                if ((vdata.Age + '-' + vdata.SubProductTypeId) === (allLeads[index].Age + '-' + allLeads[index].SubProductTypeId)) {
                    AddLeadToarr(vdata.StatusId, vdata.LeadID);
                }
            }
        });
    }

    const AddLeadToarr = (StatusId, LeadID) => {
        if ([1, 2, 3, 4, 11].indexOf(StatusId) !== -1) {
            Rleads.push(LeadID);
            leadx = LeadID + "," + leadx;
        }
    }
    const getOptionsList = (name) => {
        let optionsList = [];
        let labelKey = 'Name', valueKey = 'ID';
        switch (name) {
            case 'Insurers':
                if (!Array.isArray(suppliers) || suppliers.length === 0) {
                    masterService.getSuppliers().then(res => {
                        setSuppliers(res);
                    })
                }
                labelKey = 'SupplierDisplayName';
                valueKey = '_all'
                optionsList = supplierList;
                break;
            case 'CrossSell':
                if (!Array.isArray(SubProductsList) || SubProductsList.length === 0) {
                    masterService.getSubProductByProductID(productId).then(res => {
                        setSubProductsList(res);
                    });
                }
                labelKey = 'Name';
                valueKey = '_all'
                optionsList = SubProductsList;
                break;
            case 'Rejection Sub Reason':
                if (([1572, 2234, 1355, 1749, 2329, 2362].indexOf(LeadSubStatus) !== -1) && (!Array.isArray(SmeRejectionSubReasons) || SmeRejectionSubReasons.length === 0)) {
                    GetRejectionSubReasons(productId, LeadSubStatus).then((result) => {
                        setSmeRejectionSubReasons(result);
                    });
                }
                if (([2343, 1354].indexOf(LeadSubStatus) !== -1) && (!Array.isArray(SubProductsList) || SubProductsList.length === 0)) {
                    masterService.getSubProductByProductID(productId).then(res => {
                        setSubProductsList(res);
                    });
                }
                if ([2343, 1354].indexOf(LeadSubStatus) !== -1) {
                    labelKey = 'Name';
                    valueKey = '_all'
                    optionsList = SubProductsList;
                }
                if ([1572, 2234, 1355, 1749, 2329, 2362].indexOf(LeadSubStatus) !== -1) {
                    labelKey = 'Reason';
                    valueKey = '_all'
                    optionsList = SmeRejectionSubReasons;
                }
                if ([1621].indexOf(LeadSubStatus) !== -1) {
                    labelKey = 'Name';
                    valueKey = '_all'
                    optionsList = Languages;
                }
                break;
            default:
                break;
        }
        optionsList = Array.isArray(optionsList) ? optionsList : [];
        return { optionsList, labelKey, valueKey };
    }
    const getCheckboxOptions = (name) => {
        let options = require("../../../../assets/json/RejectionPropertiesData").default;
        return options[name];
    }
    const handleCheckboxChange = (e, option) => {
        //handle checkbox property
        let checked = e.target.checked;
        if (checked) {
            setSelectedCheckboxProperty({ ...selectedCheckboxProperty, [option.id]: option })
        }
        else {
            setSelectedCheckboxProperty({ ...selectedCheckboxProperty, [option.id]: undefined })
        }
    }
    const getInputByProperty = (property) => {
        let { PropertyId, PropertyName, PropertyType, ValidationType } = property;
        let Component = null;
        let _handleChange = (e) => { handleChangeForProperties(e, PropertyId, PropertyName, ValidationType, PropertyType) }
        switch (PropertyType) {
            case 'textbox':
                Component = <TextInput
                    name={PropertyName} // id
                    label={PropertyName}
                    value={propertiesInput[PropertyId] ? propertiesInput[PropertyId].value : ''}
                    handleChange={_handleChange}
                    show={true}
                    sm={12} md={12} xs={12}
                    maxLength={255}
                    key={PropertyId}
                />
                break;
            case 'dropdown':
                const { optionsList, labelKey, valueKey } = getOptionsList(PropertyName);
                Component = <>{(LeadSubStatus && productId == 131 && LeadSubStatus == 2343)
                    ?
                    <Grid key={"SMEAutoCompleteGrid"} item sm={12} md={12} xs={12}>
                        <Autocomplete
                            id={PropertyName}
                            name={PropertyName}
                            value={propertiesInput[PropertyId] ? propertiesInput[PropertyId].value : ''}
                            onChange={(event, value) => _handleChange({ target: { value: value } })}
                            options={optionsList}
                            getOptionLabel={(option) => (option[labelKey] || '')}
                            show={true}
                            renderInput={(params) =>
                                <TextField {...params}
                                    label={PropertyName}
                                    variant='outlined'
                                />}
                            key={PropertyId}
                        />
                    </Grid>
                    :
                    <SelectDropdown
                        name={PropertyName}
                        label={PropertyName}
                        value={propertiesInput[PropertyId] ? propertiesInput[PropertyId].value : ''}
                        options={optionsList}
                        labelKeyInOptions={labelKey}
                        valueKeyInOptions={valueKey}
                        handleChange={_handleChange}
                        show={true}
                        sm={12} md={12} xs={12}
                    />}
                </>
                break;
            case 'checkbox':
                const options = getCheckboxOptions(PropertyName);
                Component = <>
                    <Grid item>
                        <h3>{PropertyName}</h3>
                    </Grid>
                    {options.map((option) => (
                        <Grid key={option.id} item sm={12} md={12} xs={12}>
                            <Fragment key={option.id}>
                                <input
                                    type="checkbox"
                                    onChange={(e) => { handleCheckboxChange(e, option) }}
                                    checked={selectedCheckboxProperty[option.id] !== undefined}
                                    id={option.id} name={option.label}
                                    key={1}
                                />
                                <label key={2}>{option.label}</label>
                            </Fragment>
                        </Grid>
                    ))}
                </>

                break;
            default:
                break;
        }
        return Component;
    }

    const selectAndDisableAllLeads = () => {
        // auto select all leads and disable lead selection
        setallChecked(true);
        setAllLeads(AllLeads.map(item => ({ ...item, Selected: true })));
        setRejectAll(true);
    }
    const selectCurrentLeadOnly = () => {
        setAllLeads(AllLeads.map(item => ({ ...item, Selected: item.LeadID === selectedLeadId })));
        if (AllLeads.length !== 1) {
            setallChecked(false);
        }
    }
    //check for sme non eb lead for rejection reason comment box
    const isSMEInterestedLead = () => {
        try {
            if (StatusId && StatusId == 4 && rootScopeService.getProductId() == 131) {
                return true;
            }
            return false;
        }
        catch {
            return false;
        }
    }
    return (
        <>
            <ModalPopup open={props.open} title='Reject Lead(s)' handleClose={props.handleClose}>
                {<div className="rejectLeadPopop">
                    <Grid container spacing={2}>
                        <SelectDropdown
                            name="ReasonForRejection"
                            label="Reason for rejection"
                            value={ReasonInput === 0 ? '' : ReasonInput}
                            options={ReasonForRejection || []}
                            labelKeyInOptions='SubStatusName'
                            valueKeyInOptions='SubStatusID'
                            handleChange={handleChange}
                            show={true}
                            sm={12} md={12} xs={12}
                            native={true}
                        />

                        {([11, 184, 1542, 1543].indexOf(LeadSubStatus) !== -1 || [2024, 2024, 2025, 2026, 2027, 2028].indexOf(LeadSubStatus) !== -1)
                            &&
                            <>
                                <TextInput
                                    name="ContactDay"
                                    label="ContactDay"
                                    value={ContactDay}
                                    handleChange={handleChange}
                                    show={[11, 184, 1542, 1543].indexOf(LeadSubStatus) !== -1}
                                    sm={3} md={3} xs={12}
                                    maxLength={2}
                                />
                                <SelectDropdown
                                    name="ContactMonth"
                                    label="ContactMonth"
                                    value={ContactMonth}
                                    options={Months}
                                    labelKeyInOptions='Name'
                                    valueKeyInOptions='Id'
                                    handleChange={handleChange}
                                    show={[11, 184, 1542, 1543].indexOf(LeadSubStatus) !== -1}
                                    sm={6} md={6} xs={12}
                                />
                                <DatePicker
                                    name="Covid Date"
                                    label="Covid Case Date"
                                    value={caseDate}
                                    handleChange={(e) => { setCaseDate(e.target.value) }}
                                    show={[2024, 2024, 2025, 2026, 2027, 2028].indexOf(LeadSubStatus) !== -1}
                                    disabled={false}
                                    className='covidCaseDate'
                                />
                            </>
                        }


                        {Array.isArray(properties) &&
                            <>

                                {properties.map((property) => {
                                    return getInputByProperty(property);
                                })}

                            </>
                        }
                        {
                            IsParentLeadSelected && FOSCity && [2, 115, 7, 1000].indexOf(productId) > -1 && IsAppointmentAvailable === false && User && User.RoleId === 13 &&
                            <div>
                                <h3>FOS eligible lead:</h3>
                                <p className="PitchText">Have you pitched FOS on this lead yet?</p>
                                <input type="checkbox" id="1" value="Yes" onClick={() => { getSelectItemThat("1") }} /> Yes
                                <input type="checkbox" id="2" value="No" onClick={() => { getSelectItemThat("2") }} /> No
                            </div>
                        }
                        <Grid item sm={12} md={12} xs={12}>
                            {
                                isSMEInterestedLead() &&

                                <Textarea
                                    name="comment"
                                    minRows={4}
                                    placeholder="Enter your comments here…"
                                    value={comment}
                                    onChange={(e) => setComment(e.target.value)}
                                />
                            }
                            {
                                isSMEInterestedLead() &&
                                <span className="TextCount">{textCount}/150</span>
                            }
                        </Grid>
                        <p>Lead Id</p>

                        <Grid item sm={12} md={12} xs={12}>
                            <input
                                type="checkbox" name="checkAll"
                                onChange={(e) => handleChange(e, true)}
                                checked={allChecked}
                                disabled={IsRejectAllDisabled}
                            />
                            <label>Reject all Case</label>
                        </Grid>

                        {AllLeads.map((gleads) => (
                            <Grid item sm={12} md={12} xs={12} key={gleads.LeadID}>
                                <Fragment>
                                    <input type="checkbox"
                                        onChange={(e) => handleChange(e, true)}
                                        checked={gleads.Selected}
                                        id={gleads.LeadID}
                                        name={gleads.LeadID}
                                    />
                                    <label htmlFor="leadids">({gleads.R_Number}) - {gleads.LeadID}</label>
                                </Fragment>
                            </Grid>
                        ))}

                    </Grid>
                    <div className="text-center">
                        <button onClick={() => ValidateRejection()} className="rejectLeadBtn">REJECT</button>
                    </div>

                </div>

                }
            </ModalPopup>

            {/* CrossSell Modal */}
            {showCrossSellModal &&
                <ErrorBoundary name="CrossSellModal">
                    <CrossSellModal
                        open={showCrossSellModal}
                        handleClose={() => setShowCrossSellModal(false)}
                        selectedLeadId={selectedLeadId}
                    />
                </ErrorBoundary>
            }
        </>
    )
}