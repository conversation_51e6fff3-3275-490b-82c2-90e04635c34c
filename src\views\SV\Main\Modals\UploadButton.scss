/* Upload Button Styles */
.upload-section {
    margin-top: 12px;
}

.upload-label {
    color: black !important;
    font-size: 12px !important;
    font-weight: 500 !important;
}

.upload-button-container {
    display: block;
    margin-top: 6px;
}

.compact-upload-btn {
    min-height: 32px !important;
    padding: 6px 12px !important;
    font-size: 12px !important;
    font-weight: 500 !important;
    border-color: #2196F3 !important;
    color: #2196F3 !important;
    border-radius: 6px !important;
    text-transform: none !important;
    
    &:hover {
        border-color: #1976D2 !important;
        background-color: #f0f7ff !important;
    }
    
    .upload-icon {
        font-size: 14px;
        margin-right: 4px;
    }
}

.uploaded-file-container {
    margin-top: 8px;
    padding: 6px 8px;
    background-color: #f0f7ff;
    border-radius: 4px;
    border: 1px solid #e3f2fd;
}

.uploaded-file-label {
    color: #1976D2 !important;
    font-size: 11px !important;
    font-weight: 500 !important;
}

.uploaded-file-name {
    font-size: 12px !important;
    color: #424242 !important;
    word-break: break-all;
    margin-top: 2px !important;
}

/* Responsive Design */
@media (max-width: 768px) {
    .upload-section {
        margin-top: 10px;
    }
    
    .compact-upload-btn {
        min-height: 30px !important;
        padding: 5px 10px !important;
        font-size: 11px !important;
    }
    
    .uploaded-file-container {
        padding: 5px 6px;
    }
    
    .uploaded-file-name {
        font-size: 11px !important;
    }
}

/* Focus states for accessibility */
.compact-upload-btn:focus {
    outline: 2px solid #2196F3;
    outline-offset: 2px;
}

/* Animation for file upload */
.uploaded-file-container {
    animation: slideInUp 0.3s ease-out;
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Hidden file input */
.hidden-file-input {
    display: none !important;
}

/* Button text styling */
.upload-button-text {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* Hover effect for the entire upload section */
.upload-section:hover .compact-upload-btn {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(33, 150, 243, 0.2);
    transition: all 0.2s ease;
}

/* Success state for uploaded file */
.uploaded-file-container.success {
    background-color: #e8f5e8;
    border-color: #4caf50;
}

.uploaded-file-container.success .uploaded-file-label {
    color: #2e7d32 !important;
}

/* Error state */
.upload-error {
    margin-top: 4px;
    padding: 4px 8px;
    background-color: #ffebee;
    border: 1px solid #ffcdd2;
    border-radius: 4px;
    color: #c62828;
    font-size: 11px;
}

/* Loading state */
.upload-loading {
    .compact-upload-btn {
        opacity: 0.6;
        pointer-events: none;
    }
}

/* File type icons */
.file-type-icon {
    margin-right: 4px;
    font-size: 12px;
}

.file-type-pdf::before {
    content: "📄";
}

.file-type-image::before {
    content: "🖼️";
}

.file-type-doc::before {
    content: "📝";
}

.file-type-default::before {
    content: "📎";
}
