/* Upload Button Design Options */

/* Option 1: Compact Modern Button */
.upload-option-1 {
    .upload-section {
        margin-top: 8px;
    }

    .upload-label {
        color: #424242 !important;
        font-size: 11px !important;
        font-weight: 600 !important;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .compact-upload-btn {
        min-height: 28px !important;
        padding: 4px 10px !important;
        font-size: 11px !important;
        font-weight: 600 !important;
        border-color: #4CAF50 !important;
        color: #4CAF50 !important;
        border-radius: 20px !important;
        text-transform: none !important;

        &:hover {
            border-color: #388E3C !important;
            background-color: #f1f8e9 !important;
            transform: translateY(-1px);
        }

        .upload-icon {
            font-size: 12px;
        }
    }
}

/* Option 2: Gradient But<PERSON> */
.upload-option-2 {
    .upload-section {
        margin-top: 10px;
    }

    .upload-label {
        color: #333 !important;
        font-size: 12px !important;
        font-weight: 500 !important;
    }

    .compact-upload-btn {
        min-height: 32px !important;
        padding: 6px 16px !important;
        font-size: 12px !important;
        font-weight: 500 !important;
        background: linear-gradient(45deg, #FF6B6B, #4ECDC4) !important;
        color: white !important;
        border: none !important;
        border-radius: 8px !important;
        text-transform: none !important;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1) !important;

        &:hover {
            background: linear-gradient(45deg, #FF5252, #26C6DA) !important;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.2) !important;
        }

        .upload-icon {
            font-size: 14px;
        }
    }
}

/* Option 3: Minimal Flat Design */
.upload-option-3 {
    .upload-section {
        margin-top: 8px;
        padding: 8px;
        background: #fafafa;
        border-radius: 6px;
        border: 1px dashed #ddd;
    }

    .upload-label {
        color: #666 !important;
        font-size: 11px !important;
        font-weight: 400 !important;
    }

    .compact-upload-btn {
        min-height: 30px !important;
        padding: 5px 12px !important;
        font-size: 11px !important;
        font-weight: 500 !important;
        background: #fff !important;
        color: #666 !important;
        border: 1px solid #ddd !important;
        border-radius: 4px !important;
        text-transform: none !important;

        &:hover {
            background: #f5f5f5 !important;
            border-color: #bbb !important;
        }

        .upload-icon {
            font-size: 12px;
        }
    }
}

/* Option 4: Card Style */
.upload-option-4 {
    .upload-section {
        margin-top: 10px;
        padding: 12px;
        background: #fff;
        border: 1px solid #e0e0e0;
        border-radius: 8px;
        box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    }

    .upload-label {
        color: #333 !important;
        font-size: 12px !important;
        font-weight: 500 !important;
        margin-bottom: 8px;
        display: block;
    }

    .compact-upload-btn {
        min-height: 36px !important;
        padding: 8px 16px !important;
        font-size: 12px !important;
        font-weight: 500 !important;
        background: #f8f9fa !important;
        color: #495057 !important;
        border: 2px dashed #dee2e6 !important;
        border-radius: 6px !important;
        text-transform: none !important;
        width: 100%;

        &:hover {
            background: #e9ecef !important;
            border-color: #adb5bd !important;
        }

        .upload-icon {
            font-size: 16px;
        }
    }
}

/* Option 5: Icon-First Design */
.upload-option-5 {
    .upload-section {
        margin-top: 8px;
    }

    .upload-label {
        color: #555 !important;
        font-size: 11px !important;
        font-weight: 500 !important;
    }

    .compact-upload-btn {
        min-height: 30px !important;
        padding: 6px 8px !important;
        font-size: 11px !important;
        font-weight: 500 !important;
        background: #fff !important;
        color: #FF9800 !important;
        border: 1px solid #FF9800 !important;
        border-radius: 50px !important;
        text-transform: none !important;
        min-width: 100px;

        &:hover {
            background: #FF9800 !important;
            color: white !important;
        }

        .upload-icon {
            font-size: 14px;
            margin-right: 6px;
        }
    }
}

.uploaded-file-container {
    margin-top: 8px;
    padding: 6px 8px;
    background-color: #f0f7ff;
    border-radius: 4px;
    border: 1px solid #e3f2fd;
}

.uploaded-file-label {
    color: #1976D2 !important;
    font-size: 11px !important;
    font-weight: 500 !important;
}

.uploaded-file-name {
    font-size: 12px !important;
    color: #424242 !important;
    word-break: break-all;
    margin-top: 2px !important;
}

/* Responsive Design */
@media (max-width: 768px) {
    .upload-section {
        margin-top: 10px;
    }
    
    .compact-upload-btn {
        min-height: 30px !important;
        padding: 5px 10px !important;
        font-size: 11px !important;
    }
    
    .uploaded-file-container {
        padding: 5px 6px;
    }
    
    .uploaded-file-name {
        font-size: 11px !important;
    }
}

/* Focus states for accessibility */
.compact-upload-btn:focus {
    outline: 2px solid #2196F3;
    outline-offset: 2px;
}

/* Animation for file upload */
.uploaded-file-container {
    animation: slideInUp 0.3s ease-out;
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Hidden file input */
.hidden-file-input {
    display: none !important;
}

/* Button text styling */
.upload-button-text {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* Hover effect for the entire upload section */
.upload-section:hover .compact-upload-btn {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(33, 150, 243, 0.2);
    transition: all 0.2s ease;
}

/* Success state for uploaded file */
.uploaded-file-container.success {
    background-color: #e8f5e8;
    border-color: #4caf50;
}

.uploaded-file-container.success .uploaded-file-label {
    color: #2e7d32 !important;
}

/* Error state */
.upload-error {
    margin-top: 4px;
    padding: 4px 8px;
    background-color: #ffebee;
    border: 1px solid #ffcdd2;
    border-radius: 4px;
    color: #c62828;
    font-size: 11px;
}

/* Loading state */
.upload-loading {
    .compact-upload-btn {
        opacity: 0.6;
        pointer-events: none;
    }
}

/* File type icons */
.file-type-icon {
    margin-right: 4px;
    font-size: 12px;
}

.file-type-pdf::before {
    content: "📄";
}

.file-type-image::before {
    content: "🖼️";
}

.file-type-doc::before {
    content: "📝";
}

.file-type-default::before {
    content: "📎";
}
