/* Upload Button Design Options */

/* Option 1: Full Width Modern Upload Area */
.upload-option-1 {
    .upload-section {
        margin-top: 12px;
        width: 100%;
    }

    .upload-label {
        color: #424242 !important;
        font-size: 12px !important;
        font-weight: 500 !important;
        margin-bottom: 8px;
        display: block;
    }

    .upload-button-container {
        width: 100%;
        display: block;
    }

    .compact-upload-btn {
        width: 100% !important;
        min-height: 44px !important;
        padding: 12px 16px !important;
        font-size: 13px !important;
        font-weight: 500 !important;
        border: 2px dashed #2196F3 !important;
        color: #2196F3 !important;
        background: #f8f9ff !important;
        border-radius: 8px !important;
        text-transform: none !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        gap: 8px !important;
        transition: all 0.3s ease !important;

        &:hover {
            border-color: #1976D2 !important;
            background: #e3f2fd !important;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(33, 150, 243, 0.2) !important;
        }

        .upload-icon {
            font-size: 18px;
        }
    }

    .uploaded-file-container {
        margin-top: 12px;
        padding: 12px 16px;
        background: #e8f5e8;
        border: 1px solid #4caf50;
        border-radius: 8px;
        width: 100%;
        box-sizing: border-box;

        .uploaded-file-label {
            color: #2e7d32 !important;
            font-size: 12px !important;
            font-weight: 500 !important;
            display: flex;
            align-items: center;
            gap: 6px;
        }
    }
}

/* Option 2: Full Width Drag & Drop Style */
.upload-option-2 {
    .upload-section {
        margin-top: 12px;
        width: 100%;
    }

    .upload-label {
        color: #333 !important;
        font-size: 12px !important;
        font-weight: 500 !important;
        margin-bottom: 8px;
        display: block;
    }

    .upload-button-container {
        width: 100%;
        display: block;
    }

    .compact-upload-btn {
        width: 100% !important;
        min-height: 60px !important;
        padding: 16px !important;
        font-size: 14px !important;
        font-weight: 500 !important;
        background: #fafafa !important;
        color: #666 !important;
        border: 2px dashed #ddd !important;
        border-radius: 12px !important;
        text-transform: none !important;
        display: flex !important;
        flex-direction: column !important;
        align-items: center !important;
        justify-content: center !important;
        gap: 6px !important;
        transition: all 0.3s ease !important;

        &:hover {
            background: #f0f0f0 !important;
            border-color: #999 !important;
            transform: scale(1.02);
        }

        .upload-icon {
            font-size: 24px;
        }

        .upload-text {
            font-size: 12px;
            color: #999;
            margin-top: 4px;
        }
    }

    .uploaded-file-container {
        margin-top: 12px;
        padding: 12px 16px;
        background: #fff;
        border: 1px solid #e0e0e0;
        border-radius: 8px;
        width: 100%;
        box-sizing: border-box;

        .uploaded-file-label {
            color: #333 !important;
            font-size: 12px !important;
            font-weight: 500 !important;
            display: flex;
            align-items: center;
            gap: 6px;
        }
    }
}

/* Option 3: Full Width Gradient Style */
.upload-option-3 {
    .upload-section {
        margin-top: 12px;
        width: 100%;
    }

    .upload-label {
        color: #333 !important;
        font-size: 12px !important;
        font-weight: 500 !important;
        margin-bottom: 8px;
        display: block;
    }

    .upload-button-container {
        width: 100%;
        display: block;
    }

    .compact-upload-btn {
        width: 100% !important;
        min-height: 48px !important;
        padding: 12px 20px !important;
        font-size: 13px !important;
        font-weight: 600 !important;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
        color: white !important;
        border: none !important;
        border-radius: 10px !important;
        text-transform: none !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        gap: 10px !important;
        box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3) !important;
        transition: all 0.3s ease !important;

        &:hover {
            background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%) !important;
            transform: translateY(-3px);
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4) !important;
        }

        .upload-icon {
            font-size: 18px;
        }
    }

    .uploaded-file-container {
        margin-top: 12px;
        padding: 12px 16px;
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        color: white;
        border-radius: 8px;
        width: 100%;
        box-sizing: border-box;

        .uploaded-file-label {
            color: white !important;
            font-size: 12px !important;
            font-weight: 500 !important;
            display: flex;
            align-items: center;
            gap: 6px;
        }
    }
}

/* Option 4: Card Style */
.upload-option-4 {
    .upload-section {
        margin-top: 10px;
        padding: 12px;
        background: #fff;
        border: 1px solid #e0e0e0;
        border-radius: 8px;
        box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    }

    .upload-label {
        color: #333 !important;
        font-size: 12px !important;
        font-weight: 500 !important;
        margin-bottom: 8px;
        display: block;
    }

    .compact-upload-btn {
        min-height: 36px !important;
        padding: 8px 16px !important;
        font-size: 12px !important;
        font-weight: 500 !important;
        background: #f8f9fa !important;
        color: #495057 !important;
        border: 2px dashed #dee2e6 !important;
        border-radius: 6px !important;
        text-transform: none !important;
        width: 100%;

        &:hover {
            background: #e9ecef !important;
            border-color: #adb5bd !important;
        }

        .upload-icon {
            font-size: 16px;
        }
    }
}

/* Option 5: Icon-First Design */
.upload-option-5 {
    .upload-section {
        margin-top: 8px;
    }

    .upload-label {
        color: #555 !important;
        font-size: 11px !important;
        font-weight: 500 !important;
    }

    .compact-upload-btn {
        min-height: 30px !important;
        padding: 6px 8px !important;
        font-size: 11px !important;
        font-weight: 500 !important;
        background: #fff !important;
        color: #FF9800 !important;
        border: 1px solid #FF9800 !important;
        border-radius: 50px !important;
        text-transform: none !important;
        min-width: 100px;

        &:hover {
            background: #FF9800 !important;
            color: white !important;
        }

        .upload-icon {
            font-size: 14px;
            margin-right: 6px;
        }
    }
}

.uploaded-file-container {
    margin-top: 8px;
    padding: 6px 8px;
    background-color: #f0f7ff;
    border-radius: 4px;
    border: 1px solid #e3f2fd;
}

.uploaded-file-label {
    color: #1976D2 !important;
    font-size: 11px !important;
    font-weight: 500 !important;
}

.uploaded-file-name {
    font-size: 12px !important;
    color: #424242 !important;
    word-break: break-all;
    margin-top: 2px !important;
}

/* Responsive Design */
@media (max-width: 768px) {
    .upload-section {
        margin-top: 10px;
    }
    
    .compact-upload-btn {
        min-height: 30px !important;
        padding: 5px 10px !important;
        font-size: 11px !important;
    }
    
    .uploaded-file-container {
        padding: 5px 6px;
    }
    
    .uploaded-file-name {
        font-size: 11px !important;
    }
}

/* Focus states for accessibility */
.compact-upload-btn:focus {
    outline: 2px solid #2196F3;
    outline-offset: 2px;
}

/* Animation for file upload */
.uploaded-file-container {
    animation: slideInUp 0.3s ease-out;
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Hidden file input */
.hidden-file-input {
    display: none !important;
}

/* Button text styling */
.upload-button-text {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* Hover effect for the entire upload section */
.upload-section:hover .compact-upload-btn {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(33, 150, 243, 0.2);
    transition: all 0.2s ease;
}

/* Success state for uploaded file */
.uploaded-file-container.success {
    background-color: #e8f5e8;
    border-color: #4caf50;
}

.uploaded-file-container.success .uploaded-file-label {
    color: #2e7d32 !important;
}

/* Error state */
.upload-error {
    margin-top: 4px;
    padding: 4px 8px;
    background-color: #ffebee;
    border: 1px solid #ffcdd2;
    border-radius: 4px;
    color: #c62828;
    font-size: 11px;
}

/* Loading state */
.upload-loading {
    .compact-upload-btn {
        opacity: 0.6;
        pointer-events: none;
    }
}

/* File type icons */
.file-type-icon {
    margin-right: 4px;
    font-size: 12px;
}

.file-type-pdf::before {
    content: "📄";
}

.file-type-image::before {
    content: "🖼️";
}

.file-type-doc::before {
    content: "📝";
}

.file-type-default::before {
    content: "📎";
}
